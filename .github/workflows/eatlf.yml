name: Build eatlf 2.4

on:
  release:
    types: [published]

jobs:
  build:
    env:
      working-directory: ./src

    runs-on: ubuntu-latest

    steps:
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
          extensions: apcu, bcmath, gd, soap, intl, zip, xsl, opcache, pdo_mysql, mcrypt
          tools: composer:2.2
          coverage: none

      - uses: actions/checkout@v3

      - name: Add composer auth
        run: |
          composer config -g github-oauth.github.com ${{ secrets.DEPLOY_TOKEN }}
          composer config -g http-basic.repo.magento.com ${{ secrets.MAGENTO_REPO_USER }} ${{ secrets.MAGENTO_REPO_PASS }}

      - name: Install dependencies
        working-directory: ${{env.working-directory}}
        run: composer install --no-progress --no-dev

      - name: Prepare production files
        working-directory: ${{env.working-directory}}
        run: |
          composer dump-autoload -o
          php bin/magento setup:di:compile
          composer dump-autoload -o --apcu
          php bin/magento setup:static-content:deploy fr_FR en_US -f --theme EatLf/default

      - name: Package artifact
        run: |
          rm ${{env.working-directory}}/app/etc/env.php
          rm -rf ${{env.working-directory}}/pub/media
          rm -rf ${{env.working-directory}}/var
          tar -czf src.tar.gz ${{env.working-directory}}

      - name: Upload release asset
        uses: softprops/action-gh-release@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          files: src.tar.gz
