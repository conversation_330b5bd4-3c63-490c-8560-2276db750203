/data/*
/.idea
src/sftp-config.json
src/pub/media/*
src/app/design/frontend/EatLf/sftp-config.json
src/app/code/EatLf/sftp-config.json
src/app/design/adminhtml/EatLf/sftp-config.json
src/app/design/frontend/Lf/sftp-config.json
/src/.composer/
/src/n98-magerun2-3.0.9.phar

# Docker mkcert certificates
docker/site/cert.crt
docker/site/private.key

# Adin ignore composer generated files
src/.npm
src/app/.htaccess
src/app/autoload.php
src/app/bootstrap.php
src/app/etc/*
!src/app/etc/config.php
src/lib
src/dev
src/update
src/bin
src/.github
src/var
src/generated
src/vendor
src/.composer
src/.ssh
src/phpserver
src/setup
src/.editorconfig
src/.htaccess
src/.htaccess.sample
src/.php-cs-fixer.dist.php
src/.user.ini
src/auth.json.sample
src/CHANGELOG.md
src/COPYING.txt
src/grunt-config.json.sample
src/Gruntfile.js.sample
src/LICENSE.txt
src/LICENSE_AFL.txt
src/nginx.conf.sample
src/package.json.sample
src/SECURITY.md
src/pub/*
!src/pub/.well-known
/src/app/code/Lf/Customer/Model/Config/Source/Newsletters.php
/src/app/code/Lf/Customer/view/adminhtml/layout/sales_order_invoice_view.xml
/src/docs/sequence_system.md
/src/.bash_history
/src/.viminfo
/src/n98-magerun2.phar
/src/old
/Bl_2023-09-11_49613.pdf
/Bl_2025-04-14_1799741.pdf
