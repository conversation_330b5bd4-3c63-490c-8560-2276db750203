# La Famille Cantine
Code source pour le site [https://cantine.lf.fr](https://cantine.lf.fr)

## Développement
### Mise en place des certificats SSL
**⚠ Cette étape est obligatoire avant le premier build de l'image docker/site ⚠**

L'environnement de développement fonctionne exclusivement en https. Afin de générer des certificats ssl valides pour le développement il faut installer sur votre machine le package **mkcert**

Pour ubuntu
```
sudo apt install libnss3-tools
curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64"
chmod +x mkcert-v*-linux-amd64
sudo cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert
```

Pour arch
```
sudo pacman -Syu mkcert
```

Une fois l'installation terminée exécuter la série de commande ci-dessous pour créer le certificat SSL eatlf.local:

```
$ mkcert -install
Created a new local CA 💥
The local CA is now installed in the system trust store! ⚡️
The local CA is now installed in the Firefox trust store (requires browser restart)! 🦊

$ cd docker/site

$ mkcert -key-file private.key -cert-file cert.crt eatlf.local
Created a new certificate valid for the following names 📜
 - "eatlf.local"

The certificate is at "cert.crt" and the key at "private.key" ✅

It will expire on 2 May 2025 🗓
```

Vous pouvez maintenant lancer `docker-compose up`!

### Configuration du host
Ajouter la ligne ci-dessous au fichier /etc/hosts

```********** eatlf.local```

### Fichier env.php

```php
<?php
return [
    'backend' => [
        'frontName' => 'admin'
    ],
    'install' => [
        'date' => 'Fri, 20 Nov 2015 08:11:26 +0000'
    ],
    'crypt' => [
        'key' => 'bb8e168de821d682610ecc5e92467c93'
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'redis',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '1',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '1',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '14400'
        ]
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'db',
                'dbname' => 'magento2',
                'username' => 'magento2',
                'password' => 'magento2',
                'active' => '1',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'driver_options' => [
                    1014 => false
                ]
            ]
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'id_prefix' => '061_',
                'backend' => 'Magento\\Framework\\Cache\\Backend\\Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'database' => '2',
                    'port' => '6379',
                    'compress_data' => '1',
                    'compression_lib' => '',
                    'password' => '',
                    'use_lua' => false,
                    'preload_keys' => [
                        '061_EAV_ENTITY_TYPES',
                        '061_GLOBAL_PLUGIN_LIST',
                        '061_DB_IS_UP_TO_DATE',
                        '061_SYSTEM_DEFAULT'
                    ]
                ]
            ],
            'page_cache' => [
                'id_prefix' => '061_'
            ]
        ],
        'allow_parallel_generation' => false
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1,
        'franchise_product' => 1,
        'vertex' => 1
    ],
    'downloadable_domains' => [
        'lf.local'
    ],
    'remote_storage' => [
        'driver' => 'file'
    ],
    'queue' => [
        'consumers_wait_for_messages' => 1
    ],
    'http_cache_hosts' => [
        [
            'host' => 'varnish'
        ]
    ],
    'lock' => [
        'provider' => 'db'
    ],
    'directories' => [
        'document_root_is_pub' => true
    ],
    'system' => [
        'default' => [
            'catalog' => [
                'search' => [
                    'elasticsearch7_server_hostname' => 'elasticsearch',
                    'elasticsearch7_server_port' => '9200',
                    'elasticsearch7_index_prefix' => 'magento2'
                ]
            ],
            'web' => [
                'unsecure' => [
                    'base_url' => 'https://eatlf.local/'
                ],
                'secure' => [
                    'base_url' => 'https://eatlf.local/'
                ]
            ],
            'system' => [
                'security' => [
                    'max_session_size_admin' => '0'
                ]
            ],
            'smtp' => [
                'general' => ['enabled' => 1],
                'configuration_option' => [
                    'host' => 'mailcatcher',
                    'port' => '1025'
                ]
            ]
        ]
    ]
];
```

### XDebug
Par défaut xdebug est en mode `start_with_request=trigger`. Pour l'activer vous pouvez utiliser l'une de ces extensions:

[Firefox](https://addons.mozilla.org/fr/firefox/addon/xdebug-helper-for-firefox/)

[Chromium](https://chrome.google.com/webstore/detail/xdebug-helper/eadndfjplgieldjbigjakmdgkmoaaaoc)

Pour l'activer en mode cli

```export XDEBUG_SESSION=1```

### Configuration Grunt
Pour configurer Grunt lors de la première utilisation:

- Copier `src/Gruntfile.js.sample` dans `src/Gruntfile.js`
- Copier `src/grunt-config.json.sample` dans `src/grunt-config.json`
- Créer le fichier `src/dev/tools/grunt/configs/local-themes` avec le contenu ci-dessous

```
'use strict';

module.exports = {
    EatLf: {
        area: 'frontend',
        name: 'EatLf/default',
        locale: 'fr_FR',
        files: [
            'css/styles-m',
            'css/styles-l'
        ],
        dsl: 'less'
    },
    backend: {
        area: 'adminhtml',
        name: 'EatLf/default',
        locale: 'fr_FR',
        files: [
            'css/styles-old',
            'css/styles'
        ],
        dsl: 'less'
    }
};
```

On peut ensuite utiliser les commandes :

- `grunt exec:EatLf less:EatLf watch`
- `grunt exec:backend less:backend watch`

### Gestion du cache

Le module magento-cache-clean fait partie des dépendences de dev. Il permet de vider automatiquement les caches concernés par les modifications.

Pour le démarrer, lancer dans le conteneur **fpm** la commande :
`vendor/bin/cache-clean.js --watch`

### Accès aux mails

Les mails de dev sont accessibles à [http://**********0:8025](http://**********0:8025)
