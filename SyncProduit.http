POST https://{{host}}/rest/V1/integration/admin/token
Accept: application/json
Content-Type: application/json

{"username":"{{user}}", "password":"{{password}}"}

> {%
 client.global.set("tockenId", response.body)
%}
###

GET https://{{host}}/rest/V1/products?searchCriteria[pageSize]=1
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{tockenId}}

###

GET https://{{host}}/rest/V1/products?searchCriteria[filterGroups][0][filters][0][field]=updated_at&searchCriteria[filterGroups][0][filters][0][value]=2019-12-12%2010%3A24%3A13&searchCriteria[filterGroups][0][filters][0][conditionType]=gt
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{tockenId}}

###
# get Ids

GET https://{{host}}/rest/V1/products?searchCriteria[pageSize]=&fields=items[id]
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{tockenId}}

#
### get Medias

GET https://{{host}}/rest/V1/products/708/media
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{tockenId}}

###