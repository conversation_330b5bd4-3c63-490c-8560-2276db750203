name: 'cantine-247'

services:
  db:
    image: mysql:8.0.26
    volumes:
      - ./data/mysql:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=magento2
      - MYSQL_DATABASE=magento2
      - MYSQL_USER=magento2
      - MYSQL_PASSWORD=magento2
    networks:
      bridge:
        ipv4_address: **********

  fpm:
    build: docker/fpm
    volumes:
      - ./src:/var/www
      - ~/.bash_history:/root/.bash_history
      - ~/.composer/:/var/www/.composer
      - ~/.ssh/:/var/www/.ssh
    environment:
      PHP_IDE_CONFIG: "serverName=EatLf"
    networks:
      bridge:
        ipv4_address: **********

  site:
    build: docker/site
    volumes:
      - ./src:/var/www
    environment:
      FPM_HOST: fpm:9000
    networks:
      bridge:
        ipv4_address: **********

  mailcatcher:
    image: mailhog/mailhog
    networks:
      bridge:
        ipv4_address: **********0

  redis:
    image: redis:7.2
    networks:
      bridge:
        ipv4_address: **********0

  varnish:
    image: varnish:7.5
    volumes:
      - ./docker/varnish/varnish.vcl:/etc/varnish/default.vcl:ro
    networks:
      bridge:
        ipv4_address: ***********

  elasticsearch:
    image: elasticsearch:7.17.24
    environment:
      - xpack.security.enabled=false
      - discovery.type=single-node
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    cap_add:
      - IPC_LOCK
    networks:
      bridge:
        ipv4_address: **********0

networks:
  bridge:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
