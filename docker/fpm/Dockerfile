FROM php:8.3-fpm-bullseye

RUN curl -sL https://deb.nodesource.com/setup_20.x | bash - \
    && apt update \
    && apt install -y git-core locales nodejs unzip procps \
    && npm install --production -g grunt-cli \
    && rm -rf /var/lib/apt/lists/*

RUN sed -i -e 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen \
    && locale-gen

RUN apt update \
    && apt install -y libicu-dev zlib1g-dev libpng-dev libjpeg-dev libfreetype6-dev libxslt-dev libzip-dev \
    && docker-php-ext-configure gd --with-freetype=/usr/include/ --with-jpeg=/usr/include/ \
    && docker-php-ext-install -j$(nproc) pdo_mysql intl xsl zip opcache soap bcmath sockets gd \
    && apt remove -y libicu-dev zlib1g-dev libpng-dev libjpeg-dev libfreetype6-dev libxslt-dev libzip-dev \
    && rm -rf /var/lib/apt/lists/*

RUN pecl install xdebug && docker-php-ext-enable xdebug

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer --version=2.8.1

RUN curl -o n98-magerun2 https://files.magerun.net/n98-magerun2.phar \
    && chmod +x ./n98-magerun2 \
    && mv ./n98-magerun2 /usr/local/bin/

RUN curl -Lo mhsendmail https://github.com/mailhog/mhsendmail/releases/download/v0.2.0/mhsendmail_linux_amd64 \
    && chmod +x ./mhsendmail \
    && mv ./mhsendmail /usr/local/bin/

RUN usermod -u 1000 www-data

ADD php.ini /usr/local/etc/php

USER www-data:www-data
WORKDIR /var/www
