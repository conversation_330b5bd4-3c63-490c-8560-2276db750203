FROM httpd:2.4-alpine

RUN apk add --update apache-mod-fcgid apache2-ssl \
    && sed -i \
        -e 's/^#\(Include .*httpd-vhosts.conf\)/\1/' \
        -e 's/^#\(LoadModule .*mod_proxy_fcgi\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_proxy\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_proxy_http\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_rewrite\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_ssl\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_expires\.so\)/\1/' \
        -e 's/^#\(LoadModule .*mod_headers\.so\)/\1/' \
        -e 's/^Listen 80/Listen 443\nListen 80\nListen 8080/'\
        conf/httpd.conf

COPY httpd-vhosts.conf conf/extra/httpd-vhosts.conf
COPY cert.crt .
COPY private.key .