#Vhost HTTP
<VirtualHost *:80>
    ProxyPreserveHost On
    ProxyPass / http://varnish:80/
    ProxyPassReverse / http://varnish:80/
</VirtualHost>

#Vhost SSL
<VirtualHost *:443>
    SSLEngine on
    SSLCertificateFile /usr/local/apache2/cert.crt
    SSLCertificateKeyFile /usr/local/apache2/private.key

    # SSL offloading indication for Magento
    RequestHeader set X-Forwarded-Proto https

    ProxyPreserveHost On
    ProxyPass / http://varnish:80/
    ProxyPassReverse / http://varnish:80/
</VirtualHost>

#Vhost Magento
<VirtualHost *:8080>
    <FilesMatch \.php$>
    	SetHandler "proxy:fcgi://${FPM_HOST}"
    </FilesMatch>

    DocumentRoot /var/www/pub

    <Directory /var/www/pub>
        # enable the .htaccess rewrites
        AllowOverride All
        Require all granted
    </Directory>

    Header always edit Set-<PERSON><PERSON> (PHPSESSID.*|admin.*|edenred_state.*|edenred_nonce.*) "$1; SameSite=None"

    TimeOut 600
    ProxyTimeout 600
</VirtualHost>