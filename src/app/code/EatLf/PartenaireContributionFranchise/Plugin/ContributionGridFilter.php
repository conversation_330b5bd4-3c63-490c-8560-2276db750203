<?php
declare(strict_types=1);

namespace EatLf\PartenaireContributionFranchise\Plugin;

use EatLf\PartenaireContribution\Model\ResourceModel\Contribution\Grid\Collection;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory;

class ContributionGridFilter
{
    /**
     * @var \Magento\Backend\Model\Auth\Session
     */
    private Session $session;

    /**
     * @param \Magento\Backend\Model\Auth\Session $session
     */
    public function __construct(Session $session)
    {
        $this->session = $session;
    }

    /**
     * Filter contribution grid access
     *
     * @param \Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory $subject
     * @param \Magento\Framework\Data\Collection $result
     * @param string $requestName
     *
     * @return \Magento\Framework\Data\Collection
     */
    public function afterGetReport(CollectionFactory $subject, $result, $requestName)
    {
        $adminUser = $this->session->getUser();

        if ($result instanceof Collection && !empty($adminUser->getFranchiseId())) {
            $result->addFieldToFilter('franchise_id', $adminUser->getFranchiseId());
        }

        return $result;
    }
}
