<?php
declare(strict_types=1);

namespace EatLf\PartenaireContributionFranchise\Plugin;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use Lf\SalesFranchiseFilter\Model\AccessFilter;

class ContributionRepositoryFilter
{
    /**
     * @var \EatLf\Partenaire\Api\PartenaireRepositoryInterface
     */
    private PartenaireRepositoryInterface $partenaireRepository;
    /**
     * @var \Lf\SalesFranchiseFilter\Model\AccessFilter
     */
    private AccessFilter $accessFilter;

    /**
     * @param \EatLf\Partenaire\Api\PartenaireRepositoryInterface $partenaireRepository
     * @param \Lf\SalesFranchiseFilter\Model\AccessFilter $accessFilter
     */
    public function __construct(
        PartenaireRepositoryInterface $partenaireRepository,
        AccessFilter $accessFilter
    ) {
        $this->partenaireRepository = $partenaireRepository;
        $this->accessFilter = $accessFilter;
    }

    /**
     * Check contribution object access rights.
     *
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $subject
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterface $result
     * @param string $id
     *
     * @return mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterGetById(ContributionRepositoryInterface $subject, $result, $id)
    {
        $partenaire = $this->partenaireRepository->getById($result->getPartenaireId());
        $this->accessFilter->checkEntityAccess($partenaire->getFranchiseId());

        return $result;
    }
}
