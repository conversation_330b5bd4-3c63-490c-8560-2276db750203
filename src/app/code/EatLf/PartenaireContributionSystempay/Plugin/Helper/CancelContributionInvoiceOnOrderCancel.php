<?php
declare(strict_types=1);

namespace EatLf\PartenaireContributionSystempay\Plugin\Helper;

use EatLf\PartenaireContribution\Model\InvoiceEventService;
use EatLf\PartenaireContribution\Model\SendInvoiceEvent;
use Lyranetwork\Systempay\Helper\Payment;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\RefundInvoiceInterface;
use Magento\Sales\Model\Order;

class CancelContributionInvoiceOnOrderCancel
{
    private RefundInvoiceInterface $refundInvoice;

    private OrderRepositoryInterface $orderRepository;

    private SendInvoiceEvent $sendInvoiceEvent;

    private InvoiceEventService $invoiceEventService;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        RefundInvoiceInterface $refundInvoice,
        SendInvoiceEvent $sendInvoiceEvent,
        InvoiceEventService $invoiceEventService
    ) {
        $this->refundInvoice = $refundInvoice;
        $this->orderRepository = $orderRepository;
        $this->sendInvoiceEvent = $sendInvoiceEvent;
        $this->invoiceEventService = $invoiceEventService;
    }

    public function beforeCancelOrder(Payment $paymentHelper, Order $order)
    {
        if (!$order->getData('contribution_id')) {
            return null;
        }

        $this->fixOrderItemsArray($order);

        foreach ($order->getInvoiceCollection() as $invoice) {
            if ($invoice->getData('is_contribution') == '1') {
                $this->refundInvoice->execute($invoice->getEntityId());

                $this->sendCancelInvoiceEvent($invoice);
            }
        }

        return null;
    }

    // The order items array is indexed by position (0,1,2...) but the refundInvoice
    // expects to find data indexed by item id. This is a strange mismatch that is very probably
    // a core bug.
    //
    // This method converts fixed indices into item id indices.
    private function fixOrderItemsArray(Order $order): void
    {
        $order = $this->orderRepository->get($order->getEntityId());

        $orderItems = [];

        foreach ($order->getAllItems() as $item) {
            $orderItems[$item->getItemId()] = $item;
        }

        $order->setItems($orderItems);
    }

    private function sendCancelInvoiceEvent(mixed $invoice): void
    {
        $cancelEvent = $this->invoiceEventService->createCancelInvoiceEvent($invoice->getIncrementId());

        $this->sendInvoiceEvent->execute($cancelEvent);
    }
}
