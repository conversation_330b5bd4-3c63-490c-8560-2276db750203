<?php
declare(strict_types=1);

namespace EatLf\PartenaireContributionSystempay\Plugin\Model\Method;

use Lf\Systempay\Rewrite\Model\Method\Standard;

class StandardTokenContribution
{
    /**
     * Activate contribution deduction on quote grand total.
     *
     * @param \Lf\Systempay\Rewrite\Model\Method\Standard $subject
     * @param \Magento\Quote\Model\Quote $quote
     *
     * @return null
     */
    public function beforeGetRestTokenBaseRequest(
        Standard $subject,
        $quote
    ) {
        $quote->setData('include_contribution', true);
        return null;
    }
}