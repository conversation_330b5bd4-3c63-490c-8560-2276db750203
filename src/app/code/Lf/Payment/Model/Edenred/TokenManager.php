<?php
declare(strict_types=1);

namespace Lf\Payment\Model\Edenred;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Service to manage Edenred tokens stored in customer attributes
 */
class TokenManager
{
    /**
     * Customer attribute IDs for Edenred tokens
     */
    private const EDENRED_ACCESS_TOKEN_ATTRIBUTE_ID = 217;
    private const EDENRED_REFRESH_TOKEN_ATTRIBUTE_ID = 215;

    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger
    ) {
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
    }

    /**
     * Remove Edenred tokens for a customer
     *
     * @param int $customerId
     * @return bool
     */
    public function removeEdenredTokens(int $customerId): bool
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            
            $tokensRemoved = false;
            
            // Remove access token (attribute 217)
            if ($this->removeCustomerAttribute($customer, self::EDENRED_ACCESS_TOKEN_ATTRIBUTE_ID)) {
                $tokensRemoved = true;
            }
            
            // Remove refresh token (attribute 215)
            if ($this->removeCustomerAttribute($customer, self::EDENRED_REFRESH_TOKEN_ATTRIBUTE_ID)) {
                $tokensRemoved = true;
            }
            
            if ($tokensRemoved) {
                $this->customerRepository->save($customer);
                $this->logger->info(
                    'Edenred tokens removed for customer',
                    ['customer_id' => $customerId]
                );
            }
            
            return $tokensRemoved;
            
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                'Customer not found when trying to remove Edenred tokens',
                ['customer_id' => $customerId, 'error' => $e->getMessage()]
            );
            return false;
        } catch (LocalizedException $e) {
            $this->logger->error(
                'Error removing Edenred tokens for customer',
                ['customer_id' => $customerId, 'error' => $e->getMessage()]
            );
            return false;
        }
    }

    /**
     * Check if customer has Edenred tokens
     *
     * @param int $customerId
     * @return bool
     */
    public function hasEdenredTokens(int $customerId): bool
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            
            $accessToken = $this->getCustomerAttributeValue($customer, self::EDENRED_ACCESS_TOKEN_ATTRIBUTE_ID);
            $refreshToken = $this->getCustomerAttributeValue($customer, self::EDENRED_REFRESH_TOKEN_ATTRIBUTE_ID);
            
            return !empty($accessToken) || !empty($refreshToken);
            
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                'Customer not found when checking Edenred tokens',
                ['customer_id' => $customerId, 'error' => $e->getMessage()]
            );
            return false;
        }
    }

    /**
     * Remove a specific customer attribute by ID
     *
     * @param CustomerInterface $customer
     * @param int $attributeId
     * @return bool
     */
    private function removeCustomerAttribute(CustomerInterface $customer, int $attributeId): bool
    {
        $attributeValue = $this->getCustomerAttributeValue($customer, $attributeId);
        
        if (!empty($attributeValue)) {
            // Set the attribute to null/empty to remove it
            $this->setCustomerAttributeValue($customer, $attributeId, null);
            return true;
        }
        
        return false;
    }

    /**
     * Get customer attribute value by attribute ID
     *
     * @param CustomerInterface $customer
     * @param int $attributeId
     * @return string|null
     */
    private function getCustomerAttributeValue(CustomerInterface $customer, int $attributeId): ?string
    {
        // Note: This is a simplified approach. In a real implementation,
        // you might need to use the EAV system to get attributes by ID
        // For now, we'll use a mapping approach based on known attribute codes
        
        $attributeCode = $this->getAttributeCodeById($attributeId);
        if ($attributeCode) {
            $attribute = $customer->getCustomAttribute($attributeCode);
            return $attribute ? $attribute->getValue() : null;
        }
        
        return null;
    }

    /**
     * Set customer attribute value by attribute ID
     *
     * @param CustomerInterface $customer
     * @param int $attributeId
     * @param string|null $value
     * @return void
     */
    private function setCustomerAttributeValue(CustomerInterface $customer, int $attributeId, ?string $value): void
    {
        $attributeCode = $this->getAttributeCodeById($attributeId);
        if ($attributeCode) {
            $customer->setCustomAttribute($attributeCode, $value);
        }
    }

    /**
     * Map attribute ID to attribute code
     * This mapping should be updated based on the actual attribute codes used
     *
     * @param int $attributeId
     * @return string|null
     */
    private function getAttributeCodeById(int $attributeId): ?string
    {
        $mapping = [
            self::EDENRED_ACCESS_TOKEN_ATTRIBUTE_ID => 'edenred_access_token',
            self::EDENRED_REFRESH_TOKEN_ATTRIBUTE_ID => 'edenred_refresh_token'
        ];
        
        return $mapping[$attributeId] ?? null;
    }
}
