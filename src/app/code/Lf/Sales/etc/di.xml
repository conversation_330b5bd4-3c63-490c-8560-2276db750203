<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <preference for="Magento\Sales\Model\Order\Pdf\Invoice" type="Lf\Sales\Model\Order\Pdf\Invoice"></preference>
    <preference for="Magento\Sales\Block\Order\History" type="Lf\Sales\Block\Order\History"></preference>
    <preference for="Magento\Sales\Block\Order\Info" type="Lf\Sales\Block\Order\Info"></preference>
    <preference for="Magento\Sales\Block\Adminhtml\Order\View" type="Lf\Sales\Block\Adminhtml\Order\View"></preference>
    <preference for="Magento\Sales\Block\Adminhtml\Order\View\Info"
                type="Lf\Sales\Block\Adminhtml\Order\View\Info"></preference>

    <type name="Magento\Sales\Api\OrderManagementInterface">
        <plugin name="lf_sales_lien_franchise_customer" type="Lf\Sales\Plugins\OrderManagementInterfacePlugin"/>
    </type>
    <preference for="Magento\Sales\Ui\Component\Listing\Column\ViewAction"
                type="Lf\Sales\Ui\Component\Listing\Column\ViewAction"></preference>
    <preference for="Magento\Sales\Model\Order\Pdf\Items\Invoice\DefaultInvoice"
                type="Lf\Sales\Model\Order\Pdf\Items\Invoice\DefaultInvoice"></preference>
    <preference for="Magento\Sales\Model\Order\Pdf\Creditmemo" type="Lf\Sales\Model\Order\Pdf\Creditmemo"></preference>
    <preference for="Magento\Sales\Model\Order\Pdf\Items\Creditmemo\DefaultCreditmemo"
                type="Lf\Sales\Model\Order\Pdf\Items\Creditmemo\DefaultCreditmemo"></preference>
    <preference for="Magento\Sales\Model\ResourceModel\Order\Grid\Collection"
                type="Lf\Sales\Model\ResourceModel\Order\Grid\Collection"></preference>
    <preference for="Magento\Sales\Controller\Order\PrintInvoice"
                type="Lf\Sales\Controller\Order\PrintInvoice"></preference>
    <preference for="Magento\Sales\Controller\Order\PrintCreditmemo"
                type="Lf\Sales\Controller\Order\PrintCreditmemo"></preference>
    <preference for="Magento\Sales\Model\Order\Email\Sender\CreditmemoSender"
                type="Lf\Sales\Model\Order\Email\Sender\CreditmemoSender"></preference>
    <preference for="Magento\Sales\Model\ResourceModel\Order\Invoice"
                type="Lf\Sales\Model\ResourceModel\Override\Order\Invoice"></preference>
    <preference for="Magento\Sales\Model\ResourceModel\Order\Creditmemo"
                type="Lf\Sales\Model\ResourceModel\Override\Order\Creditmemo"></preference>
    <preference for="Magento\Sales\Model\ResourceModel\Order\Shipment"
                type="Lf\Sales\Model\ResourceModel\Override\Order\Shipment"></preference>

    <preference for="Magento\SalesRule\Model\Validator" type="Lf\Sales\Model\Validator"/>

    <preference for="Lf\Sales\Api\CustomerOrdersInterface" type="Lf\Sales\Model\CustomerOrders"/>

    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="franchiseIdExtension" type="Lf\Sales\Plugin\OrderRepositorySaveFranchise"/>
    </type>

    <type name="Magento\Sales\Model\Order\Email\Container\Template">
        <plugin name="addFranchiseToEmails" type="Lf\Sales\Plugins\AddFranchiseToEmails"/>
    </type>

    <type name="Magento\Sales\Model\Order\Invoice\Validation\CanRefund">
        <plugin name="lf_sales_allow_invoice_refuns" type="Lf\Sales\Plugin\AllowUnpaidInvoiceRefund"/>
    </type>

    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Grid" type="Magento\Sales\Model\ResourceModel\Grid">
        <arguments>
            <argument name="columns" xsi:type="array">
                <item name="franchise_id" xsi:type="string">sales_order.franchise_id</item>
                <item name="payment_date" xsi:type="string">sales_order.payment_date</item>
                <item name="lf_payments_amount" xsi:type="string">sales_order_payment.lf_payments_amount</item>
                <item name="lf_payments_methods" xsi:type="string">sales_order_payment.lf_payments_methods</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Override the invoice and creditmemo grids to use the 'main_table' filter prefix from
    Magento\Sales\Model\ResourceModel\Order\Grid\Collection -->
    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Invoice\Grid\Collection"
                 type="Magento\Sales\Model\ResourceModel\Order\Grid\Collection"/>

    <virtualType name="Lf\Sales\Model\ResourceModel\Order\Invoice\Grid\Collection"
                 type="Magento\Sales\Model\ResourceModel\Order\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">sales_creditmemo_grid</argument>
            <argument name="resourceModel" xsi:type="string">Magento\Sales\Model\ResourceModel\Order\Creditmemo
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="sales_order_creditmemo_grid_data_source" xsi:type="string">
                    Lf\Sales\Model\ResourceModel\Order\Invoice\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>
    <!-- Fin d'override -->

    <preference for="Lf\Sales\Api\OrderMessageSenderInterface" type="Lf\Sales\Model\OrderMessageSender"/>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="lf_amqp_orders_command" xsi:type="object">
                    Lf\Sales\Console\Command\SendOrderMessagesCommand
                </item>
            </argument>
        </arguments>
    </type>
</config>
