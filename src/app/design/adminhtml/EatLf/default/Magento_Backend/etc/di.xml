<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Framework\App\Config\Storage\WriterInterface" type="Magento\Framework\App\Config\Storage\Writer" />
    <preference for="Magento\Framework\App\Request\PathInfoProcessorInterface" type="Magento\Backend\App\Request\PathInfoProcessor" />
    <preference for="Magento\Backend\Model\Auth\StorageInterface" type="Magento\Backend\Model\Auth\Session" />
    <preference for="Magento\Backend\Model\Auth\Credential\StorageInterface" type="Magento\User\Model\User" />
    <preference for="Magento\Backend\App\ConfigInterface" type="Magento\Backend\App\Config" />
    <preference for="Magento\Backend\Model\UrlInterface" type="Magento\Backend\Model\Url" />
    <preference for="Magento\Backend\Block\Widget\Button\ToolbarInterface" type="Magento\Backend\Block\Widget\Button\Toolbar" />
    <preference for="Magento\Backend\Service\V1\ModuleServiceInterface" type="Magento\Backend\Service\V1\ModuleService" />
    <type name="Magento\Backend\Block\Widget\Button\ButtonList" shared="false" />
    <type name="Magento\Framework\App\AreaList">
        <arguments>
            <argument name="areas" xsi:type="array">
                <item name="adminhtml" xsi:type="array">
                    <item name="frontNameResolver" xsi:type="string">Magento\Backend\App\Area\FrontNameResolver</item>
                    <item name="router" xsi:type="string">admin</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Model\Menu\Config">
        <arguments>
            <argument name="menuDirector" xsi:type="object">Magento\Backend\Model\Menu\Director\Director</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Helper\Data">
        <arguments>
            <argument name="backendUrl" xsi:type="object">Magento\Backend\Model\UrlInterface\Proxy</argument>
            <argument name="auth" xsi:type="object">Magento\Backend\Model\Auth\Proxy</argument>
            <argument name="locale" xsi:type="object">Magento\Backend\Model\Locale\Resolver\Proxy</argument>
        </arguments>
    </type>
	<type name="Magento\Theme\Model\View\Design">
        <arguments>
             <argument name="themes" xsi:type="array">
                 <item name="adminhtml" xsi:type="string">EatLf/default</item> <!-- Example: "Magento/backend" -->
             </argument>
         </arguments>
    </type>
    <type name="Magento\Framework\App\Router\NoRouteHandlerList">
        <arguments>
            <argument name="handlerClassesList" xsi:type="array">
                <item name="backend" xsi:type="array">
                    <item name="class" xsi:type="string">Magento\Backend\App\Router\NoRouteHandler</item>
                    <item name="sortOrder" xsi:type="string">10</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Model\Widget\Grid\Row\UrlGenerator">
        <arguments>
            <argument name="backendUrl" xsi:type="object">Magento\Backend\Model\UrlInterface\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Model\Url\ScopeResolver">
        <arguments>
            <argument name="areaCode" xsi:type="string">adminhtml</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Model\Url">
        <arguments>
            <argument name="scopeResolver" xsi:type="object">Magento\Backend\Model\Url\ScopeResolver</argument>
            <argument name="authSession" xsi:type="object">Magento\Backend\Model\Auth\Session\Proxy</argument>
            <argument name="formKey" xsi:type="object">Magento\Framework\Data\Form\FormKey\Proxy</argument>
            <argument name="scopeType" xsi:type="const">Magento\Store\Model\ScopeInterface::SCOPE_STORE</argument>
            <argument name="backendHelper" xsi:type="object">Magento\Backend\Helper\Data\Proxy</argument>
        </arguments>
    </type>
    <preference for="Magento\Framework\Authorization\PolicyInterface" type="Magento\Framework\Authorization\Policy\Acl"/>
    <preference for="Magento\Framework\Acl\AclResource\ProviderInterface" type="Magento\Framework\Acl\AclResource\Provider"/>
    <type name="Magento\Framework\Acl\AclResource\Config\Reader\Filesystem">
        <arguments>
            <argument name="converter" xsi:type="object">Magento\Framework\Acl\AclResource\Config\Converter\Dom</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\Acl\AclResource\Provider">
        <arguments>
            <argument name="configReader" xsi:type="object">Magento\Framework\Acl\AclResource\Config\Reader\Filesystem</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\Acl\Builder">
        <arguments>
            <argument name="resourceLoader" xsi:type="object">Magento\Framework\Acl\Loader\ResourceLoader</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\Acl\RootResource">
        <arguments>
            <argument name="identifier" xsi:type="string">Magento_Backend::all</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Controller\Adminhtml\Index\GlobalSearch">
        <arguments>
            <argument name="searchModules" xsi:type="array">
                <item name="customers" xsi:type="array">
                    <item name="class" xsi:type="string">Magento\Backend\Model\Search\Customer</item>
                    <item name="acl" xsi:type="string">Magento_Customer::customer</item>
                </item>
                <item name="sales" xsi:type="array">
                    <item name="class" xsi:type="string">Magento\Backend\Model\Search\Order</item>
                    <item name="acl" xsi:type="string">Magento_Sales::sales</item>
                </item>
            </argument>
        </arguments>
    </type>
    <virtualType name="Magento\Backend\Model\Auth\Session\Storage" type="Magento\Framework\Session\Storage">
        <arguments>
            <argument name="namespace" xsi:type="string">admin</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Backend\Model\Auth\Session">
        <arguments>
            <argument name="aclBuilder" xsi:type="object">Magento\Framework\Acl\Builder\Proxy</argument>
            <argument name="storage" xsi:type="object">Magento\Backend\Model\Auth\Session\Storage</argument>
        </arguments>
    </type>
    <virtualType name="Magento\Backend\Model\Session\Storage" type="Magento\Framework\Session\Storage">
        <arguments>
            <argument name="namespace" xsi:type="string">adminhtml</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Backend\Model\Session">
        <arguments>
            <argument name="storage" xsi:type="object">Magento\Backend\Model\Session\Storage</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\App\Request\DataPersistor">
        <arguments>
            <argument name="session" xsi:type="object">Magento\Backend\Model\Session</argument>
        </arguments>
    </type>
    <virtualType name="Magento\Backend\Model\Session\Quote\Storage" type="Magento\Framework\Session\Storage">
        <arguments>
            <argument name="namespace" xsi:type="string">adminhtml_quote</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Backend\Model\Session\Quote">
        <arguments>
            <argument name="storage" xsi:type="object">Magento\Backend\Model\Session\Quote\Storage</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="cacheEnableCommand" xsi:type="object">Magento\Backend\Console\Command\CacheEnableCommand</item>
                <item name="cacheDisableCommand" xsi:type="object">Magento\Backend\Console\Command\CacheDisableCommand</item>
                <item name="cacheFlushCommand" xsi:type="object">Magento\Backend\Console\Command\CacheFlushCommand</item>
                <item name="cacheCleanCommand" xsi:type="object">Magento\Backend\Console\Command\CacheCleanCommand</item>
                <item name="cacheStatusCommand" xsi:type="object">Magento\Backend\Console\Command\CacheStatusCommand</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="Magento\Setup\BackendApp" type="Magento\Backend\App\BackendApp">
        <arguments>
            <argument name="cookiePath" xsi:type="string">setup</argument>
            <argument name="startupPage" xsi:type="string">setup</argument>
            <argument name="aclResourceName" xsi:type="string">Magento_Backend::setup_wizard</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Backend\App\BackendAppList">
        <arguments>
            <argument name="backendApps" xsi:type="array">
                <item name="setup" xsi:type="object">Magento\Setup\BackendApp</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\App\Response\HeaderManager">
        <arguments>
            <argument name="headerProviderList" xsi:type="array">
                <item name="x-frame-options" xsi:type="object">Magento\Framework\App\Response\HeaderProvider\XFrameOptions</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Config\Model\Config\TypePool">
        <arguments>
            <argument name="sensitive" xsi:type="array">
                <item name="admin/url/custom" xsi:type="string">1</item>
                <item name="admin/url/custom_path" xsi:type="string">1</item>
                <item name="trans_email/ident_custom1/email" xsi:type="string">1</item>
                <item name="trans_email/ident_custom1/name" xsi:type="string">1</item>
                <item name="trans_email/ident_custom2/email" xsi:type="string">1</item>
                <item name="trans_email/ident_custom2/name" xsi:type="string">1</item>
                <item name="trans_email/ident_general/email" xsi:type="string">1</item>
                <item name="trans_email/ident_general/name" xsi:type="string">1</item>
                <item name="trans_email/ident_sales/email" xsi:type="string">1</item>
                <item name="trans_email/ident_sales/name" xsi:type="string">1</item>
                <item name="trans_email/ident_support/email" xsi:type="string">1</item>
                <item name="trans_email/ident_support/name" xsi:type="string">1</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Block\Menu">
        <arguments>
            <argument name="menuItemChecker" xsi:type="object">Magento\Backend\Block\MenuItemChecker</argument>
            <argument name="anchorRenderer" xsi:type="object">Magento\Backend\Block\AnchorRenderer</argument>
        </arguments>
    </type>
</config>
