.ordermanagement-ordermanagement-index {
    .page-content {
        padding: 10px 20px;
    }

    .page-wrapper {
        width: 100%;
    }

    .page-wrapper {
        background-color: inherit;
    }

    .validation-error {
        color: red;
    }

    .messages {
        .message:last-child {
            margin: 0;
        }
    }

    .card .card-header {
        height: 35px;
    }

    table {
        color: inherit;
    }

    .img-fluid {
        height: 34px;
    }

    .popup-input {
        float: right;
        width: 70px;
        margin-right: 20px;
    }

    .popup-header {
        margin-left: 10px;
    }

    .former-price {
        text-decoration: line-through;
        color: #cc0000;
    }

    .cart-row-total {
        font-weight: bold;
        font-size: 1.1em;
    }

    .cart-totals {
        height: 110px;
        margin-bottom: 0;
        background:#EEE;
        border-width: 1px 0 0 0 !important;
    }

    .ui-state-focus {
        border: 1px solid #fbd850;
        background: #ffffff url(images/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x;
        font-weight: bold;
        color: #eb8f00;
    }

    .franchise {
        textarea {
            min-width: initial;
            width: 190px;
            height: 100px;
        }
    }

    input.qty {
        width: 80px;
    }

    .table-responsive {
        height: 86%;
    }

    .zone-inconnue.form-control {
        background-color: #f0be63;
    }

    .ui-tooltip {
        max-width: 800px;
    }

    .cart-tooltip {
        font-family: Eczar, arial;
        color: #003456;

        .desc {
            font-size: 16px;
            line-height: 20px;
        }

        .details {
            font-size: 14px;
            margin-top: 20px;

            h5 {
                font-family: 'DinProBlack';
                text-transform: uppercase;
                text-align: left;
                margin-bottom: 0;
                font-weight: 700;
                line-height: 1.1;
            }
        }

        .details2 {
            margin-top: 20px;
            display: inline-block;
            vertical-align: bottom;
            font-size: 20px;
            margin-right: 20px;
            text-align: center;
            margin-bottom: 20px;

            h6 {
                font-size: 12px;
                margin-bottom: 0;
                font-family: 'DinProBlack';
                text-transform: uppercase;
                font-weight: 700;
                line-height: 1.1;
            }
        }
    }

    .convives {
        clear: both;

        .convives-input {
            margin-left: 5px;
            width: 50px;
        }
    }

    .modal-content {
        padding: 2rem 3rem;
        border: 0;

        input[type="text"] {
            width: 100%;
        }

        .modal-label {
            font-size: 1rem;
            font-weight: bold;
            margin-right: 15px;
        }

        .devis-errors {
            color: red;
            font-weight: bold;
        }
    }

    .split-shortcut {
        margin-top: 3px;
        margin-left: 5px;
    }

    .cart-column {
        max-height: 95vh;
    }

    .cart-container {
        display: flex;
        flex-direction: column;
        height: calc(100% - 35px);
    }

    .cart-table {
        flex-grow: 1;
        height: initial;
    }

    .ui-datepicker-prev, .ui-datepicker-next {
        line-height: 15px;
    }

    .ui-datepicker .ui-datepicker-title .ui-datepicker-month {
        margin-right: initial;
        width: initial;
    }

    .ui-datepicker table.ui-datepicker-calendar {
        background: inherit;
        border: 0;
        border-collapse: collapse;
        position: relative;
        z-index: 1;
    }

    .ui-datepicker table.ui-datepicker-calendar tr td {
        background: initial;
        border: 0;
        padding: 1px;
    }

    .ui-datepicker table.ui-datepicker-calendar span, .ui-datepicker table.ui-datepicker-calendar a {
        background: transparent;
        border: none;
        color: #2b2b2b;
        font-weight: inherit;
        text-align: center;
        width: initial;
        line-height: 17px;
        font-size: 11px;
    }

    .ui-datepicker table.ui-datepicker-calendar .ui-datepicker-today a {
        border: 0;
        line-height: inherit;
    }

    .ui-datepicker table.ui-datepicker-calendar tr th {
        background: transparent;
        border: 0;
        padding: .7em .3em;
        font-size: 11px;
    }

    .ui-datepicker table.ui-datepicker-calendar .ui-state-active {
        background: #003456;
        color: #fff;
    }

    .ll-skin-latoja .ui-datepicker-calendar .ui-state-hover {
        background: #d9e1e7;
        border-radius: 5px;
    }

    .ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next {
        width: 1.7em;
        height: 2.2em;
    }

    .ui-autocomplete {
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 700px;
    }
}