.preparation-view-index {
  .order-details-container {
    table {
      font-size: 1.1rem;

      th {
        background: #EEE;
        font-weight: bold;
        color: black;
      }

      img {
        height: 55px;
        width: 80px;
      }

      .col-img {
        width: 90px;
      }

      .col-qty {
        text-align: center;
        width: 110px;
      }
    }

    .comment-label {
      font-weight: bold;
      font-size: 18px;
    }
  }

  .order-details-modal {
    .modal-title {
      font-size: 1.4rem;
      margin-right: 6.4rem;
      font-weight: bold;
    }

    .modal-header {
      padding-bottom: 1rem;
      padding-top: 1rem;
    }

    .action-close {
      padding: 1rem;
    }

    .modal-content {
      border: 0;
      padding-top: 15px;
      max-height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }

  .modal-popup {
    pointer-events: none;
  }
}

