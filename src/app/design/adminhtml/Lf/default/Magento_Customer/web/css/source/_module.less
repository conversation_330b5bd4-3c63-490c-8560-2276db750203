// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

.customer_form_areas_address_address_customer_address_update_modal_update_customer_address_form_loader {
    .admin__field {
        .admin__field {
            .admin__field-label {
                background: none;
            }
        }
    }
}

.customer-address-form {

    *,
    *:after,
    *:before {
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }

    address {
        font-style: normal;
    }

    .customer-default-address-wrapper {
        align-items: flex-start;
        display: flex;
        float: left;
        position: relative;
        width: 50%;

        .action-additional {
            margin: 2px 0 0 2px;
        }
    }

    .edit-default-billing-address-button,
    .edit-default-shipping-address-button {
        position: absolute;
    }

    .edit-default-billing-address-button {
        left: 320px;
    }

    .edit-default-shipping-address-button {
        left: 300px;
    }

    .customer_form_areas_address_address_customer_address_listing {
        clear: both;
    }

    .add-new-address-button {
        clear: both;
        float: right;
        margin-bottom: 30px;
        position: relative;
    }

    .address-information {
        float: left;
        margin-bottom: 20px;

        address {
            float: left;

            .address_caption {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 16px;
            }
        }
    }
}

.customer-newsletter-fieldset.admin__fieldset {
    &.multi-website {
        > .admin__field > .admin__field-control {
            width: ~'calc(100% * 0.75 - 30px)';

            table {
                th.subscriber-status {
                    text-align: center;
                }

                td {
                    &.subscriber-status {
                        text-align: center;
                    }

                    select.admin__control-select {
                        width: 100%;
                    }
                }
            }
        }
    }
}
