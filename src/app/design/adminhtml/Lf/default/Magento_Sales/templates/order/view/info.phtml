<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @var \Magento\Sales\Block\Adminhtml\Order\View\Info $block
 */

// @codingStandardsIgnoreFile

$order = $block->getOrder();
$shippingDatas = $block->getShippingDatas();

$orderAdminDate = $block->formatDate(
    $block->getOrderAdminDate($order->getCreatedAt()),
    \IntlDateFormatter::MEDIUM,
    true
);

$orderStoreDate = $block->formatDate(
    $order->getCreatedAt(),
    \IntlDateFormatter::MEDIUM,
    true,
    $block->getTimezoneForStore($order->getStore())
);

$deliveryDate = $block->formatDate(
    $shippingDatas->getShippingDate(),
    \IntlDateFormatter::MEDIUM,
    false,
    $block->getTimezoneForStore($order->getStore())
);

$timeslot = $block->getFormattedTimeslot();

$customerUrl = $block->getCustomerViewUrl();
?>

<section class="admin__page-section order-view-account-information">
    <div class="admin__page-section-title">
        <span class="title"><?= $block->escapeHtml(__('Order & Account Information')) ?></span>
    </div>
    <div class="admin__page-section-content">
        <div class="admin__page-section-item order-information">
            <?php /* Order Information */ ?>
            <?php $confirmationEmailStatusMessage = $order->getEmailSent() ? __('The order confirmation email was sent') : __('The order confirmation email is not sent'); ?>
            <div class="admin__page-section-item-title">
                <span class="title">
                    <?php if ($block->getNoUseOrderLink()): ?>
                        <?= $block->escapeHtml(__('Order # %1', $order->getRealOrderId())) ?> (<span><?= $block->escapeHtml($confirmationEmailStatusMessage) ?></span>)
                    <?php else: ?>
                        <a href="<?= $block->escapeHtml($block->getViewUrl($order->getId())) ?>"><?= $block->escapeHtml(__('Order # %1', $order->getRealOrderId())) ?></a>
                        <span>(<?= $block->escapeHtml($confirmationEmailStatusMessage) ?>)</span>
                    <?php endif; ?>
                </span>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary order-information-table">
                <tr>
                    <th><?= $block->escapeHtml(__('Order Date')) ?></th>
                    <td><?= $block->escapeHtml($orderAdminDate) ?></td>
                </tr>
                <tr>
                    <th><?= $block->escapeHtml(__('Delivery Date')) ?></th>
                    <td><?= $block->escapeHtml($deliveryDate) ?></td>
                </tr>
                <tr>
                    <th><?= $block->escapeHtml(__('Timeslot')) ?></th>
                    <td><?= $block->escapeHtml($timeslot) ?></td>
                </tr>
                <?php if ($orderAdminDate != $orderStoreDate):?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Order Date (%1)', $block->getTimezoneForStore($order->getStore()))) ?></th>
                        <td><?= $block->escapeHtml($orderStoreDate) ?></td>
                    </tr>
                <?php endif;?>
                <tr>
                    <th><?= $block->escapeHtml(__('Order Status')) ?></th>
                    <td><span id="order_status"><?= $block->escapeHtml($order->getStatusLabel()) ?></span></td>
                </tr>
                <?= $block->getChildHtml() ?>
                <?php if ($block->isSingleStoreMode() == false):?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Purchased From')) ?></th>
                        <td><?= $block->escapeHtml($block->getOrderStoreName(), ['br']) ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($order->getRelationChildId()): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Link to the New Order')) ?></th>
                        <td>
                            <a href="<?= $block->escapeHtml($block->getViewUrl($order->getRelationChildId())) ?>">
                                <?= $block->escapeHtml($order->getRelationChildRealId()) ?>
                            </a>
                        </td>
                    </tr>
                <?php endif; ?>
                <?php if ($order->getRelationParentId()): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Link to the Previous Order')) ?></th>
                        <td>
                            <a href="<?= $block->escapeHtml($block->getViewUrl($order->getRelationParentId())) ?>">
                                <?= $block->escapeHtml($order->getRelationParentRealId()) ?>
                            </a>
                        </td>
                    </tr>
                <?php endif; ?>
                <?php if ($order->getRemoteIp() && $block->shouldDisplayCustomerIp()): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Placed from IP')) ?></th>
                        <td><?= $block->escapeHtml($order->getRemoteIp()); echo $order->getXForwardedFor() ? ' (' . $block->escapeHtml($order->getXForwardedFor()) . ')' : ''; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($order->getGlobalCurrencyCode() != $order->getBaseCurrencyCode()): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('%1 / %2 rate:', $order->getGlobalCurrencyCode(), $order->getBaseCurrencyCode())) ?></th>
                        <td><?= $block->escapeHtml($order->getBaseToGlobalRate()) ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($order->getBaseCurrencyCode() != $order->getOrderCurrencyCode()): ?>
                    <tr>
                        <th><?= $block->escapeHtml(__('%1 / %2 rate:', $order->getOrderCurrencyCode(), $order->getBaseCurrencyCode())) ?></th>
                        <th><?= $block->escapeHtml($order->getBaseToOrderRate()) ?></th>
                    </tr>
                <?php endif; ?>
            </table>
            </div>
        </div>

        <div class="admin__page-section-item order-account-information">
            <?php /* Account Information */ ?>
            <div class="admin__page-section-item-title">
                <span class="title"><?= $block->escapeHtml(__('Account Information')) ?></span>
                <div class="actions">
                    <?php if ($customerUrl) : ?>
                        <a href="<?= /* @noEscape */ $customerUrl ?>" target="_blank">
                            <?= $block->escapeHtml(__('Edit Customer')) ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="admin__page-section-item-content">
                <table class="admin__table-secondary order-account-information-table">
                    <tr>
                        <th><?= $block->escapeHtml(__('Customer Name')) ?></th>
                        <td>
                            <?php if ($customerUrl): ?>
                                <a href="<?= $block->escapeUrl($customerUrl) ?>" target="_blank">
                                    <span><?= $block->escapeHtml($order->getCustomerName()) ?></span>
                                </a>
                            <?php else: ?>
                                <?= $block->escapeHtml($order->getCustomerName()) ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th><?= $block->escapeHtml(__('Email')) ?></th>
                        <td><a href="mailto:<?php echo $block->escapeHtml($order->getCustomerEmail()) ?>"><?php echo $block->escapeHtml($order->getCustomerEmail()) ?></a></td>
                    </tr>
                    <?php if ($groupName = $block->getCustomerGroupName()) : ?>
                        <tr>
                            <th><?= $block->escapeHtml(__('Customer Group')) ?></th>
                            <td><?= $block->escapeHtml($groupName) ?></td>
                        </tr>
                    <?php endif; ?>
                    <?php foreach ($block->getCustomerAccountData() as $data):?>
                        <tr>
                            <th><?= $block->escapeHtml($data['label']) ?></th>
                            <td><?= $block->escapeHtml($data['value'], ['br']) ?></td>
                        </tr>
                    <?php endforeach;?>
                    <tr>
                        <th><?= $block->escapeHtml(__('Phone number')) ?></th>
                        <td><?php echo $block->getCustomerTelephone() ?></td>
                    </tr>
                    <?= $block->getChildHtml('extra_customer_info') ?>
                </table>
            </div>
        </div>
    </div>
</section>

<section class="admin__page-section order-addresses">
    <div class="admin__page-section-title">
        <span class="title"><?= $block->escapeHtml(__('Address Information')) ?></span>
    </div>
    <div class="admin__page-section-content">
        <div class="admin__page-section-item order-billing-address">
            <?php /* Billing Address */ ?>
            <div class="admin__page-section-item-title">
                <span class="title"><?= $block->escapeHtml(__('Billing Address')) ?></span>
                <div class="actions"><?= /* @noEscape */ $block->getAddressEditLink($order->getBillingAddress()); ?></div>
            </div>
            <address class="admin__page-section-item-content"><?= /* @noEscape */ $block->getFormattedAddress($order->getBillingAddress()); ?></address>
        </div>
        <?php if (!$block->getOrder()->getIsVirtual()): ?>
            <div class="admin__page-section-item order-shipping-address">
                <?php /* Shipping Address */ ?>
                <div class="admin__page-section-item-title">
                    <span class="title"><?= $block->escapeHtml(__('Shipping Address')) ?></span>
                    <div class="actions"><?= /* @noEscape */ $block->getAddressEditLink($order->getShippingAddress()); ?></div>
                </div>
                <address class="admin__page-section-item-content"><?= /* @noEscape */ $block->getFormattedAddress($order->getShippingAddress()); ?></address>
            </div>
        <?php endif; ?>
    </div>
</section>
