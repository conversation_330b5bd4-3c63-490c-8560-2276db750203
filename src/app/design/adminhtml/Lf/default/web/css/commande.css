@charset "UTF-8";


.h83 {
    height: 83%;
    max-height: 83%;
}
.h83 .card-body  {
    height: 85%;
    max-height: 85%;
}
.h15 {
    height: 15%;
    max-height: 15%;
}
.h33 {
    height: 33%;
    max-height: 33%;
}
.h30 {
    height: 20%;
    max-height: 20%;
}
.h79 {
    height: 79%;
    max-height: 79%;
}
 .f-left {
  float:left;
 }
 .triangle {
  position: absolute;
    margin: 9px 0;
 }

 .f-left.col-xl-3 {
  max-width:23%;
 }
 .split {
  background:#CCC;
  color:black;
  font-weight: bold;
 }

 .badge.small {
  zoom:0.75;
 }
.table-responsive tr:focus,
.table-responsive td:focus,
.table-responsive tr:hover,
.table-responsive tr.selected  {
  background:#f3eae0;
}

.switcher {
      padding: 0.375rem .2rem;
}

.table-responsive tr.remise {
  background:#DDD;
}
.table-responsive td.show {
      position: relative; 
}
.table-responsive td.cart-article-menu {
   text-align:right;
}
.table-responsive td.cart-article-menu .dropdown-menu{
   width:280px;
}
.table-responsive td.cart-article-menu .dropdown-menu .dropdown-item {
   float:left;
   display: inline-block;
   width: auto;
   padding:0.4rem 1.25rem;
} 
.sublevel {
  margin: 0 -15px;
  width: calc(100% + 30px);
}

.sublevel .row{
      margin: 9px 0;
}

.card.active {
   opacity:1;
}

.card.active .card-header .col {
    background: #E88C0C;
}


.ll-skin-latoja .ui-datepicker {
    padding: 0;
    position: absolute;
    z-index: 5;
    top: 40px;
    display: none!important;
}

.ll-skin-latoja .form-control:focus + .ui-datepicker { 
    display: block!important;
}
.ll-skin-latoja .form-control + .ui-datepicker:focus { 
    display: block!important;
}
.img-fluid + .dropdown-menu {
      left: 5px!important;
     white-space: normal;
}
 strike {
  color:#cc0000;
  font-size:13px;
 }
 .w-15 {
    width: 11% !important;
}
#map {
  display: none;
}
.pac-container:after { 
    background-image: none !important; height: 0px;
}
.noZone {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.noZone:focus {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.fieldico.bronze {
  color:#b35919;
}
.fieldico.gold {
  color:#d4aa3b;
}