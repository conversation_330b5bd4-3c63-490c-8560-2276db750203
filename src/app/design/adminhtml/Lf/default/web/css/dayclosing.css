@charset "UTF-8";


.dayclosing-view-index .page-content {
  padding-left: 0;
} 
.dayclosing-view-index .nav {
  background:none;
  border-top: 0;
 display: inline-block;
}  
.dayclosing-view-index .nav li {
  
 display: inline-block;
}  
.dayclosing-view-index .nav em{
  margin-left: 5px;
}  
.dayclosing-view-index .recap.input{
 width: 280px;
 border-right: 0px!important;
 padding-bottom: 10px;
} 
.dayclosing-view-index .input input{
float:right;
width: 80px;
padding-left: 5px;
color: #003456!important;
}  
.dayclosing-view-index .tab-content {
  background:white; 
  padding:30px 20px;
  height: calc(100% - 150px);
  overflow-y: scroll;
  border-top:1px solid #dee2e6!important;
  margin-top: -1px;
}
.dayclosing-view-index .badge {
 font-size: 20px;
  padding:10px 45px 10px 25px;
  vertical-align: bottom;
} 
.dayclosing-view-index .badge em {
 margin-right: 10px;
} 
.dayclosing-view-index .badge + em {
 margin-left: -35px;
 color:white;
 margin-top: -10px;
 pointer-events: none;
 vertical-align: middle;
}   
#dayclosing .badge  {
  padding-right: 15px!important;
}
#dayclosing .badge em {
 margin-left: 15px;
 color:white!important;
 margin-top: -4px;
 pointer-events: none;
 vertical-align: middle;
}  
.dayclosing-view-index .recap {
 
 padding:0px 20px;
  border-right:1px solid #003456!important;
 display: inline-block;
  font-size: 20px;
}  
.dayclosing-view-index .recap + .recap + .recap + .recap { 
  border-right:0px; 
}   
.dayclosing-view-index .total {
  font-size: 20px;
   padding-bottom: 20px;
} 
.dayclosing-view-index .total  span{ 
  width: 160px; 
  display: inline-block;
} 
.dayclosing-view-index .total.two span + span{
  margin-left: 15px;
  width: 80px;
  text-align: center;
} 
.dayclosing-view-index .bg {
   background: #edF1F2;
} 
.dayclosing-view-index .total span+ span{
  display: inline-block;
  width:90px;
  text-align: right;
}
.dayclosing-view-index .total span {
  font-weight: bold; color:#003456!important;
} 
.dayclosing-view-index .total.ecart span {
  font-weight: normal; color:#CC0000!important;
} 
.dayclosing-view-index .fa-print   {
 color:#003456!important;
 font-size: 20px;
} 
#filterzone {
  color:#666;
}

.table th {
  font-size: 20px!important;
  color:#003456!important;
}
.table td {
  font-size: 18px!important;
  padding-left: 15px;
  
}
.table tfoot {
  border:2px solid #003456!important;
  background: #edF1F2;
  font-weight: bold;
 
}
.table tfoot .grand {
   padding:0;
}
.table tfoot .grand div {
  display: inline-block;
  padding: 5px 15px;
  background: #CCC;
  font-weight: bold;
}
.nav-link {
   font-size: 24px!important;
}
.h83 {
    height: 83%;
    max-height: 83%;
}
.h83 .card-body  {
    height: 85%;
    max-height: 85%;
}
.h15 {
    height: 15%;
}
.h33 {
    height: 33%;
    max-height: 33%;
}
.h30 {
    height: 30%;
    max-height: 30%;
}
.h79 {
    height: 79%;
    max-height: 79%;
}
.h94 {
    height: 94%;
    max-height: 94%;
}
.h90 {
    height: 88%;
    max-height: 88%; 
}
.overflow-y {
   overflow-y: scroll;
    overflow-x: hidden;
}
 .f-left {
  float:left;
 }
 .f-right {
  float:right;
 }

 .f-left.col-xl-3 {
  max-width:23%;
 }
 .split {
  background:#CCC;
  color:black;
  font-weight: bold;
 }
 .col-lg-23 {
    flex: 0 0 17%;
    max-width: 17%;
}
.col-lg-25 {
    flex: 0 0 19.4%;
    max-width: 19.4%;
}
.col-lg-24 {
    flex: 0 0 18%;
    max-width: 18%;
}
.col-lg-26 {
    flex: 0 0 22%;
    max-width: 22%;
}

.col-lg-24 span {
    width:60px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
.icon-layers {
      font-weight: bold;
      color:#ce61ca;
 }
 

 td .col-lg-24 em  {
   font-size:24px;
}

 td .col-lg-24 em + em {
  margin-left: -19px;
}
.col-lg-24 a {
    width: 0px;
    position: absolute;
    text-decoration: none;
}
.col-lg-24 small{
    width:20px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 .badge.small {
  zoom:0.75;
 }
.table-responsive tr:focus,
.table-responsive td:focus,
.table-responsive tr:hover,
.table-responsive tr.selected  {
  background:#f3eae0;
}

.table-responsive tr.remise {
  background:#DDD;
}
.table-responsive td.show {
      position: relative; 
}

.sublevel {
  margin: 0 -15px;
  width: calc(100% + 30px);
}

.sublevel .row{
      margin: 9px 0;
}
.card  {
   opacity:0.5;
}
.card.active {
   opacity:1;
}

.ll-skin-latoja .ui-datepicker {
    padding: 0;
    position: absolute;
    z-index: 5;
    top: 40px;
    display: none!important;
}

.ll-skin-latoja .form-control:focus + .ui-datepicker { 
    display: block!important;
}
.ll-skin-latoja .form-control + .ui-datepicker:focus { 
    display: block!important;
}
.img-fluid + .dropdown-menu {
      left: 5px!important;
     white-space: normal;
}
 strike {
  color:#cc0000;
  font-size:13px;
 }
 .w-15 {
    width: 11% !important;
}
 .w-35 {
    width: 35% !important;
}
#map {
  display: none;
}
.pac-container:after { 
    background-image: none !important; height: 0px;
}
.noZone {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.noZone:focus {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.logo {
  width:170px;
}
h2 {
  color:#003456;
  font-size: 2.25rem;
}
h2 small{
  margin-left:30px;
}
h2 a {
  padding: 0 10px;
  font-size:25px; 
  position: absolute;
  left: 41.66%;
}
h2 a + a {
  right: 30%;
  left: unset;
}
a.selected {
   color:#003456;
}

.hidden {
 visibility: hidden;
}

.pt-05 {
  padding-top: 0.125rem !important;
}

.zindex {
  z-index: 3;
  height:85%!important;
  font-size:1.25em;
  width: 99%;
}
.text-big {
  font-size:2em!important;
  line-height: 1em!important; 
}

.btn.text-big {
  padding: 0rem 1rem;
}
.btn.text-big.btn-success {
  font-size: 1.25em!important;
  padding-top:0.25em;padding-bottom:0.25em;
}
input.text-big {
  height: 30px;
  width: 50px;
  border:0;
  background:transparent;

}

input.text-big[value='0'] + button + button.vert  {
    opacity:0;
    pointer-events: none;
}
.mt-10 {
  margin-top:50px;
}
.zindex .img-fluid {
    width: 62px;
    margin-right: 10px;
}
.zindex tr td .col-lg-5 {
   margin-top: -1px;
   font-size: 1.55em;
}

.vert {
  vertical-align:middle;
}
.shrinked {
  margin-right: 95px;
}
.shrink {
  position:absolute; 
  opacity:0!important;
  margin-left: -150px;
}
.bg-warning2 {
  background:#d0a715!important;
}
.bg-success2 {
  background:#287e3c!important;
}
.bg-info2 {
  background:#528c9e!important;
}
.bg-blue {
  background: #003456!important;
}
.o-1 {
  opacity:1!important;
}
.o-1.ui-sortable-helper {
  position:fixed!important;
}
  .print {
    display:inline-block;
    background:#999!important;
}

.ui-sortable.hovered {
  background:#DDD;
  opacity:0.7;
}


.text-success {
  color: #21a240 !important; }

.text-info {
  color: #528c9e !important; }

.text-warning {
  color: #d0a715 !important; }

