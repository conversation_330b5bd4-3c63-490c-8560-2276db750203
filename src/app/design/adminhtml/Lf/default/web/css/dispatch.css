@charset "UTF-8";

.dispatch-view-index .content-wrapper {
    padding: 0!important;
}
.dispatch-view-index .wrapper .section-container {
    left: 15px;
    right: 0;
    width: calc(100% - 30px);
}
.inconnu {
  display: none
}

.h83 {
    height: 83%;
}
.h83 .card-body  {
    height: 85%;
}
.h15 {
    height: 15%;
}
.h33 {
    height: 33%;
}
.h30 {
    height: 30%;
}
.h79 {
    height: 79%;
}
.h94 {
    height: 94%;
}
.h90 {
    height: calc(100% - 85px);
}
.overflow-y {
   overflow-y: scroll;
    overflow-x: hidden;
}
.mapp {
  margin-top: -10px;
  display:block!important;
}
 .f-left {
  float:left;
 }
 .f-right {
  float:right;
 }

 .f-left.col-xl-3 {
  max-width:23%;
 }
 .split {
  background:#CCC;
  color:black;
  font-weight: bold;
 }
 .col-lg-23 {
    flex: 0 0 17%;
    max-width: 17%;
}
.col-lg-25 {
    flex: 0 0 19.4%;
    max-width: 19.4%;
}
.col-lg-24 {
    flex: 0 0 18%;
    max-width: 18%;
}
.col-lg-26 {
    width: 365px;
    margin-left: 5px;
}
.col-lg-77 {
    width: calc(100% - 380px);
}
.col-lg-100 {
    width: calc(100% - 10px);
    margin-left: 5px;
}
.col-lg-55 {
    flex: 0 0 44%;
    max-width: 44%;
}

.col-lg-24 span {
    width:60px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 td .col-lg-24 em  {
   font-size:24px;
}
 td .col-lg-24 em + em {
  margin-left: -19px;
}
.col-lg-24 a {
    width: 0px;
    position: absolute;
    text-decoration: none;
}
.col-lg-24 small{
    width:20px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 .badge.small {
  zoom:0.75;
 }
.table-responsive tr:focus,
.table-responsive td:focus,
.table-responsive tr:hover,
.table-responsive tr.selected  {
  background:#f3eae0;
}

.table-responsive tr.remise {
  background:#DDD;
}
.table-responsive td.show {
      position: relative; 
}

.sublevel {
  margin: 0 -15px;
  width: calc(100% + 30px);
}

.sublevel .row{
      margin: 9px 0;
}
.card  {
   opacity:0.5;
   box-shadow:none;
}
.card.active {
   opacity:1;
}

.ll-skin-latoja .ui-datepicker {
    padding: 0;
    position: absolute;
    z-index: 5;
    top: 40px;
    display: none!important;
}

.ll-skin-latoja .form-control:focus + .ui-datepicker { 
    display: block!important;
}
.ll-skin-latoja .form-control + .ui-datepicker:focus { 
    display: block!important;
}
.img-fluid + .dropdown-menu {
      left: 5px!important;
     white-space: normal;
}
 strike {
  color:#cc0000;
  font-size:13px;
 }
 .w-15 {
    width: 11% !important;
}
 .w-35 {
    width: 35% !important;
}
#map {
  display: none;
}
.pac-container:after { 
    background-image: none !important; height: 0px;
}
.noZone {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.noZone:focus {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.logo {
  width:170px;
}
h2 {
  color:#003456;
  font-size: 2.25rem;
}
h2 small{
  margin-left:30px;
}
h2 a {
  padding: 0 10px;
  font-size:25px; 
  position: absolute;
  left: 41.66%;
}
h2 a + a {
  right: 40%;
  left: unset;
}
a.selected {
   color:#003456;
}

.hidden {
 visibility: hidden;
}

.pt-05 {
  padding-top: 0.125rem !important;
}

.zindex {
  z-index: 3;
  height:85%!important;
  font-size:1.25em;
  width: 99%;
}
.text-big {
  font-size:2em!important;
  line-height: 1em!important; 
}

.btn.text-big {
  padding: 0rem 1rem;
}
.btn.text-big.btn-success {
  font-size: 1.25em!important;
  padding-top:0.25em;padding-bottom:0.25em;
}
input.text-big {
  height: 30px;
  width: 50px;
  border:0;
  background:transparent;

}

input.text-big[value='0'] + button + button.vert  {
    opacity:0;
    pointer-events: none;
}
.mt-10 {
  margin-top:50px;
}
.zindex .img-fluid {
    width: 62px;
    margin-right: 10px;
}
.zindex tr td .col-lg-5 {
   margin-top: -1px;
   font-size: 1.55em;
}

.vert {
  vertical-align:middle;
}
.shrinked {
  margin-right: 95px;
}
.shrink {
  position:absolute; 
  opacity:0!important;
  margin-left: -150px;
}
.bg-warning2 {
  background:#d0a715!important;
}
.bg-success2 {
  background:#287e3c!important;
}
.bg-info2 {
  background:#528c9e!important;
}
.bg-blue {
  background: #003456!important;
}
.border-blue {
  border: 1px solid #003456!important;
}
.o-1 {
  opacity:1!important;
  width: 100%;
}

.ui-sortable-helper {
    position:fixed!important;
}
 
.deliver {
    flex: 0 0 48%;
    max-width: 48%; 
  margin-left: 2%;
}
 .o-1.bg-danger:before {
  content:'';
  width:100%;
  height:2px;
  background: #CC0000;
  transform:rotate(-10deg);
  position:absolute;
  top: 38px;
}
.print {
    display:none;
}
#portlet-2 .print {
    display:inline-block;
    background:#999;
}

.ui-sortable.hovered {
  background:#DDD;
  opacity:0.7;
}
.card-header.hovered {
    opacity:1!important;
} 
 
.card-header:hover {
    opacity:1!important;
}
.o-0 {
  opacity:0;
  width:0;
}
.card-columns {
    column-count: unset;
  position:relative;
  height: 100%;
}
.card-columns .card > .card-header  {
  padding:0!important;
  opacity:0.8;
}

.card-columns .card > .card-header  .btn {
    padding:0 10px;
    margin-top: -6px; 
}
.card-columns .card .card {
 position:relative;
  margin-bottom:0.5rem;
  min-height: 58px;
  height: auto;
}

.box-placeholder {
  height: 58px!important;
}
.card-columns .ui-sortable .card-header  {
    opacity:1;
    padding:5px!important;
}

.card-columns .entournee .card-body, 
.card-columns .entournee .card-body .card-header,  
.card-columns .ui-sortable.card-body  {
    padding: 5px!important;

} 
.card-body[data-toggle="portlet"] {
  height: 100%; 
  min-height: 100%;
}

.h4.hidden,
.card-body.hidden {
display:none;

}
.card-header:hover .show {
  z-index:1000;
  position:relative;
}
.mycontainer {          
    border: 1px solid #ccc;
    height: 100%;
    margin-bottom: 20px;
    min-height: 173px;
    z-index: 0;
    position: relative;
    display: inline-block;
    width: 345px;
    padding-bottom: 0px;
    vertical-align: top;
    white-space: normal;
    transition: all 0.3s ease;
}

.overflow-all {
    overflow: scroll;
    white-space: nowrap;
}

.mycontainer:hover {
   z-index:1;                       
   border:1px solid #333;  
   padding-bottom: 0px;

}

.mycontainer:hover .tournee:not(.withouteight) {
   min-height: 127px;
}

.mycontainer .withouteight {
  height: 0!important; 
   min-height: 0px!important;
}

.isSelected {
  z-index: -1;
  pointer-events: none;

}

.hours, .minutes {
  font-size:20px!important;
  padding-right: 3px!important;
}
.seconds {  
  font-size:16px!important;
  padding-left: 1px!important;
}
.isSelected2 {
    z-index: -1;
    pointer-events: none;
    position:absolute!important;
}

 
.isSelected   .bg-primary {
    background:#f0e097!important;
    border:1px solid #5d9CEC;
    border-bottom:3px solid  #5d9CEC!important;
  }
 .bg-primary { 
   background:none!important;
    border:1px solid #5d9CEC;
    border-bottom:2px solid  #5d9CEC!important;
  }
 


.mycontainer:hover {
                          
  border:1px solid #333; 
}
.hovered {
  background: none!important;
}

.card-columns .ui-sortable.card-body.tournee  { 
    height: 100%; 
}  
 .card-columns .ui-sortable.card-body.tournee.hovered  {
     min-height: 0px!important;
    height: 0; 
}  
.card-columns .ui-sortable.card-body  {
    padding: 5px!important;

}

.card-columns .card  {
  height: 100%;
position: absolute;
min-height: 170px;
}

.cf:before,
.cf:after {
    content: " "; /* 1 */
    display: table; /* 2 */
}

.cf:after {
    clear: both;
}


#portlet-2 .card *, 
.card-columns .entournee .card *,
.card-columns .ui-sortable .card *,
.card-columns .ui-sortable .no-card * {
   font-size:0.8rem;
}
.top-30 {
  top: -30px!important;
}

.livree, .encours {
    opacity:0.7!important;
}
.entournee .card-header .bg-blue {
    background:#888!important;
}

div[data-bou="1"]  {
    margin-top: -20px;
    color: white;
}
div[data-bou="1"] span {
  background:#003456;
  color:white;
  border-radius: 4px;
  padding:1px 3px;
}

div[data-bou="1"] span em {
  display: inline-block;
  font-size: 12px;
  margin-top: -2px;
}


.text-success {
  color: #21a240 !important; }

.text-info {
  color: #528c9e !important; }

.text-warning {
  color: #d0a715 !important; }


.bg-primary.selected .card-header {
  background-color: #f0e097; }

.bg-success2.selected .card-header {
  background-color: #f0e097; }




@-webkit-keyframes rotating  {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.rotating {
  -webkit-animation: rotating 2s linear infinite;
  -moz-animation: rotating 2s linear infinite;
  -ms-animation: rotating 2s linear infinite;
  -o-animation: rotating 2s linear infinite;
  animation: rotating 2s linear infinite;
      width: 14px;
    height: 11px;
}

