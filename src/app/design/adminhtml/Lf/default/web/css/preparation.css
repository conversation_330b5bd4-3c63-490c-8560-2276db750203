@charset "UTF-8";
 

::-webkit-scrollbar {
  width: 40px!important;
  height: 8px;
  background-color: #eee; /* or add it to the track */
}
/* Add a thumb */
::-webkit-scrollbar-thumb {
    background: #bbb; 
}

.preparation-view-index .content-wrapper {
    padding: 0!important; 
}
.preparation-view-index .wrapper .section-container { 
    left: 15px;
    right: 0;
    width: calc(100% - 30px);
}
#filterzone {
  color:#666;
}
.inconnu {
  display: none
}
.bg-primary.selected .card-header {
  background-color: #f0e097; }

.bg-success2.selected .card-header {
  background-color: #f0e097; }

.h83 {
    height: 83%; 
}
.h83 .card-body  {
    height: 85%; 
}
.h15 {
    height: 15%;
}
.h33 {
    height: 33%; 
}
.h30 {
    height: 30%; 
}
.h79 {
    height: 79%; 
}
.h94 {
    height: 94%; 
}
.h90 {
    height: 88%; 
}
.overflow-y {
   overflow-y: scroll;
    overflow-x: hidden;
}
 .f-left {
  float:left;
 }
 .f-right {
  float:right;
 }

 .f-left.col-xl-3 {
  max-width:23%;
 }
 .split {
  background:#CCC;
  color:black;
  font-weight: bold;
 }
 .col-lg-23 {
    flex: 0 0 17%;
    max-width: 17%;
}
.col-lg-25 {
    flex: 0 0 19.4%;
    max-width: 19.4%;
}
.col-lg-24 {
    flex: 0 0 18%;
    max-width: 18%;
}
.col-lg-26 {
    flex: 0 0 22%;
    max-width: 22%;
}

.col-lg-24 span {
    width:60px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
.icon-layers {
      font-weight: bold;
      color:#ce61ca;
 }
 

 td .col-lg-24 em  {
   font-size:24px;
}

 td .col-lg-24 em + em {
  margin-left: -19px;
}
.col-lg-24 a {
    width: 0px;
    position: absolute;
    text-decoration: none;
}
.col-lg-24 small{
    width:20px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 .badge.small {
  zoom:0.75;
 }
.table-responsive tr:focus,
.table-responsive td:focus,
.table-responsive tr:hover,
.table-responsive tr.selected  {
  background:#f3eae0;
}

.table-responsive tr.remise {
  background:#DDD;
}
.table-responsive td.show {
      position: relative; 
}

.sublevel {
  margin: 0 -15px;
  width: calc(100% + 30px);
}

.sublevel .row{
      margin: 9px 0;
}
.card  {
   opacity:0.5;
}
.card.active {
   opacity:1;
}

.ll-skin-latoja .ui-datepicker {
    padding: 0;
    position: absolute;
    z-index: 5;
    top: 40px;
    display: none!important;
}

.ll-skin-latoja .form-control:focus + .ui-datepicker { 
    display: block!important;
}
.ll-skin-latoja .form-control + .ui-datepicker:focus { 
    display: block!important;
}
.img-fluid + .dropdown-menu {
      left: 5px!important;
     white-space: normal;
}
 strike {
  color:#cc0000;
  font-size:13px;
 }
 .w-15 {
    width: 11% !important;
}
 .w-35 {
    width: 35% !important;
}
#map {
  display: none;
}
.pac-container:after { 
    background-image: none !important; height: 0px;
}
.noZone {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.noZone:focus {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.logo {
  width:170px;
}
h2 {
  color:#003456;
  font-size: 2.25rem;
}
h2 small{
  margin-left:30px;
}
h2 a {
  padding: 0 10px;
  font-size:25px; 
  position: absolute;
  left: 41.66%;
}
h2 a + a {
  right: 30%;
  left: unset;
}
a.selected {
   color:#003456;
}

.hidden {
 visibility: hidden;
}

.pt-05 {
  padding-top: 0.125rem !important;
}

.zindex {
  z-index: 3;
  height:85%!important;
  font-size:1.25em;
  width: 99%;
}
.text-big {
  font-size:2em!important;
  line-height: 1em!important; 
}

.btn.text-big {
  padding: 0rem 1rem;
}
.btn.text-big.btn-success {
  font-size: 1.25em!important;
  padding-top:0.25em;padding-bottom:0.25em;
}
input.text-big {
  height: 30px;
  width: 50px;
  border:0;
  background:transparent;

}

input.text-big[value='0'] + button + button.vert  {
    opacity:0;
    pointer-events: none;
}
.mt-10 {
  margin-top:50px;
}
.zindex .img-fluid {
    width: 62px;
    margin-right: 10px;
}
.zindex tr td .col-lg-5 {
   margin-top: -1px;
   font-size: 1.55em;
}

.vert {
  vertical-align:middle;
}
.shrinked {
  margin-right: 95px;
}
.shrink {
  position:absolute; 
  opacity:0!important;
  margin-left: -150px;
}
.bg-warning2 {
  background:#d0a715!important;
}
.bg-success2 {
  background:#287e3c!important;
}
.bg-info2 {
  background:#528c9e!important;
}
.bg-blue {
  background: #003456!important;
}
.o-1 {
  opacity:1!important;
}
.o-1.ui-sortable-helper {
  position:fixed!important;
}
  .print {
    display:inline-block;
    background:#999!important;
}

.ui-sortable.hovered {
  background:#DDD;
  opacity:0.7;
}


.text-success {
  color: #21a240 !important; }

.text-info {
  color: #528c9e !important; }

.text-warning {
  color: #d0a715 !important; }

