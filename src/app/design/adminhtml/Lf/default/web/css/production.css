@charset "UTF-8";

.production-view-index .content-wrapper {
    padding: 0!important;
}
.production-view-index .wrapper .section-container {
    left: 15px;
    right: 0;
    width: calc(100% - 30px);
}

.h83 {
    height: 83%;
    max-height: 83%;
}
.h83 .card-body  {
    height: 85%;
    max-height: 85%;
}
.h15 {
    height: 15%;
}
.h33 {
    height: 33%;
    max-height: 33%;
}
.h30 {
    height: 30%;
    max-height: 30%;
}
.h79 {
    height: 79%;
    max-height: 79%;
}
.h94 {
    height: 94%;
    max-height: 94%;
}
 .f-left {
  float:left;
 }
 .f-right {
  float:right;
 }

 .f-left.col-xl-3 {
  max-width:23%;
 }
 .split {
  background:#CCC;
  color:black;
  font-weight: bold;
 }
 .col-lg-23 {
    flex: 0 0 17%;
    max-width: 17%;
}
.col-lg-25 {
    flex: 0 0 19.4%;
    max-width: 19.4%;
}
.col-lg-24 {
    flex: 0 0 18%;
    max-width: 18%;
}
.col-lg-26 {
    flex: 0 0 22%;
    max-width: 22%;
}
.numero {
  min-width: 51px;
}

.fusion {
  margin-left: 10px;
}

.nmpd-wrapper {display: none;    zoom: 2;}
.nmpd-target {cursor: pointer;}
.nmpd-grid {position:absolute; left:50px; top:50px; z-index:5000; -khtml-user-select: none; padding:10px; width: initial;}
.nmpd-overlay {z-index:4999;}

input.nmpd-display {text-align: right;}

.modal-backdrop {
    opacity:0.2;
}
.col-lg-24 span {
    width:60px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 td .col-lg-24 em  {
   font-size:24px;
}
 td .col-lg-24 em + em {
  margin-left: -19px;
}
.col-lg-24 a {
    width: 0px;
    position: absolute;
    text-decoration: none;
}
.col-lg-24 small{
    width:20px;
    display: inline-block;
    text-align: center;
    transition: all 0.5s ease;
    opacity:1;
}
 .badge.small {
  zoom:0.75;
 }
.table-responsive tr:focus,
.table-responsive td:focus,
.table-responsive tr:hover,
.table-responsive tr.selected  {
  background:#f3eae0;
}

.table-responsive tr.remise {
  background:#DDD;
}
.table-responsive td.show {
      position: relative; 
}

.sublevel {
  margin: 0 -15px;
  width: calc(100% + 30px);
}

.sublevel .row{
      margin: 9px 0;
}
.card  {
   opacity:0.5;
}
.card.active {
   opacity:1;
}

.ll-skin-latoja .ui-datepicker {
    padding: 0;
    position: absolute;
    z-index: 5;
    top: 40px;
    display: none!important;
}

.ll-skin-latoja .form-control:focus + .ui-datepicker { 
    display: block!important;
}
.ll-skin-latoja .form-control + .ui-datepicker:focus { 
    display: block!important;
}
.img-fluid + .dropdown-menu {
      left: 5px!important;
     white-space: normal;
}
 strike {
  color:#cc0000;
  font-size:13px;
 }
 .w-15 {
    width: 11% !important;
}
#map {
  display: none;
}
.pac-container:after { 
    background-image: none !important; height: 0px;
}
.noZone {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.noZone:focus {
  color:#ff902b!important;
  border:1px solid #ff902b!important;
}
.logo {
  width:170px;
}
h2 {
  color:#003456;
  font-size: 2.25rem;
}
h2 small{
  margin-left:30px;
}
h2 a {
  padding: 0 10px;
  font-size:25px; 
  position: absolute;
  left: 41.66%;
}
h2 a + a {
  right: 40%;
  left: unset;
}
a.selected {
   color:#003456;
}

.hidden {
 visibility: hidden;
}

.pt-05 {
  padding-top: 0.125rem !important;
}

.zindex {
  z-index: 3;
  height:85%!important;
  font-size:1.25em;
  width: 101%;
}
.text-big {
  font-size:2em!important;
  line-height: 1.3em!important; 
}

.btn.text-big {
  padding: 0rem 1rem;
  width: 55px;
    height: 50px;
    background:#ccc;
}
.btn.text-big.btn-success {
  font-size: 1.25em!important;
  padding-top:0.25em;padding-bottom:0.25em;
  width: 90px;
  background-color: rgb(65, 137, 82);
}
input.text-big {
  height: 49px;
  width: 50px;
  border:0;
  background:transparent;

}
.table th, .table td {
    padding: 0.5rem 0.25rem;
}

input.text-big + button + button.vert  {
    opacity:0;
    pointer-events: none;
    visibility: hidden;
    height: 0;
}
input.text-big + button + button.vert.visible  {
    opacity:1;
    pointer-events:all;
    visibility: visible;
    height: 50px;
}
.mt-10 {
  margin-top:50px;
}
.zindex .img-fluid {
    width: 90px;
    margin-right: 10px;
}
.zindex tr td .col-lg-5 {
   margin-top: -1px;
   font-size: 1.55em;
}

.vert {
  vertical-align:middle;
}
.shrinked {
  margin-right: 95px;
}
.shrink {
  position:absolute; 
  opacity:0!important;
  margin-left: -150px;
}
