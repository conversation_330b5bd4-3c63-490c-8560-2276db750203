/*
whirl.css - http://jh3y.github.io/-cs-spinner
Licensed under the MIT license

<PERSON><PERSON> (c) 2014.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/
/* whirl base styling */
.whirl {
    position: relative;
}
.whirl:before {
    content: "";
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #999999;
    opacity: 0.6;
}
.whirl:after {
    z-index: 2;
    content: "";
    height: 40px;
    width: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -20px 0 0 -20px;
    -webkit-transition: all .75s ease 0s;
    transition: all .75s ease 0s;
    border-radius: 100%;
    border-top: 4px solid #555555;
    -webkit-animation: standard .75s infinite linear;
    animation: standard .75s infinite linear;
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}
.whirl.no-overlay:before {
    content: none;
    display: none;
}
/* whirl standard rotation animation used for duo, double-up etc. */
@-webkit-keyframes standard {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes standard {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
/*whirl bar follow - a simple strafing bar animation that follows itself*/
.whirl.bar.follow:after {
    -webkit-animation: bar-follow 1s infinite linear;
    animation: bar-follow 1s infinite linear;
}
@-webkit-keyframes bar-follow {
    0% {
        -webkit-box-shadow: inset 40px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 40px 0px 0px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset -40px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset -40px 0px 0px rgba(0, 0, 0, 0.5);
    }
}
@keyframes bar-follow {
    0% {
        -webkit-box-shadow: inset 40px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 40px 0px 0px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset -40px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset -40px 0px 0px rgba(0, 0, 0, 0.5);
    }
}
/*whirl bar - a simple strafing bar animation*/
.whirl.bar:after {
    height: 20px;
    width: 40px;
    border-radius: 0;
    border: none;
    -webkit-animation: bar 1s infinite linear;
    animation: bar 1s infinite linear;
}
@-webkit-keyframes bar {
    from {
        -webkit-box-shadow: inset 0px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px 0px 0px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset 80px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 80px 0px 0px rgba(0, 0, 0, 0.5);
    }
}
@keyframes bar {
    from {
        -webkit-box-shadow: inset 0px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px 0px 0px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset 80px 0px 0px rgba(0, 0, 0, 0.5);
        box-shadow: inset 80px 0px 0px rgba(0, 0, 0, 0.5);
    }
}
/* whirl blade spinner.
experimental spinner that tries to create a blade effect.*/
.whirl.blade:after {
    height: 20px;
    width: 20px;
    margin: -10px 0 0 10px;
    border-right: 4px solid transparent;
    border-top: 4px solid transparent;
    border-left: 4px solid transparent;
    border-bottom: 4px solid #e74c3c;
    opacity: 1;
    -webkit-transform-origin: left;
    -ms-transform-origin: left;
    transform-origin: left;
}
/*whirl double up - a doubling up standard animation spinner*/
.whirl.double-up:after {
    border-right: 4px solid #e74c3c;
    border-top: 4px double #e74c3c;
    border-left: 4px double #e74c3c;
    border-bottom: 4px double #e74c3c;
}
/*whirl duo - a dual colored standard animated spinner*/
.whirl.duo:after {
    border-right: 4px solid #333333;
    border-left: 4px solid #333333;
    border-top: 4px solid #f1c40f;
    border-bottom: 4px solid #f1c40f;
}
/* whirl helicopter spinner.*/
.whirl.helicopter:after {
    height: 10px;
    width: 40px;
    margin: -10px 0 0 -20px;
    border: none;
    border-radius: 0;
    opacity: 1;
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
    background-color: #8e44ad;
}
/*whirl line back and forth grow - a simple strafing line animation that goes back and forth and grows in the middle*/
.whirl.line.back-and-forth.grow:after {
    -webkit-animation: line-back-and-forth 1s infinite linear, line-back-and-forth-grow 1s infinite linear;
    animation: line-back-and-forth 1s infinite linear, line-back-and-forth-grow 1s infinite linear;
}
@-webkit-keyframes line-back-and-forth-grow {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    25% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    50% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    75% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    100% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
}
@keyframes line-back-and-forth-grow {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    25% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    50% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    75% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    100% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
}
/*whirl line back and forth - a simple strafing line animation that goes back and forth*/
.whirl.line.back-and-forth:after {
    -webkit-animation: line-back-and-forth 1s infinite linear;
    animation: line-back-and-forth 1s infinite linear;
}
@-webkit-keyframes line-back-and-forth {
    0% {
        width: 10px;
    }
    50% {
        width: 50px;
    }
    100% {
        width: 10px;
    }
}
@keyframes line-back-and-forth {
    0% {
        width: 10px;
    }
    50% {
        width: 50px;
    }
    100% {
        width: 10px;
    }
}
/*whirl line grow - a simple strafing line animation that grows in the middle*/
.whirl.line.grow:after {
    -webkit-animation: line 0.75s infinite linear, line-grow 0.75s infinite linear;
    animation: line 0.75s infinite linear, line-grow 0.75s infinite linear;
}
@-webkit-keyframes line-grow {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    50% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    100% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
}
@keyframes line-grow {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
    50% {
        -webkit-transform: scaleY(2);
        transform: scaleY(2);
    }
    100% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
}
/*whirl line - a simple strafing line animation*/
.whirl.line:after {
    height: 20px;
    border-radius: 0;
    border: none;
    -webkit-box-shadow: inset -10px 0px 0px rgba(0, 0, 0, 0.5);
    box-shadow: inset -10px 0px 0px rgba(0, 0, 0, 0.5);
    -webkit-animation: line .75s infinite linear;
    animation: line .75s infinite linear;
}
@-webkit-keyframes line {
    from {
        width: 10px;
    }
    to {
        width: 50px;
    }
}
@keyframes line {
    from {
        width: 10px;
    }
    to {
        width: 50px;
    }
}
/* whirl ringed spinner.
a spinner with a static ring and no overlay.*/
.whirl.ringed:after {
    border-right: 4px solid #e74c3c;
    border-top: 4px solid #2ecc71;
    border-left: 4px solid #2ecc71;
    border-bottom: 4px solid #2ecc71;
    opacity: 1;
}
/* whirl shadow oval left - makes use of box shadowing effects
in addition to offsetting the width and the height to angle the shadow*/
.whirl.shadow.oval.left:after,
.whirl.shadow.oval:after {
    -webkit-animation: spin-shadow 0.25s infinite linear;
    animation: spin-shadow 0.25s infinite linear;
    border: none;
    border-radius: 40% 100%;
}
@-webkit-keyframes spin-shadow {
    0% {
        -webkit-box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    25% {
        -webkit-box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    50% {
        -webkit-box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
    }
}
@keyframes spin-shadow {
    0% {
        -webkit-box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    25% {
        -webkit-box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    50% {
        -webkit-box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
    }
}
/* whirl shadow oval right - makes use of box shadowing effects
in addition to offsetting the width and the height to angle the shadow*/
.whirl.shadow.oval.right:after {
    -webkit-animation: spin-shadow 0.25s infinite linear;
    animation: spin-shadow 0.25s infinite linear;
    border: none;
    border-radius: 100% 40%;
}
@keyframes spin-shadow {
    0% {
        -webkit-box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    25% {
        -webkit-box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    50% {
        -webkit-box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
    }
}
/* whirl shadow - makes use of box shadowing effects */
.whirl.shadow:after {
    -webkit-animation: spin-shadow 0.25s infinite linear;
    animation: spin-shadow 0.25s infinite linear;
    border-radius: 100%;
    border: none;
}
@keyframes spin-shadow {
    0% {
        -webkit-box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    25% {
        -webkit-box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px 5px 5px rgba(0, 0, 0, 0.5);
    }
    50% {
        -webkit-box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset -5px -5px 5px rgba(0, 0, 0, 0.5);
    }
    100% {
        -webkit-box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
        box-shadow: inset 5px -5px 5px rgba(0, 0, 0, 0.5);
    }
}
/*whirl spherical horizontal spinner*/
.whirl.sphere.horizontal:after,
.whirl.sphere:after {
    border-radius: 100%;
    -webkit-animation: sphere .5s infinite;
    animation: sphere .5s infinite;
    border: none;
    height: 40px;
}
@-webkit-keyframes sphere {
    from {
        -webkit-box-shadow: inset 45px 0px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 45px 0px 10px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset -45px 0px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset -45px 0px 10px rgba(0, 0, 0, 0.5);
    }
}
@keyframes sphere {
    from {
        -webkit-box-shadow: inset 45px 0px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 45px 0px 10px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset -45px 0px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset -45px 0px 10px rgba(0, 0, 0, 0.5);
    }
}
/*whirl spherical vertical spinner*/
.whirl.sphere.vertical:after {
    border-radius: 100%;
    -webkit-animation: sphere-v 0.5s infinite;
    animation: sphere-v 0.5s infinite;
    border: none;
    height: 40px;
}
@-webkit-keyframes sphere-v {
    from {
        -webkit-box-shadow: inset 0px 45px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px 45px 10px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset 0px -45px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px -45px 10px rgba(0, 0, 0, 0.5);
    }
}
@keyframes sphere-v {
    from {
        -webkit-box-shadow: inset 0px 45px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px 45px 10px rgba(0, 0, 0, 0.5);
    }
    to {
        -webkit-box-shadow: inset 0px -45px 10px rgba(0, 0, 0, 0.5);
        box-shadow: inset 0px -45px 10px rgba(0, 0, 0, 0.5);
    }
}
/* whirl traditional spinner.
Makes use of standard animation spin and minimally extends core.*/
.whirl.traditional:after {
    border-right: 4px solid #777777;
    border-top: 4px solid #777777;
    border-left: 4px solid #999999;
    border-bottom: 4px solid #bbbbbb;
}