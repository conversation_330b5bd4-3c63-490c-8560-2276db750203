<!--
/**
 * CedCommerce
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the End User License Agreement (EULA)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * https://cedcommerce.com/license-agreement.txt
 *
 * @category    Ced
 * @package     Ced_Wallet
 * <AUTHOR> Core Team <<EMAIL>>
 * @copyright   Copyright CedCommerce (https://cedcommerce.com/)
 * @license     https://cedcommerce.com/license-agreement.txt
 */
-->
<div class="payment-method" data-bind="css: {'_active': (getCode() == isChecked())}">
    <div style="display:none"  class="payment-method-title field choice">
        <input  type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()"/>
        <label data-bind="attr: {'for': getCode()}" class="label"><span data-bind="text: getTitle()"></span></label>

    </div>
    <div>
                    <div class="payment-method-title field choice">
                        <div class='wallet_payment'>
                            <input id='checkbox'
                                   type="checkbox"
                                   name="payment[method]"
                                   value='automatic'
                                   class="wallet_checkbox"
                                   data-bind="attr: {'for': getCode()}, value: getCode(),checked: status,click: postwalletpayment "
                            />
                            <label><span data-bind="text: getTitle()"></span></label>
                        </div>
                    </div>
                 <div class='wallet_payment' data-bind="css: {'_active':status()}">
                    <!-- ko if: getLeftAmount() >= 0 -->
                    <div>
                        <p><!-- ko i18n: 'After the order payment your amount will be' --> <!-- /ko --><!-- ko text: getDisplayLeftAmount() --><!-- /ko --><!-- ko text:  getCurrency() --><!-- /ko -->.
                    </div>
                     <!--/ko-->
                   </div>
    </div>
        <!-- ko if: isCreditUsed() -->
                    <p data-bind="html: geteatlfCreditUsedText()"></p>
                    <p data-bind="html: getReloadText()"></p>
        <!--/ko-->
        <!-- ko if: hasNotEnoughMoney() -->
                <p data-bind="html: getNotEnoughCredit()"></p>
                <p data-bind="html: getReloadText()"></p>
        <!--/ko-->
    <p><span class="title gold"><!-- ko i18n: 'Astuce' --><!-- /ko --></span><br/><span data-bind="html: getTipsText()"</span></p>
    <div class="payment-method-content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <div class="payment-method-billing-address">
        <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        </div>
        <div class="checkout-agreements-block">
            <!-- ko foreach: $parent.getRegion('before-place-order') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>
          <!-- ko if: getLeftAmountWithOverdraft() >=0 -->
        <div class="actions-toolbar">
            <div class="primary">
                <button class="action primary checkout"
                        type="submit"
                        data-bind="
                        click: placeOrder,
                        attr: {title: $t('Place Order')},
                        css: {disabled: !isPlaceOrderActionAllowed()},
                        enable: (getCode() == isChecked())
                        "
                        disabled>
                    <span data-bind="i18n: 'Place Order'"></span>
                </button>
            </div>
        </div>
          <!--/ko-->
    </div>
</div>
