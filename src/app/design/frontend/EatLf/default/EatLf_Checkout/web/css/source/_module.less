//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .cart-summary {
    .grand.totals .price {
      font-size: 20px;
    }

    .totals {
      .price {
        font-size: 16px;
      }
    }

    .totals-tax {
      .amount {
        border-bottom: 1px solid #d1d1d1;
      }
    }

    .shipping {
      .free {
        color: @eatred;
        font-weight: bold;
        text-transform: uppercase;
        font-family: 'DinProBold';
      }
    }
  }
}



// **************** DEBUT COUPON PAGE PANIER ********************************
.coupon-tips {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.coupon-tips i {
    font-size: 28px;
    color: @lf-blue;
    margin-left: 5px;
}
.cart-summary .block .fieldset {
    display: flex;
    border: none;
}

.cart-summary .discount .field {
    display: flex;
    color: red;
    border: none;
}

.cart-summary .tip-popin {
    position: absolute;
    background: @lf-blue;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    line-height: 14px;
    color: white;
    padding: 15px;
    padding-right: 32px;
    margin-top: -62px;
    margin-right: 0px;
    margin-left: -30px;
}
.cart-summary .tip-popin .close {
    position: absolute;
    right: 4px;
    top: 18px;
    color: white;
    background: none;
    padding: 0;
    margin: -5px;
    border: 0;
    font-size: 1.4rem;
    line-height: 1.6rem;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-block;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 700;
}

.cart-summary .tip-popin .close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 38px;
    line-height: 32px;
    color: inherit;
    content: '\e616';
    font-family: 'icons-blank-theme';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}

.cart-summary .tip-popin:before {
    border: 6px solid;
    border-color: #003456 transparent transparent transparent;
    z-index: 99;
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
    bottom: -10px;
    right: 35px;
}

@media only screen and (min-width: 300px) and (max-width: 1000px) {
    .cart-summary .tip-popin:before {
        right: 25px;
    }
    .checkout-cart-index .cart-summary .tip-popin .close {
        color: @color-white !important;
        height: initial;
        margin-top: -5px;
        right: 7px;
    }
}

// **************** FIN COUPON PAGE PANIER ********************************
