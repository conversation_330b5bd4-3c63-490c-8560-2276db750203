//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .customer-account-index {
    .box-header {
      color: @lf-blue;
      display: flex;
      flex-direction: column;
      font-family: DinPro;
      justify-content: space-between;
      margin-bottom: 10px;

      strong {
        font-size: 1.9rem;
        font-family: DinProBold;
        letter-spacing: 0.18em;
        text-transform: uppercase;
      }

      .action.edit {
        margin-top: 10px;
      }
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
  .customer-account-index {
    .box-header {
      align-items: center;
      background: white;
      flex-direction: row;
      margin: -10px -20px;
      margin-bottom: 10px;
      padding: 20px 0;

      .action.edit {
        margin-top: 0;
      }
    }
  }
}