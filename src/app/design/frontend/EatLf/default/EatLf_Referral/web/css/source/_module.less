//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .account {
    .block.block-referral {
      background-color: @color-white;

      .box-title {
        margin-bottom: 15px !important;
      }
    }

    .box.box-referral {
      color: @lf-blue;
      font-family: DinP<PERSON>;
      height: auto !important;
    }

    .referral-dash-inner {
      background-color: @color-white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 15px 0;
      padding: 15px;
    }

    .referral-dash-content {
      display: flex;

      & > * {
        flex: 1;

        &:not(:last-child) {
          margin-right: 15px;
        }
      }
    }

    .referral-dash-cms {
      margin-bottom: 20px;
      margin-top: 5px;
      text-align: center;
    }
  }

  .referral-customer {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    font-family: 'Din<PERSON>ro';
  }

  .referral-customer-or {
    align-items: center;
    display: flex;

    span {
      color: @lf-blue;
      font-family: 'Din<PERSON>ro';
      font-size: 1.6rem;
      margin: 0 10px;
      text-transform: uppercase;
    }

    &:before, &:after {
      border-bottom: 1px solid @lf-blue;
      content: '';
      display: block;
      height: 1px;
      flex: 1;
    }
  }

  .referral-customer-block {
    background: rgba(240, 240, 240, 0.6);
    padding: 10px 20px 20px;

    &:not(:first-child) {
      margin-top: 10px;
    }

    .referral-customer-button {
      margin: 20px 0;
      width: 100%;
    }
  }

  .referral-customer-share {
    .message {
      margin: 0;
      padding-left: 20px;
    }

    div.mage-error {
      margin-top: 0 !important;
      padding-top: 10px;
      padding-bottom: 10px;
      background: #fb7576;
      color: #fff;
      text-align: center;
      font-size: 14px;
    }
  }

  input.referral-customer-emails {
    .lib-input-placeholder(
      @_input-placeholder-color: #969696;
    );

    border: 0;
    height: 56px;
    border-bottom: 1px solid #000356;
    padding: 15px;
  }

  .referral-customer-intro {
    background: rgba(240, 240, 240, 0.6);
    margin: 0 10%;
    padding: 20px;
  }

  .referral-customer-cms {
    background: rgba(240, 240, 240, 0.6);
    padding: 15px 20px 20px;
    margin-top: 15px;
  }

  .referral-customer-account {
    display: flex;
    justify-content: center;
  }

  .referral-customer-code {
    align-items: center;
    background-color: @color-white;
    border: 1px solid @lf-blue;
    color: @lf-blue;
    display: flex;
    font-family: 'DinProBold';
    font-weight: bold;
    font-size: 1.6rem;
    height: 48px;
    justify-content: center;
    text-align: center;
  }

  .referral-customer-button {
    align-items: center;
    background-color: @lf-blue;
    box-sizing: border-box;
    color: @color-white;
    display: flex;
    font-weight: bold;
    font-size: 1.7rem;
    height: 48px;
    justify-content: center;
    text-align: center;
    transition: all .3s;

    &:active {
      background-color: @lf-blue;
      color: @color-white;
    }
  }

  .referral-customer-copy {
    &:before {
      background-image: url('@{baseDir}images/copy.svg');
      display: block;
      content: '';
      height: 24px;
      margin-right: 5px;
      width: 24px;
    }
  }

  .referral-customer-forward {
    &:after {
      background-image: url('@{baseDir}images/arrow_right.svg');
      display: block;
      content: '';
      height: 24px;
      margin-left: 5px;
      width: 24px;
    }
  }

  .referral-customer-conditions {
    font-size: 1.2rem;
  }

  .referral-customer-link {
    color: @color-black;
    font-weight: bold;
    text-decoration: underline;

    &:hover, &:active, &:focus {
      color: #333;
      text-decoration: underline;
    }
  }

  .summary.title.discount {
    order: 2;
  }

  .checkout-onepage-success {
    .order-success-referral {
      margin-top: 60px;
    }

    .order-success-referral-header {
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      border-bottom: 1px solid #003456;
      padding-bottom: 15px;
    }
  }

  @media only screen and (min-width: 300px) and (max-width: @screen__ipad) {
    .account {
      .box.box-referral {
        width: ~"calc(100% - 30px)" !important;

        &:before {
          display: block;
          content: '';
          height: 40px;
          background: @color-white;
          margin: 0 -15%;
        }

        .box-header {
          padding-top: 15px;
        }
      }
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
  .referral-customer {
    flex-direction: row;
    margin-left: 10%;
    margin-right: 10%;
  }

  .referral-customer-block {
    margin-right: 20px;
    width: 275px;
  }

  .referral-customer-cms {
    margin-top: 0;
    max-width: 500px;
  }

  .checkout-cart-index {
    .cart-totals {
      margin-bottom: 25px;
    }

    .block.discount {
      border-top: 1px solid #d1d1d1;
    }
  }
}