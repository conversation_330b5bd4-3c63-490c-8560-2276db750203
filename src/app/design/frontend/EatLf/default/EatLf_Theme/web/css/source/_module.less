//
//  Theme variables
//  _____________________________________________
@font-face {
  font-family: "Futura";
  src: url(../fonts/Futura.otf) format("otf"), url(../fonts/Futura.woff) format("woff"), url(../fonts/Futura.ttf) format("ttf");
}
@font-face {
  font-family: "Eczar";
  src: url(../fonts/DINPro-Regular.otf) format("otf"), url(../fonts/DINPro-Regular.woff) format("woff"), url(../fonts/DINPro-Regular.ttf) format("ttf");
}
@eatorange: #ff8165;
@eatbeige:#f7f7f7;
@eatred: #b9a264;

@eatFontFamily: DinProBold, Arial;

//
//  Common
//  _____________________________________________

//
//
//  reprise du CMS de benoit
//  _____________________________________________
@media only screen and (min-width: 1024px){
  .produits h2 {margin-bottom: 15px;}
  .cms-home.cms-index-index.page-layout-1column.desk .produits .categorie .container .produit .addtocart div.ht {display:none;}
  .produits .categorie .container .produit .addtocart div.ttc {padding-left:15px;}
}
@media only screen and (max-width: 1000px) and (min-width: 300px){
  .produits .categorie .container .produit .addtocart div.ht {display:none;}
  .produits .categorie .container .produit .addtocart div.ttc {margin-left: 20px;}
}
.checkout-cart-index .cart-summary .discount .content .btn {background-color: #003456 !important;
  color: #fff !important;
  transition:none!important;
  height: 42px!important;
  margin-top: 0px!important;
  border-left-style: solid;
  border-left-color:#003456;

  font-size:12px;
    border: 0px;
}

.checkout-cart-index .cart-summary .discount .content .btn.cancel { width:44px!important;}
.checkout-cart-index .cart-summary .discount .content .btn.cancel span.cross:after { content: '\e616';
  font-family: 'icons-blank-theme';
  display: inline-block!important;
  font-size: 40px;
  height: 30px;
  line-height: 30px;
  color: white;
  float: right;
  margin-right: -14px;
  font-weight: 400;
}


//  reprise du CMS de benoit
//  _____________________________________________


  .alias-name {
      display: inline-block;
      width:46%;
      font-weight: bold;
      float: left;
  }
  .alias-pan {
      width: 0%;
      white-space: nowrap;
  }

.iwd_opc_select_option.selected .payment-method:not(._active),
  .iwd_opc_select_option:not(.selected) .payment-method,
  .iwd_opc_select_option:not(.selected) .payment-method._active  {
    display:none!important;
  }


  .iwd_opc_select_option.selected .payment-method._active {
    display:block;
    margin-top:15px;
    .iwd_opc_universal_wrapper {
      margin-bottom: 0;
    }
  }
  .iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option:not(.selected):before {
      background: white;
      border: 1px solid @lf-blue;
      box-shadow: 0px;
      width: 20px;
      height: 20px;
      margin-top: 0px;
      margin-left: 0px;
      border-radius: 20px;
  }
  .iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option.selected:after {
    display: none;
  }
  .iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option.selected:before {
      background: @lf-blue;
      border: 3px solid #fff;
      box-shadow: 0px 0px 0px 1px @lf-blue;
      width: 14px;
      height: 14px;
      margin-top: 1px;
      margin-left: 1px;
      margin-right: 11px;
  }



  .iwd_main_wrapper .field .iwd_opc_select_container {
    max-height: none;
  }


#discount-coupon-form .coupon .control{
  height:40px;
}
#discount-coupon-form[novalidate] .coupon .control{
  height:70px;
}
#discount-coupon-form[novalidate] .coupon .btn {
  margin-top: -79px!important;
}
.main .wallet {
  border:0;
  width:83%;
  text-align:center;
  font-family: @eatFontFamily;
  color:@lf-blue;
  text-transform: uppercase;
}
.columns .main {
  overflow:hidden;
}
.admin__control-select,
.data-grid-filters,
.admin__control-support-text,
.admin__filter-actions {
  display: none!important;
}
#transactionGrid {
  display: flex;
  flex-direction: column-reverse;
  font-family: DinPro
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper .iwd_opc_column.iwd_opc_payment_column {
  height: auto;
}
.admin__data-grid-pager-wrap {
  float: none;
  text-align: center!important;
  width: 100%;
}
.admin__data-grid-pager {
  margin-left: -14%;
  border:0;
  font-family: @eatFontFamily;
}
.data-grid td {
  border:0;
  background: rgba(240,240,240,0.7)!important;
}

.data-grid .data-grid-th {
  border:0;
  background: rgba(240,240,240,0.7)!important;
  color:@lf-blue!important;
}






#co-payment-form {
  fieldset {
    min-height: 0px;
  }
}
.systempay-payment-redirect {
  background:none!important;
}
.iwd_opc_wrapper .scroll-wrapper,
.iwd_opc_wrapper .scroll-wrapper .scroll-content {
  max-height: unset!important;
  height: auto!important;
  padding-bottom: 0;
}

.iwd_opc_wrapper .scroll-wrapper .scroll-content:not(.selected)  .iwd_opc_select_option[data-value="lf_payment_livraison"]:after {
  height:14px;
  width: 14px;
}

.iwd_opc_wrapper .scroll-wrapper .scroll-content:not(.selected)  .iwd_opc_select_option[data-value="wallet"] {
  padding-bottom: 55px !important;
}


.iwd_opc_wrapper .scroll-element {
  display: none!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image {
  &[data-value="edenred"] {
    .iwd_opc_option_image {
      right: 0px !important;
      top:0px!important;
    }
  }
  &[data-value="wallet"]  {
    img {
      right:5px!important;
    }
  }
}
.iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected,
.iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option.selected,
.iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option:hover {
  background-color: rgba(229,229,229,0.4)!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option {
  &[data-value="wallet"]:after {
    top:1px!important;
  }
}
.payment-method-content fieldset.systempay-form {
  padding: 0 30px;
}
.iwd_main_wrapper .field:not(.choice) .kr-label label {
  display: inline-block;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option[data-value="systempay_standard"] .iwd_opc_option_image {
  width: auto!important;
  margin-top: 10px!important;
  margin-left: 0!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option[data-value="systempay_standard"] {

  padding-right: 0!important;

  &:after {
    margin-top:-4px;
  }
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"]   {
  height:auto!important;
  padding-bottom: 17px!important;
  .payment-method {
    margin-top: 50px;
  }
}
.onepage-index-index.desk .page-bottom {
    margin-top: 0px!important;
}
#header .delivery .step1 .adresse::placeholder {
  color:#656565;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option:after {
  width:14px; height:14px;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"] {
  border-bottom:1px solid @lf-blue!important;
  padding-bottom: 36px;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option {
  overflow:visible!important;
  white-space:normal!important;
  padding-bottom: 20px;
  border-bottom:1px solid @lf-blue!important;
  margin-bottom: 0!important;
  padding-top:20px;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image img.iwd_opc_option_image {
  right:0px!important;
  display: block!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image   {
  border-bottom:1px solid @lf-blue!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container  .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"]:not(.selected)   {
  border-bottom:1px solid @lf-blue!important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method:last-child._active   {
  background-color: #f5f5f5 !important;
  margin-top: -21px;
  padding-bottom: 10px;
  padding-right: 10px;
  border-bottom:1px solid @lf-blue!important;
  padding-top: 26px;
  border-top: 1px solid #f5f5f5;
  position:relative;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .iwd_opc_select_option .payment-method:last-child._active {
  border-bottom: 0 !important;
  margin-top: 15px !important;
  padding-top: 10px;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method._active p {
  text-align:left;
  padding-left:34px;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method._active b {
  color:@eatorange;
  font-weight: normal;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method._active p p {
  padding-left:0px;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method._active .title {
  font-family:@eatFontFamily;
  text-transform:uppercase;
  font-size:18px;
  &.gold {
    color:@lf-gold;
  }
}

.wallet_payment._active {
  margin-top:-30px;
}

.iwd_main_wrapper .field-error {
  font-size: 1.8rem;
  font-weight: bold;
}
.iwd_main_wrapper .field .scroll-wrapper {
  min-height: unset;
}
.iwd_main_wrapper .field .scroll-wrapper,
.iwd_main_wrapper .field .iwd_opc_select_container {
  border-bottom:0!important;
}

body.customer-account-create,
body.checkout-onepage-success,
body.checkout-cart-index,
body.onepage-index-index {
  background: none!important;
}


.modal-popup.shipping-warning-modal,
.modal-popup.checkout-validation-popin,
.modal-popup.cart-stock-popin {
  .modal-content {
    padding-top: 20px;
    text-align: center;
  }
  .modal-title {
    border:0;
    color: @lf-blue;
    font-size: 30px;
    font-family: @eatFontFamily!important;
    line-height: 35px;
  }
  .modal-inner-wrap {
    min-width: 550px!important;
    width: 550px!important;
    overflow: visible;
  }
  .modal-footer {
    min-height: unset;
    padding: 20px!important;
    text-align: center;
  }
  .modal-header {
    padding-bottom: 0.5rem;
    padding-top: 1.2rem!important;
    color: @lf-blue;
    font-size: 30px;
    font-family: @eatFontFamily!important;
  }
}



.back-to-top {
  background:rgba(255,255,255,0.1)!important;
}
h2 {
  font-family: @eatFontFamily!important;
}

.ui-autocomplete {

  z-index: 1000;
  float: left;
  min-width: 160px;
  padding: 0;
  max-width: 450px;
  list-style: none;
  background-color: #ffffff;
  border-color: @lf-blue;
  border-style: solid;
  border-width: 1px;
  border-radius: 5px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  background-clip: padding-box;
  *border-right-width: 2px;
  *border-bottom-width: 2px;

  li {
    padding: 0;
    margin: 0;
    &.ui-menu-no-result a {
      background:white!important;
      color: @lf-blue!important;
    }
    &:hover {
      background-color: @lf-blue;
      a {
        color:white!important;
        background-color: @lf-blue!important;
      }
    }
    a {
      color: @lf-blue;
      padding: 5px 15px!important;
      text-decoration: none;
      cursor:pointer;
      display: block;
    }

    .company-name {
      display: inline-block;
      width: 150px;
      font-weight: bold;
    }

    .company-address {
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal !important;
    }

    #showMessage {
      background-color: #003456;
    }

    .ui-menu-item-label {
      color: #ffffff;
      font-size: 14px !important;
      display: inline;
      font-family: 'Trebuchet MS',Tahoma,Verdana,Arial,sans-serif;
    }

    .ui-menu-item-url {
      color: #ff99cc;
      font-style: italic;
      display: inline;
      padding-left: 76px;
      font-family: @eatFontFamily;
      font-size: 12px;
      text-decoration: underline;
    }

    .ui-state-hover, .ui-state-active, .ui-state-focus {
      color: #ffffff;
      text-decoration: none;
      background: @lf-blue;
      border: 0;
      border-radius: 0px;
    }
  }

  .ui-menu-item > a.ui-corner-all {
    display: block;
    padding: 3px 15px;
    clear: both;
    font-weight: normal;
    line-height: 18px;
    color: #555555;
    white-space: nowrap;
    text-decoration: none;
  }


}



footer {
  padding:70px!important;

  .prefooter {
    background: @eatbeige;
    margin:-70px -70px 70px;
    padding:20px 70px;
    font-family: 'DinProBold';
    text-transform: uppercase;
    text-align: center;
    color:@lf-blue;
    a {
      background:@lf-blue;
      display: inline-block;
      padding: 15px;
      margin:0 20px;
      color: white;
      &.sep  {
        margin:0 140px 0 20px;
      }
      &.sep:after {
        content:'';
        display: inline-block;
        height: 50px;
        border-right: 1px solid @lf-blue;
        margin-left: 100px;
        position: absolute;
        margin-top: -15px;
      }
    }
    & + .container {
      padding-top: 40px;
    }
  }
  .container {
    max-width: 1300px;
  }
  .bloc {

    border-right: 1px solid @eatbeige!important;
    width: 16%!important;
    &.last {
      border:0!important;
      width: 5%!important;
    }
    &:nth-of-type(2) {
      margin-left: -2%;
      width: 11%!important;
    }
    &:nth-of-type(3) {
      width: 17%!important;
    }
    &:first-child {
      border:0!important;
      text-align: left;
      padding-left: 0!important;
      margin-left: 0!important;
      width: 26%!important;
      .logo {               //logo du footer
        width: 260px;
        display: block;
        margin:auto;
        float:none!important;
        margin-bottom:15px;
        background-image: url('@{baseDir}images/logoa_new.svg')!important;
        background-size: 260px;
      }
    }

  }
}
.hilite {
  background:@lf-gold;
  display: inline-block!important;
  color:white!important;
  padding:0 5px;
  position:unset!important;
}

.hilite2 {
  background:@lf-blue;
  display: inline-block!important;
  padding:0 5px;
  color:white!important;
}


//
//  Produit
//  _____________________________________________


.produits {


  h2 {
    font-family: @eatFontFamily;
    letter-spacing: 2px;
    margin-top:15px;
    margin-bottom:0px;
  }
  .filtres, .filtres.fixednoanim {
    background:#f7f7f7;
    .filtre {
      color:@lf-blue!important;
      &.last:not(.selected):hover {
        background: none!important;
        color:@lf-blue!important;
        transition:unset!important;
        &:after {
          background: none!important;
        }
        .f1 {
          display:inline;
        }
        .f2 {
          display:none;
        }
      }
      &.last {
        transition:none;
      }
      &.last .tooltip {
        background:@lf-blue;
        h5 {
          color:@eatbeige!important;
        }
        .item {
          color:@eatbeige;
          &.selectedfilter:after {
            outline:5px solid @eatbeige;
          }
          &.selectedfilter[data-content='veggie']:after {
            outline: 5px solid #2a8e50!important;
          }
        }
        .item:hover {
          background:@eatbeige;
          color:@lf-blue;
          img {
            filter: invert(0);
          }
          &.selectedfilter:after {
            outline:5px solid @lf-blue;
          }
        }
        &:before {
          background:@lf-blue;
        }
      }
      &.last:before {
        border-left:1px solid #e9d2a0;
      }
      &:hover, &.selected {
        background:@lf-blue!important;
        color:white!important;
      }

      &.selected ~ .last.clickable,
      &.selected ~ .last.not(.clickable), {
      background: none!important;
      color:@lf-blue!important;
      transition: none;
      &:hover {
        background:@lf-blue!important;
        color:white!important;
        transition: none;
      }
    }
    }
    &.fixednoanim {
      border-top:91px solid #f7f7f7;
    }
    &.fixednoanim + .recap_filtres + .categorie {
      transition:none;
    }


  }
  .recap_filtres .item img {
    filter:invert(0);
  }
  .categorie .container .produit {
    margin-top:10px;
    h3,h4 {
      font-family: @eatFontFamily;
      padding-left: 15px;
    }
    &.cms:before {
      display:none;
    }
    &.cms:hover {
      margin-top:10px!important;
    }
    &:hover {
      margin-top:7px;
      div.visu img.prod {
        opacity: 1!important;
      }
    }

    .desc,
    .smalldesc {
      font-family: 'DinPro';
      padding-left: 15px;
    }
    .no-delivery,
    .addtocart {
      img {
        border:6px solid @eatred;
        border-bottom:5px solid @eatred;
        background: @eatred;
      }
      h5 {
        color:#FB7576!important;
      }
    }
    .addtocart div.add  {
        color: white;
        background-color: #b9a264;
        font-size: 16px;
        font-weight: bold;
        padding: 6px;
        width: 80px;
        text-align: center;
        float: right;
        margin-top: -4px;
    }
  }
}




//
//  Fiche produit
//  _____________________________________________


.productlayer .modal-inner-wrap {
  .produit {
    .no-delivery,
    .addtocart {
      img {
        border:6px solid @eatred!important;
        border-bottom:5px solid @eatred!important;
        background: @eatred!important;
      }
    }
      .addtocart div.add  {
          color: white;
          background-color: #b9a264;
          font-size: 16px;
          font-weight: bold;
          padding: 6px;
          width: 80px;
          text-align: center;
          float: right;
          margin-top: -4px;
      }
  }

  .action-close {
    background: @eatbeige!important;
    &:before {
      color:@lf-blue;
    }
  }
}




//
//  forms
//  ---------------------------------------------


.control input, select {
  border-top:2px solid #ecd8b8!important;
  border-left:2px solid #e8cc9d!important;
  border-bottom: 2px solid @lf-blue!important;
  border-right: 2px solid @eatred!important;
  background:rgba(255,255,255,0.7)!important;
  height: 42px!important;
  &:-internal-autofill-selected {
    background:#fbe0b9!important;
  }
}
legend.legend {
  font-family: @eatFontFamily;
}






//
//  Customer
//  ---------------------------------------------


body.account .page-main
{
  background:rgba(255,255,255,0.3);
}
.account-nav .content {
  background:rgba(240,240,240,0.6);
  padding: 0;
  .item .delimiter {
    border-top: 1px solid @eatbeige;
  }
}
.login-container .block  {
  background:rgba(240,240,240,0.6);
}
.account h3,
.account li.item  {
  font-family: @eatFontFamily;
}


.modal-popup.confirm._show .modal-inner-wrap {
  width: 90%;
  max-width:  600px;
  .modal-content {
    margin-top: -27px;
    padding-left: 15px;
    padding-bottom: 20px;
    font-family: @eatFontFamily;
    color: @lf-blue;
  }
  .modal-footer {
    min-height: auto;
    text-align: center;
    button {
      background: @eatbeige;
      color:@lf-blue;
      font-size: 15px;
      text-transform: uppercase;
      font-family: 'DinProBlack';
      padding: 15px;
      margin:0 15px;
      & + button {
        color: #fff;
        background:@lf-blue;
      }
    }
  }
}



//
//  Header
//  ---------------------------------------------

body {
  background-color: white;
}
header {
  background-color: @eatbeige!important;
  .container {

    &.okdelivery {
      .step1 {
        display:none;
      }
      .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
        margin-top: -87px;
        .commande {
          display: none;
        }
      }
    }

    .delivery {
      width:1200px;
      background:white;
      color:@lf-blue;
      font-family:'DinProBold';
      font-size:14px;
      margin:auto;
      height:90px;
      position:relative;
      margin-top:240px;
      margin-bottom:70px;
      z-index:10;

      .loader {
        height: 60px;
        text-align: center;
        margin-bottom: -90px;
        padding-top: 30px;
        opacity:1;
        background: white;
        transition: all 1s ease;
        position: relative;
        z-index: 1;

        div {
          display: inline-block;
          position: relative;
          width: 64px;
          height: 64px;
          div {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 51px;
            height: 51px;
            margin: -10px 0;
            border: 6px solid @lf-gold;
            border-radius: 50%;
            animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            border-color: @lf-gold transparent transparent transparent;
          }
          div:nth-child(1) {
            animation-delay: -0.45s;
          }
          div:nth-child(2) {
            animation-delay: -0.3s;
          }
          div:nth-child(3) {
            animation-delay: -0.15s;
          }
        }

        @keyframes lds-ring {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        &.hidden {
          opacity:0;
          z-index: 0;
          pointer-events:none;
        }
      }
      .txt {
        margin-top: 11px;
        display: block;
        margin-left: 652px;
        position: absolute;
      }
      .tooltip {
      }
      .moment {
        background: white;
        position: relative;
        margin-top: -2px;
        padding: 5px;
        text-transform: uppercase;
      }
      .resetPopin {
        position:absolute;
        right: 152px;
        top: 91px;
        background: #003456;
        color: white;
        width: 585px;
        font-size: 12px;
        text-align: center;
        line-height: 14px;
        &:before {
          border: 6px solid;
          border-color: transparent transparent @lf-blue transparent;
          z-index: 99;

          content: '';
          display: block;
          height: 0;
          position: absolute;
          width: 0;
          margin-top: -12px;
          right: 20px;
        }
        span {
          display: inline-block;
          width:330px;
          margin-top: 30px;
        }
        .btn {
          margin:25px 40px 25px 10px;
          font-size:11px;
          padding:10px 25px;
          vertical-align: super;
        }
        .close {
          position: absolute;
          right: 10px;
          top: 10px;
          color: white;
          background: none;
          padding: 0;
          margin: -5px;
          border: 0;
          &:before {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 38px;
            line-height: 32px;
            color: inherit;
            content: '\e616';
            font-family: 'icons-blank-theme';
            margin: 0;
            vertical-align: top;
            display: inline-block;
            font-weight: normal;
            overflow: hidden;
            speak: none;
            text-align: center;
          }
        }
        &._place {
          left: 0;
          right:unset;

          &:after {

          }
        }
      }

      &.fixed {
        transition:all 0.5s ease;
        position:fixed;
        margin-top:0px;
        top:0;
      }
      &.bordered:after {
        border-bottom: 1px solid #85a0b2!important;
      }
      &.fixednoanim {
        position:fixed;
        margin-top:0px;
        top:0;
        right:181px;
        transition:none!important;
        height: 91px;
        background:#f7f7f7;

        .step3 {
          border-right: 1.4px solid @lf-blue;
        }
        .loader {
          background:#f7f7f7;
        }

        .step2 .choix-livraison label + .advice {
          z-index:2;
          margin-top:64px;
          &:after {
            margin-top:-123px;
          }
        }
        .resetPopin {
          top:92px;
          width: 480px;
          &._place {
            left: unset;
            right:670px;
          }
        }
        &:after {
          content:'';
          width: 100%;
          position:fixed;
          height: 1px;
          border-bottom: 1px solid #ccc;
          left: 0;
          top: 89px;
        }

        .step3 {
          border-left: 1px solid #aaa;
        }

        .block-minicart {
          margin: -1px 20px;
        }

        .step1 .ok {
          height: 62px;
          padding-top: 28px;
        }

      }
      .commande {
        .grey.blue {
          font-weight: bold;
          padding-top: 0;
          span {
            width:~"calc(100% - 200px)";
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        #_place span:before {
          width: auto;
          display: inline-block;
          line-height: 13px;
          font-size: 13px;
          text-transform: uppercase;
          font-family: 'DinProBold';
          position: relative;
          float: right;
          top: 8px;
        }
        #_place span:after {
          content: attr(data-hour);
          line-height: 13px;
          font-size: 13px;
          width: auto;
          display: inline-block;
          background-color:@eatred;
          text-transform: uppercase;
          font-family: 'DinProBold';
          position: absolute;
          right: 30px;
          top: 12px;
          padding:1px 4px;
          color:white;
        }
        #_place span {
          width:~"calc(100% - 85px)";
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-top: 4px;
        }
        #_when span:before {
          content:'Collecte';
          line-height: 13px;
          font-size: 13px;
          font-family: 'DinProBold';
          text-transform: uppercase;
          width: auto;
          display: inline-block;
          position: relative;
          float: right;
          top: 8px;
          right:0px;
        }
        #_when span:after {
          background-color:@eatred;
          content: attr(data-hour);
          line-height: 13px;
          font-size: 13px;
          width: auto;
          display: inline-block;
          text-transform: uppercase;
          font-family: 'DinProBold';
          position: absolute;
          right:35px;
          top: 12px;
          padding:1px 4px;
          color:white;
        }
        #_when span {
          width:~"calc(100% - 140px)";
          padding-top: 4px;
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      &.okdelivery {
        margin-top: 113px;

        .commande {
          opacity:1;
        }

        &.fixednoanim, &.fixed  {
          margin-top: 0px;
          .OK {
            height: 68px!important;
            padding-top: 0!important;
          }
        }
        .OK {
          height: 68px!important;
          padding-top: 0!important;
        }
      }
      .button {
        float:right;
        margin-top: -32px;
        margin-right: 7px;
      }
      .tooltip .button {
        margin-top: 6px;
        float:none;
        width:auto;
        &:after {
          line-height: 43px;
        }
      }
      .ok {
        color:@lf-blue;
        font-family:'DinProBlack';
        letter-spacing:4px;
        text-align: center;
      }
      .check {
        left:700px;
        top:0px;
        background:#FB7576;
        color:white;
        display:none;
        width: 430px;
        z-index: -1;
        pointer-events:none;
        position: absolute;
        font-size: 16px;
        font-family:''DinPro'';
        padding:34px 17px 35px;
        padding-left:50px;
        cursor:pointer;
        &.hidden {
          z-index: -1!important;
          pointer-events:all;
          display:none!important;
        }
        &.visible {
          z-index: 1;
          pointer-events:all;
        }
        &:before {
          content:' ';
          position:absolute;
          height:30px;
          background:#FB7576;
          width:30px;
          transform:rotate(45deg);
          display:block;
          left:-14px;
          cursor:pointer;
          top:30px;
        }
      }
      .nok,  .empty {
        color: @lf-gold;
        margin: 23px 15px;
        float: right;
        width: 362px;
        z-index: 1;
        position: relative;
        font-size: 12px;
        span {
          margin-left: -49px;
          float: left;
          margin-top: 7px;
          display: inline-block;
          img {
            width: 25px;
          }
          &:before {
            background: @lf-gold;
            content:"";
            height:32px;
            width:3px;
            display: inline-block;
            transform: rotate(35deg);
            margin-top: -6px;
            margin-left: 11px;
            position: absolute;
          }
        }
      }
      .nok:before {
        border:3px solid @lf-gold;
        content:"";
        height:28px;
        width:28px;
        border-radius:20px;
        display: inline-block;
        float: left;
        margin-right: 20px;
      }
      .empty {
        font-size: 16px;
        line-height: 32px;
      }
      .block-minicart {
        padding: 0px 20px;
        border:1px solid @lf-blue;
        box-shadow: none;
        margin: -2px 15px;
        position: absolute;

        .subtitle {
          width:320px;
          margin:0;
        }
        &:after {
          display:none;
        }

        ::-webkit-scrollbar {
          width: 6px;
        }

        /* Track */
        ::-webkit-scrollbar-track {

        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
          background: @lf-blue;
          border-radius: 10px;
        }

        .minicart-items-wrapper {
          padding:10px;
          margin: 0 -17px 0px -20px;
          min-height: 78px;
          max-height: 350px;
          height: auto!important;
        }
        .minicart-widgets {
          display:none;
        }
        .actions {
          margin:6px -10px;
        }
        .viewcart {
          background:@lf-blue;
          display: block;
          color:white;
          font-family: DinProBold, arial;
          text-transform: uppercase;
          font-size: 15px;
        }
        .product {
          text-align: left;
        }
        .product-item {
          padding:0;
          cursor:default;
        }
        .product-item-details {
          padding:0;
          display: inline-block;
          max-width:235px;
        }
        .product-item-photo {
          display: inline-block;
          float: none;
        }
        .product-item-name {
          text-align: left;
          display: inline-block;
          font-family: DinProBold, arial;
          width: 240px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
          a {
            color:@lf-blue;
            text-decoration: none;
            &.delete {
              position:relative;
              right:-2px;
              margin-top:7px;
              transform:scale(0.8);
              &:before {
                content: url(../images/bin.svg);
              }
            }
          }
        }
        .qty   {
          display: inline-block;
          width:32px;
          label:after {
            display:none;
          }
        }
        .qty div {
          display: inline;
        }
      }
      .step1, .step0 {
        .ok {


          width: 220px;
          font-size: 12px;
          text-transform: uppercase;
          height: 64px;
          margin-top: 0px;
          background: @lf-gold;
          display: inline-block;
          vertical-align: top;
          padding-top: 26px;
          line-height: 20px;
          float: left;
          color: white;

          &:before {
            background: url(../images/location2.svg);
            background-repeat: no-repeat;
            width: 26px;
            height: 34px;
            margin-left: 30px;
            margin-right: -10px;
            background-size: 25px;
            display: inline-block;
            content: '';
            vertical-align: middle;
            float: left;
          }
        }
      }
      .step1{
        &:not(.OK) {
          & + .step2.OK + .step3 {
            margin-top: -87px;
            .commande {
              display: none;
            }
          }
        }
      }

      .step-coming-soon {
        display: flex;
        align-items: center;
        position: relative;
        width:1200px;
        height:auto;
        overflow:visible;

        .coming-soon {
          margin-left: 50px;
          font-size: 18px;
        }

        .modify-shipping {
          margin-left: 50px;
          font-size: 18px;
          position: relative;

          &:after {
            background: url(../images/edit.svg);
            content: '';
            cursor: pointer;
            width: 15px;
            background-size: 15px;
            height: 15px;
            display: inline-block;
            position: absolute;
            right: -26px;
            top: calc(24%);
          }
        }

        .ok {

          width:220px;
          height:62px;
          margin-top: 0;
          background:@lf-gold;
          color:white;
          display: inline-block;
          vertical-align: top;
          text-transform: uppercase;
          padding-top:28px;
          float:left;
          &:before {
            background:url(../images/clock.svg);
            width:32.5px;
            height:32.5px;
            display: inline-block;
            content:'';
            background-size:33px;
            margin-right: 10px;
            vertical-align: middle;
            background-repeat: no-repeat;
          }
        }
      }

      .step0{
        &:not(.OK) {
          & + .step1{
              display: none;
          }
        }

        .choix-type{
            display:inline-block;
            top: 27px;
            position: absolute;
            border-left: 1px solid #e5e5e5;
            label {
              cursor:pointer;
              font-family: 'DinPro', arial;
              font-size:15px;
              width: 220px;
              margin-left: 30px;
              white-space: nowrap;
            }
        }
        .poplocalities a {
          cursor:pointer;
          display: inline-block;
          padding:3px;
        }

       .poplocalities {
          margin-left:25px;
          margin-top:40px;
          & + label + .poplocalities {
             margin-left:285px;
             & + label + .poplocalities {
               margin-left:542px;
            }
          }
        }
      }
      .step0.OK {
        display: none!important;
        & + .step1 .ok {
          line-height: 18px;
          padding-left: 20px;
          width: 200px;
          &:before {
            margin-left: 10px;
            margin-right: 0px;
          }
        }
      }

      .step1.OK  {
        display:none;

        & + .step2 {
          display: inline-block;
          position: relative;
          width:1200px;
          height:auto;
          overflow:visible;

          &.KO {
            display: none;
          }

          .choix-livraison {display:inline-block;}
          .ok {


            width:220px;
            height:62px;
            margin-top: -85px;
            background:@lf-gold;
            color:white;
            display: inline-block;
            vertical-align: top;
            text-transform: uppercase;
            padding-top:28px;
            float:left;
            &:before {
              background:url(../images/clock.svg);
              width:32.5px;
              height:32.5px;
              display: inline-block;
              content:'';
              background-size:33px;
              margin-left: 20px;
              vertical-align: middle;
              float: left;
              background-repeat: no-repeat;
            }
          }
          &.OK {
            display: none;
            & + .step3 {
              display: inline-block;
              margin: -1px 0px;
              position: absolute;
              width:100%;
              height:92px;
              overflow:visible;
              .smallcart {
                margin-top: 0;
                opacity:1;
              }
              .commande {
                opacity:1;
              }
            }
          }
        }
      }

      .step2 {
        width:0px;
        height:0px;
        overflow:hidden;
        transition:all 0.5s ease;
        display: none;
        &.noToday {
          .creneau2 {
            margin-left:423px;
          }
          .creneau3 {
            margin-left:602px;
          }
        }
        &.noTomorrow {
          &.noToday {
            .creneau3 {
              margin-left:408px;
            }
          }
          .creneau3 {
            margin-left:616px;
          }
        }
        .choix-livraison {
          top: 27px;
          position: absolute;
          border-left:1px solid #e5e5e5;
          span {
            color:#999;
            cursor:pointer;
            display: inline-block;
            margin-left:5px;
          }
          label {
            cursor:pointer;
            font-family: 'DinPro', arial;
            font-size:15px;
            width: 220px;
            margin-left: 30px;
            white-space: nowrap;
            &:after {
              display:none;
            }
            & + .advice {
              display: none;
              background:@lf-blue;
              padding:30px 50px;
              color:white;
              position:absolute;
              width: 190px;
              height: 63px;
              margin-top: -150px;
              text-transform: uppercase;
              text-align: center;
              &:after {
                content:'';
                width: 12px;
                height: 12px;
                transform: rotate(135deg);
                display: block;
                background: @lf-blue;
                position: absolute;
                top:117px;
                left:39px;
              }
            }
            &[for='delivery1']:hover + .advice,
            &[for='delivery1']:active + .advice,
            &[for='delivery1']:focus + .advice {
              display:block;
            }
          }
          input[type='radio'] {
            margin-left: 100px;
            vertical-align:middle;
            height: 21px;
            width: 21px;
            margin-top: -3px;
            cursor:pointer;
          }
        }
        .question {
          margin-top:12px;
          margin-left:230px;
          margin-right: 30px;
          padding-top: 16px;
        }
        .creneau {
          background:@lf-blue;
          border-radius:3px;
          padding:2px;
          width:240px;
          margin-top:4px;

          &:before {
            display: block;
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background:@lf-blue;
            transform: rotate(45deg);
            margin-left: 14px;
            margin-top:-5px;
          }
        }
        .creneau1 {
          margin-left:426px;
        }
        .creneau2 {
          margin-left:633px;
        }
        .creneau3 {
          margin-left:812px;
        }
        #creneaux {
          height:32px;
          z-index: 2;
          position: relative;
          width:240px;
          background:#eee;
          color:@lf-blue;
          font-family: DinPro, arial;
          option:first-child {
            font-family: DinPro, arial;
          }
          option {
            font-family: DinProBold, arial;
          }
        }

        .txt2 {
          font-size:12px;
          color:#999;
          float:right;
          display:inline-block;
          margin-top:-10px;
          & + .txt {
            visibility: hidden;
          }
        }
      }
      .step3 {
        display: none;
      }
      .question {
        font-size:18px;
        text-transform: capitalize;
        padding:13px 15px 0 19px;
        display:inline-block;


        height: 57px;
        vertical-align: middle;
        margin-top: 0;
        padding-top: 30px;
        padding-bottom: 0px;
      }
      .adresse {
        border: 0px;
        border-left:1px solid #e5e5e5;
        width: 450px;
        height: 32px;
        padding-left: 20px;
        font-family: 'DinPro', arial;
        color: @lf-blue;
        font-size: 16px;
        margin-top: 29px;
        vertical-align: top;
        &::focus {
          outline:none!important;
        }
      }
    }
  }
}

#header .container .aspan.fixednoanim a {
  transition:none;
}




input[type=text]::-ms-clear{
  display: none;
}

#goog_conv_iframe {
  opacity:0;
  visibility:hidden;
}

.customer-account-logoutsuccess .columns p,
.sales-order-history .message.empty.info {
  background:none;
  color:@lf-blue;
  font-size: 24px;
  font-family: 'DinProBlack';
  text-transform: uppercase;
  text-align: center;
  margin:0;
  width: 1200px;
  max-width: 100%;
  padding:0;
  letter-spacing: 3px;
  span:before {
    display:none;
  }
}
.customer-account-logoutsuccess .columns p {
  width: 100%;
}
.customer-account-login .field.password .control {
  margin-top: 0;
}
.messages {
  display: none;
}
.cart-container {
  .checkout-methods-items + .message {
    margin-top: 10px!important;
    background:@lf-gold!important;
    font-size: 12px!important;
    order:5;
  }
  .message {
    padding:10px 40px 10px 20px!important;
    font-size: 10px!important;
    margin-top: -15px!important;
    line-height: 22px!important;
    order:3;
    @media only screen and (min-width: 300px) and (max-width: 1000px) {
      margin-top: 0px!important;
    }
    div:after {
      position:absolute;
      right: 0px;
      top:7px;
    }
  }
}
.cart-container,
.messages {

  .message {
    cursor:pointer;
    &.error {
      background:#fb7576;
      text-align: center;
      font-family: 'DinProBold';
      text-transform: uppercase;
      font-size: 18px;
      color: white;
      letter-spacing: 2px;
    }
    &.warning {
      text-align: center;
      font-family: 'DinProBold';
      text-transform: uppercase;
      font-size: 18px;
      color: white;
      padding:30px;
      background:#fb7576;
      letter-spacing: 2px;
      padding-right: 0;
    }
    &.success {
      background:#87ca9e;
      text-align: center;
      font-family: 'DinProBold';
      text-transform: uppercase;
      font-size: 18px;
      color: white;
      padding:30px;
      letter-spacing: 2px;
      padding-right: 0;
      top: 0px;
    }
    div:before {
      display:none!important;
    }
    div:after {
      content:'\e616';
      font-family:'icons-blank-theme';
      display: inline-block;
      font-size:40px;
      height: 30px;
      line-height: 30px;
      color: white;
      float: right;
    }
  }
}

@media only screen and (min-width: 300px) and (max-width: 1000px) {
    .checkout-cart-index .messages .success{
        top: 60px;
    }
    .tunnel{
        border: 0px;
    }
}


.adresse,
.adresse:focus,
select:focus,
textarea:focus,
input:focus {
  box-shadow: none!important;
}
#password-error {
  display:none!important;
}
.password-bubble {
  display:none;
  background:#f7f7F7;
  margin-top:-10px;
  position:absolute;
  z-index:3;
  margin-left:150px;
  color:@lf-blue;
  letter-spacing: 2px;
  width: 350px;
  padding-top: 10px;
  text-align: left;
  padding-left: 30px;
  p {
    position:relative;
    z-index:4;
    padding:0 15px;
    font-weight:bold;
    font-size: 15px;
  }
  li {
    text-align:left;
    padding:0 20px 0 10px;
    list-style:none;
    font-size: 14px;
    line-height: 15px;
    &:before {
      content:'\e616';
      font-family:'icons-blank-theme';
      display: inline-block;
      font-size:26px;
      height: 20px;
      line-height: 30px;
      position: absolute;
      left: 45px;
      margin-top: -6px;
      color:#fb7576;
    }
  }
  li.okay {
    &:before {
      content:'L';
      display:block;
      width:11px;
      background:#87ca9e;
      color:white;
      height:13px;
      position:absolute;
      margin-left:5px;
      margin-top:-1px;
      z-index:1;
      border-radius: 15px;
      font-size:12px;
      line-height: 13px;
      text-align: center;
      font-weight: bold;
      font-family: arial, monospace;
      transform: rotate(45deg) scaleX(-1);
      padding-top: 2px;
      padding-right: 1px;
      padding-left: 4px;
      padding-bottom:1px;
    }

  }

  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .okay:before {
      margin-top:-11px;
      font-size:24px;
    }


  }
}
.smallcart {
  opacity:0;

  .total {

    font-family: @eatFontFamily;
  }
}
.form.contact fieldset {
  max-width: 700px;
  margin:auto;
  padding-right: 140px;
  textarea {
    border:0;
    border-bottom:1px solid @lf-blue;
  }
  .control.center {
    text-align:center;
  }
  div.txt {
    font-family: 'DinPro',arial;
    font-size: 14px;
    color: @lf-blue;
  }
}

.field.password .control {
  margin-top:-20px;
}

.eye {
  background: url(../images/eye-open.svg) no-repeat;
  width:20px;
  height:20px;
  cursor:pointer;
  transform:scale(1.5);
  float:right;
  top:13px;
  z-index:3;
  position:relative;
  margin-right:10px;
  display:block;
}
.eye.close {
  background: url(../images/eye-close.svg) no-repeat;
}

.commande {
  opacity:0;
}


#header {
  height: 470px;
  width: 100%;



  .burger, .menu {
    display: none;
  }

  .aspan.fixednoanim {
    .identified {
      color:transparent!important;
      background:@lf-gold;
      &:after    {
        top:0!important;
        color:@lf-blue!important;
        content:attr(data-content)!important;
        transform:none!important;
        background: @lf-gold!important;
      }
    }
  }
  .identified {
    color:transparent!important;
    background:@eatbeige;
    img {
      display: none;
    }
    &:after    {
      top:0!important;
      color:@lf-blue!important;
      content:attr(data-content)!important;
      transform:none!important;
      background: @eatbeige!important;
    }
    &.f-20,
    &.f-20:after {
      background: #a3a198!important;
    }
    &.f-30,
    &.f-30:after {
      background: @lf-gold!important;
    }
  }

  .bigtitre {
    position: absolute;
    top:130px;
    pointer-events: none;
    text-align: center;
    margin:auto;
    line-height: 30px;
    left:~"calc(50% - 600px)";
    width:1200px;

    &:before {
      font-size:40px;
      color:white;
      font-family: @eatFontFamily;
      text-transform: uppercase;
      letter-spacing: 7px;
      white-space: initial;
    }
    &:after {
      display: block;
      margin-top: 10px;
      font-size:20px;
      color:white;
      font-family: @eatFontFamily;
      text-transform: uppercase;
      letter-spacing: 2px;
      white-space: pre;
      line-height: 25px;
    }
  }
  .container {
    padding: 30px 10px 0px 10px;
    min-height:60px;
    .links {
      text-align: center;
      position: relative;
      width:970px;
      margin: auto;
      margin-top: -70px;
      padding-left:70px;
      span a.account {

        &:after {
          line-height:18px;
          height: 65px;
          white-space:pre-line;
          text-align: left;
          padding-top: 25px;
          padding-left:20px;
          padding-right: 120px;
          width:auto;
        }
      }
      .aspan {
        position: fixed;
        right: 0;
        z-index: 9;

        top:55px;
        width: 185px;
        height: 90px;
        cursor:pointer;
        &:hover a.account:after {
          text-decoration:underline;
        }
        a.close {
          float:right;
          z-index:33;
          position:relative;
          margin:0;
          padding:0;
          border:0;
          top:30px;
          right:-5px;
          cursor:pointer;
          text-decoration: none;
          &:after {
            content:'\e616';
            font-family:'icons-blank-theme';
            display: inline-block;
            font-size:40px;
            height: 30px;
            color:@lf-blue;
            line-height: 30px;
          }

          &:hover {
            text-decoration: none;
          }
        }
        &.fixednoanim {
          position: fixed;
          top: 0px!important;
          right: 0px;
          display: block;
          margin:0;
          border-left:0;
          line-height: 90px;
          &.logged {
            height: 14px!important;
          }
          &:after {
            line-height: 88px;
          }
        }
      }
      a {
        font-family: 'DinProBold';
        color: white;
        font-size: 11px;
        text-transform: uppercase;
        display: inline-block;
        margin-right: 20px;
        padding-right: 20px;
        border-right: 2px solid white;
        letter-spacing:4px;
        line-height: 9px;
        &.devis {
          display: none;
        }
        &:nth-of-type(4) {
          border-right: 0px;
        }
        &:last-child {
          border: 0;
          background: @eatred;
          color:white!important;
          width: 155px;
          padding:0 15px;
          text-align: center;
          top: 0;
          transition: 0.2s all ease;
          white-space: nowrap;
          right: 0;
          margin: 0;
          z-index: 12;
          font-size: 12px;
          letter-spacing: 2px;
          line-height: 91px;
          height:90px;
          display: block;

          &.identified {
            color:transparent!important;
          }

          img {
            height: 30px;
            padding:0!important;
            margin-right: 10px;
          }
        }
        &.logged {
          padding:38px 15px;
          height: 15px!important;
        }
        .account {
          height: 90px;
          margin-right: 0px;
          vertical-align: middle;
          padding: 0 15px;
          line-height: 88px;
          &:after {
            color: white;
            content: attr(data-content);
            line-height: 88px;
          }
        }

      }

    }
  }
  .logo {
    width: 300px;
    height:74px;
    transition: none!important;
    cursor: pointer;
    background-size: 300px;
    margin-top: 10px;
    background-repeat: no-repeat;
    background-color:transparent;
    background-image: url('@{baseDir}images/logob_new.svg')!important;

    &.fixednoanim {
      position: fixed;
      top: 0px;
      left: 0px;
      z-index: 9;
      transition: none!important;
      max-width: ~"calc(50% - 480px)";
      width:13%;
      background-image: url('@{baseDir}images/logob_new.svg')!important;
      border-left: 5px solid @eatbeige;
      border-left-color: @eatbeige!important;
      margin-top: 0;
      height: 90px;
      background-position: 13%;
      background-color: @eatbeige;
      background-size: 200px;
      border-right: 2720px solid @eatbeige;
    }
  }
}

.container {
  max-width: 1900px;
  margin: auto;
}

.closest {
  width:340px;
  height:370px;
  background-image: url(../images/cms/map.jpg);
  background-repeat: no-repeat;
  background-size: 944px;
  background-color:#eaeaea;
  padding-left: 1000px;
  padding-right: 60px;

  margin:auto;
  h4 {
    width: 210px;
    margin: auto;
    text-align: center;
    padding: 60px;
    font-size: 20px;
    font-family: 'DinProBlack';
    letter-spacing: 4px;
  }
  input[type='text'] {
    float: left;
    width: 290px;
    height: 50px;
    padding-left: 20px;
  }
  input[type='submit'] {
    background:@lf-blue;
    height: 50px;
    width: 50px;
    font-family:'DinProBold';
    color:white;
    font-size: 14px;
    border:0px;
    text-align: center;
    float: right;
  }
}

.bluewave {
  height:200px;
  background:@lf-blue;
  margin-top: -150px;
  width: 100%;
  position: absolute;
  left: 0;
  z-index: -1;
}


.breadcrumbs {
  display: none;
}


.bloc_bleu, .bloc_blanc {
  clear: both;
  background: @lf-blue;
  padding: 40px;
  width:auto;
  max-width: 800px;
  margin: auto;
  font-family:'DinPro';
  font-size: 16px;
  h3 {
    color:white;
    padding-top: 0!important;
    padding-left: 0!important;
    font-size: 24px!important;
    margin-bottom: 30px!important;
    text-align: center!important;
  }

  .btn {
    font-family: 'DinPro';
    font-size: 14px;
    width: 300px;
    padding: 15px 40px;
    text-transform: uppercase;
    border: 2px solid @eatred;
    color: white;
    background: @eatred;
  }

}

.bloc_bleu {
  margin-bottom: 100px;
  border:2px solid @lf-blue;
}
.bloc_blanc {
  background: white;
  color:@lf-blue;
  border:2px solid @lf-blue;
  h3 {
    color:@lf-blue;
  }
}
.parrain .modal-content {
  padding:0!important;
  width: 100%!important;

}
.parrain header {
  position: absolute;
  width: ~"calc(100% - 60px)" ;
  background:none!important;
}
.parrainPopup {


  text-align: center;
  color:@lf-blue;
  font-size: 16px;
  h3 {
    font-size: 32px!important;
    line-height: 40px!important;
    font-family: @eatFontFamily!important;
    padding:0!important;
    text-align: center!important;
    margin-bottom: 0!important;

  }
  .hilite {
    margin-top: 15px;
  }
  .head {
    height: 392px;
    b {
      font-size: 20px;
      font-family: @eatFontFamily;
      display: inline-block;
      vertical-align: middle;
    }
    img {
      float: right;
    }

    .logb {
      float: none!important;
      width: 330px;
      margin-top: 30px!important;
      margin-bottom: -40px;
    }
  }
  .bloc {
    display: inline-block;
    vertical-align: top;
    color:white;
    text-align: center;
    width: 200px;
    img  {
      margin-bottom: -10px;
      font-size: 16px;
    }
  }
  .bottomblue {
    background:@lf-blue;
    padding-top: 50px;
    padding-bottom: 50px;
    input {
      width: 500px;
      height: 50px;
      padding:15px;
    }
    .btn {
      font-family: 'DinPro';
      font-size: 12px;
      width: 220px;
      margin-top: 20px;
      padding: 15px 40px;
      text-transform: uppercase;
      border: 2px solid @lf-gold;
      color: white;
      background: @lf-gold;
    }
  }
}

.cms-devenir-partenaire {

  #header {
    background: white url(../images/cms/bgpartner.jpg) no-repeat center center / cover!important;
    .bigtitre {
      top:166px;
      display: block;

      &:before {
        font-size:35px;
        color:white;
        content:'Eat La Famille livre chaque jour \A les déjeuners de vos collaborateurs.';
        font-family: @eatFontFamily;
        text-transform: uppercase;
        letter-spacing: 7px;
        white-space: pre;
      }
      &:after {
        display: none;
      }
    }
  }
  .columns {
    max-width: 1680px;
    margin:auto;
    text-align: center;
  }
  .partner {
    background: white;
    margin-top: 70px;
    font-family: 'DinPro';
    height: 869px;
    margin-bottom: 50px;
    color:@lf-blue;
    h3 {
      font-size: 32px;
      font-family: @eatFontFamily;
      text-align: center;
      padding:0;
      padding-top: 230px;
      margin-bottom: 80px;
    }
    img {
      float: left;
    }
  }
  h2 {
    visibility: hidden!important;
  }



}


.cms-qui-sommes-nous {

  #header {
    background: white url(../images/cms/bgconcept.jpg) no-repeat center center / cover!important;
    .bigtitre {
      top:166px;
      display: block;

      &:before {
        font-size:35px;
        color:white;
        content:'Les restaurants La Famille\A s’invitent dans vos bureaux.';
        font-family: @eatFontFamily;
        text-transform: uppercase;
        letter-spacing: 3px;
        white-space: pre;
      }
      &:after {
        display: none;
      }
    }
  }

  .columns {
    max-width: 1680px;
    margin:auto;
    text-align: center;
    h3 {
      text-align: left;
      margin-bottom: 50px;
      padding-left: 80px;
      font-size: 26px;
      letter-spacing:0px;
      font-family: @eatFontFamily;
    }
  }

  .cms-right:not(.concept1), .cms-left:not(.concept2) {
    background: white;
  }
  .cms-right.concept1 {
    height: 51.2vw;
    max-height: 870px;
  }
  .cms-right:not(.concept1) {
    height: 47.6vw;
    max-height: 640px;
    .centertext {
      font-family: 'DinProBold', arial;
    }

    @media screen and (min-width: 1000px) and (max-width: 1100px) {
      height: 66vw;
    }

    @media screen and (min-width: 1100px) and (max-width: 1200px) {
      height: 58.6vw;
    }


    @media screen and (min-width: 1200px) and (max-width: 1330px) {
      height: 52.7vw;
    }



    img { margin-top: 80px;margin-bottom: 20px; }
  }
  .cms-right:not(.concept1), .cms-left.concept2 {
    max-height: 810px;
    margin-bottom: 80px;
  }
  .cms-left:not(.concept2) {
    height: 51.2vw;
    max-height: 870px;

    .centertext {
      font-size: 0.95vw;
      min-width: 720px;
    }
    h3 {
      margin-top: 90px;
      font-size: 1.4vw;
      text-align: center;
      margin-bottom: 20px;
      padding:0;
    }}
  h2 {
    visibility: hidden!important;
  }

}


.cms-comment-ca-marche {

  #header {
    background: white url(../images/cms/bgcomment.jpg) no-repeat center center / cover!important;
    .bigtitre {
      top:166px;
      display:block;
      &:before {
        display:block;
        font-size:35px;
        color:white;
        content:'Les restaurants La Famille\A s’invitent dans vos bureaux.';
        font-family: @eatFontFamily;
        text-transform: uppercase;
        letter-spacing: 5px;
        line-height: 40px;
        white-space: pre;
      }
      &:after {
        display: none;
      }
    }
  }
  h2 {
    visibility: hidden!important;
  }
  .columns {
    max-width: 1680px;
    max-height:1600px;
    margin:auto;
    text-align: center;

    h3 {
      text-align: center;
      font-size: 36px;
      letter-spacing:2px;
      letter-spacing: 2px;
      background: white;
      padding-bottom: 30px;
      margin: auto;
      margin-top: -70px;
      padding-top: 30px;
    }

    .delivered, .atwork {
      display:none;
    }
    .bloc {
      font-family: 'DinPro';
      font-size: 16px;
      color:@lf-blue;
      h3 {
        font-size: 20px;
      }
    }
    .b1 {

    }
    .b2 {
      margin-top:450px;
    }
    .b3, .b4, .b6 {
      margin-top:240px;
    }
    .b5 {
      margin-top:240px;
    }
    .b4 {
      margin-top:270px;
      margin-bottom: 30px;
      &:before {
        top:15px!important;
      }
    }
    .b5 {
      &:before {
        top:0px!important;
      }
    }

    .bloc:before {
      content:attr(data-text);
      width: 60px;
      height: 60px;
      display: block;
      border:1px solid @lf-blue;
      border-radius: 30px;
      position:relative;
      background: white;
      text-align: center;
      line-height: 56px;
      font-size: 24px;
      font-family: 'DinProBlack';
      top:15px;
    }
    .demi1 {
      width: 400px;
      height: 1450px;
      border-right: 1.3px solid @lf-blue;
      background: white;
      display: inline-block;
      vertical-align: top;
      padding-right: 70px;
      text-align: right;
      margin-bottom: 50px;


      @media screen and (min-width: 1000px) and (max-width: 1100px) {
        border-right: 1.6px solid @lf-blue;
      }

      h3 { text-align: right;
        font-family: @eatFontFamily}
      .bloc:before {
        left:439px;
      }
    }
    .demi2 {
      width: 400px;
      display: inline-block;
      vertical-align: top;
      height: 1450px;
      padding-left: 70px;
      margin-left: -4px;
      background: white;
      text-align: left;

      h3 { text-align: left; font-family: @eatFontFamily}
      .bloc:before {
        left:-102px;
      }
    }
  }
  .contain {
    border-bottom:60px solid white;
    width: 100px;
    margin:auto;
    top:-330px;
    border-top:170px solid white;


    @media screen and (min-width: 1000px) and (max-width: 1100px) {
      top:-324px;
      border-bottom:50px solid white;
    }


    position: relative;
    .btn {
      margin-left: -50px!important;
    }
  }

  .column.main .btn {
    font-family: 'DinPro';
    font-size: 16px;
    width: 250px;
    padding: 15px 40px;
    text-transform: uppercase;
    border: 2px solid @eatorange;
    color: @eatorange;
    background: white;
  }
}


.centertext {
  font-family: 'DinPro', arial;

  font-size: 17px;
  color:@lf-blue;
  margin:auto!important;
  text-align: center!important;
  .bleu {
    text-transform: uppercase;
  }
  .or {
    text-transform: uppercase;
    color:@lf-gold;
  }
  &:before {
    display:block;
    width:100px;
    height: 1px;
    background:@lf-blue;
    content:'';
    margin:auto;
    margin-top: 20px;
    margin-bottom:20px;
  }
  &.first:before  {
    display:none;
  }
}
.cms-left, .cms-right {
  width:50%;
  float:left;
  height: 870px;

  .btn {
    font-family:'DinPro';
    font-size: 11px;
    padding:15px 40px;
    text-transform: uppercase;
    border:1px solid @lf-gold;
    color: @lf-gold;
  }
  &.w40 {
    width:40%;
    float:none;
    display: inline-block;
    vertical-align: top;
    height: 435px;
    font-family: 'DinPro', arial;
    font-size: 16px;
    color:@lf-blue;
  }
  div {
    width:80%;
    text-align:left;
    margin-left:10%;
    h3, h5 {
      text-align: left;
    }
    h5 {
      font-size: 14px;
      margin-top: -20px;
      margin-bottom:20px;
      letter-spacing: 2px;
    }
  }
}
.cms-right {
  float:right;
}
.concept1 {
  background: white url(../images/cms/concept1.jpg) no-repeat center top;
  background-size: 100%;
}
.concept2 {
  background: url(../images/cms/concept2.jpg) no-repeat center top;
  background-size: 100%;
}

.formulaire {
  margin-top: 100px;
  h3 {
    margin-top:40px;
  }
  span {
    font-family: 'DinPro', arial;
    font-size: 14px;
    color:@lf-blue;
  }
  div {
    margin-left: 20%;
    margin-bottom: 15px;
    border:0;
    border-top: 1px solid @lf-blue;
    border-bottom: 1px solid @lf-blue;
    width: 60%;
    font-family:'DinProBold';
    font-size: 14px;
    text-transform: uppercase;
    height: 43px;
    line-height: 43px;
    letter-spacing: 2px;
    &.textarea {
      height: 250px;
    }
    textarea {
      border:0;
      height: 200px;
    }
    label {
      color:@lf-blue;
      cursor:pointer;
    }
    input {
      float: right;
      border:0;
      width: ~"calc(100% - 120px)";
      margin-top: 6px;
      padding-left: 15px;
      font-family:'DinProBold';
      font-size: 14px;
      color:@lf-blue;
    }
  }
}


.hand {
  background: white url(../images/cms/main.jpg) no-repeat center 70px;
  h3 {
    margin-top: 200px!important;
  }
}

.customer-account-logoutsuccess .columns p {
  text-align:center;
}

.checkout-cart-index .cart.main.actions {
  position: absolute;
  width: 100%;
  top: 640px;
}

//
//  Desktop
//  _____________________________________________

























//
//  Mobile
//  _____________________________________________




@media only screen and (min-width: 300px) and (max-width: 1000px) {


.alias-pan {
    width: 100px;
    white-space: break-spaces;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    direction: rtl;
 }
    .iwd_main_wrapper .iwd_opc_field #iwd_opc_shipping_method_group + div div.iwd_opc_select_option:after {
    display: none!important;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option {

    padding-bottom: 20px!important;
}
  #transactionGrid {
    zoom:0.7;
  }
  #co-payment-form {
    height:auto;
  }
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"] {
    padding-bottom: 25px!important;
}
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method._active p {
    padding-left:30px!important;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method:last-child._active   {
    padding-top: 6px;
    top: 1px;
    margin-top:0;
  }
  .onepage-index-index .iwd_main_wrapper .iwd_opc_alternative_wrapper .iwd_opc_payment_column {
    height: auto;
  }
  .iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option,
  .iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option.selected,
  .iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option:hover {
    font-size:16px!important;
  }
  .iwd_main_wrapper  .iwd_opc_section_delimiter {
    margin:0 auto 10px auto;
  }

  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .iwd_opc_field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image,
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image {
    padding-right: 0;
  }
  .iwd_opc_option_with_image {
    padding-right: 0!important;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image[data-value="edenred"] {
    height: 90px!important;
    img {
      float: right!important;
      position: unset!important;
    }
    &:after {
      top:-18px;
    }

  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option {
    &[data-value="wallet"]:after {
      top:-18px!important;
    }
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.selected,
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option {
    &[data-value="wallet"]:after {
      top:-18px!important;
    }
  }
  .wallet_payment._active {
    margin-top:0px;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_option_with_image:after {
    width:14px; height:14px;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image {
    overflow:visible!important;
    white-space:normal!important;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image img.iwd_opc_option_image {
    max-width: 100%!important;
    right: 17px!important;
    height: auto!important;
  }
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option[data-value="systempay_standard"] .iwd_opc_option_image {
    width: auto!important;
    margin-top: 10px!important;
    margin-left: 3px!important;
}
  .iwd_opc_option_with_image[data-value="systempay_standard"] {
    height: 100px !important;
    img {
      width: auto!important;
      margin-top: 10px!important;
      margin-left: 28px!important;
      height: auto!important;
      max-width: 100%!important;
    }
    &.selected:after {
      top: -30px !important;
    }
    &:after {
      left: 4px !important;
      top: -51px !important;
      margin-top:-18px !important;
    }
  }
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"] .payment-method {
    margin-top: 30px;
}
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .iwd_opc_field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image,
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image,
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image {
    &[data-value="wallet"] {
      padding-right:10px!important;
      padding-bottom: 25px!important;
      img {
        right:0px!important;
      }
    }
  }

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image img.iwd_opc_option_image {
    max-width: 100%!important;
    right: 0px!important;
    height: auto!important;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image div span {
    max-width: 90%;
    display: block;
}


  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"] {
    padding-bottom: 55px;
  }

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method:last-child._active   {
  margin-top: -22px!important;
}
.onepage-index-index .iwd_main_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method .payment-method-content iframe,
 .checkout-onepage-success .iwd_main_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method .payment-method-content iframe {
    height: 760px!important;
    min-height: 760px!important;
    width:100%!important;
    margin: 0 0px !important;
}
  .iwd_opc_option_with_image[data-value="wallet"] {
    border:0!important;
  }

  .ui-autocomplete{
    left: 11px!important;
    width: ~"calc(100% - 25px)!important";
    margin-top: -2px;
    li .company-name {
      display: block;
    }
  }

  .modal-popup.shipping-warning-modal,
  .modal-popup.checkout-validation-popin,
  .modal-popup.cart-stock-popin {
    .modal-content {
      padding-top: 120px;
      text-align: center;
    }
    .modal-title {
      border:0;
      color: @lf-blue;
      font-size: 25px;
      font-family: @eatFontFamily!important;
      line-height: 50px;
    }
    .modal-inner-wrap {
      min-width: 100%!important;
      width: 100%!important;
      overflow: visible;
    }
    .modal-footer {
      min-height: unset;
      padding: 20px!important;
      text-align: center;
    }
    .modal-header {
      padding-bottom: 0.5rem;
      padding-top: 1.2rem!important;
      color: @lf-blue;
      font-size: 25px;
      font-family: @eatFontFamily!important;
      left: 0;
      text-align: center;
      width: auto;
      padding-right: 100px;
      white-space: nowrap;
    }
  }

  .checkout-cart-index #maincontent  #remove-coupon + .field {
    float: left;
    width: 100%;
    & + button {

    }
  }

  .checkout-cart-index .cart.main.actions {
    margin-left: 20px;
    margin-top: 490px;
    position: absolute;
    width: 100%;
    top: unset;
  }

  .customer-account-logoutsuccess .columns p,
  .sales-order-history .message.empty.info {
    font-size: 18px;
  }
  .page.messages {
    .messages {
      .message {
        padding-top:28px;
        div {
          text-align:center;
        }
      }
    }
  }

  .block-addresses-list {
    margin-left:0!important;
    .block-content {
      padding:15px;
      padding-top:0px;
    }
  }
  .table-wrapper.order-items,
  .table-wrapper.orders-history {
    margin-left: 0;
    width: 100%;

    table tr {
      padding:5%;
      background:rgba(240,240,240,0.6);
      &:nth-child(odd) {
        background:rgba(250,250,250,0.7);
      }
    }
  }

  .control input + .nested .additional{
    margin-top: 5px!important;
  }
  .form-login,
  .form-edit-account,
  .form-address-edit{
    input {
      padding-left: 10px;
    }

  }
  .login-container .block .actions-toolbar {
    width:  auto!important;
    margin:auto !important;
  }

  footer {
    padding:40px 0px!important;
    min-height: 1150px;
    padding-top:0!important;
    .prefooter {
      margin:0;
      margin-bottom: 20px;
      padding:20px;
      & + .container {
        padding:0;
      }
      a {

        display: block;
        margin: 10px 20px!important;
        &:after {
          display:none!important;
        }
      }

    }
    .bloc {
      color:white!important;
      border-right: 0px!important;
      width: 100%!important;
      font-size: 14px;
      line-height: 24px;
      a {
        font-size: 14px;
        margin-top: 2px;
      }
      &.last {
        border:0!important;
        width: 100%!important;
        br + br + br {
          display: none;
        }
      }
      &:after {
        content: '';
        height: 1px;
        width: 160px;
        background: white;
        display: block;
        margin: auto;
        margin-top: 23px;
      }
      &:nth-of-type(2) {
        margin-left: 0%;
        display: block;
        float: none;
        width: 100%!important;
        color: white!important;
        margin-top: 50px;
      }
      &:nth-of-type(3),
      &:first-child {
        width: 100%!important;
      }
      &:first-child {
        height: 80px;
      }
    }
  }
  .modal-popup.modal-slide._inner-scroll .modal-inner-wrap {
    height:100%!important;
  }

  .password-bubble {

    margin-top:10px;
    margin-left:0px;
    width:95%;
    position:relative;
    padding-left: 0;
    padding-right: 0;
    ul {
        display: inline-block;
      padding-left: 20px;
    }
    p {
      margin-top:-15px;
      padding-top:15px;
      font-size: 15px;
      letter-spacing: 1px;
    }
    li {
        white-space: break-spaces;
      &:before {
        left:8px;
      }
    }

  }

  .field.password .control {
    margin-top:0px!important;
  }
  .eye { top:37px; }
  .form.contact fieldset {
    padding:20px;
  }
  .message.global.cookie {
    z-index: 23!important;
  }
  body {
    overflow-x:hidden;
  }
  .d-none {
    display:none!important;
  }
  .d-nope {
    height:1px!important;
  }
  #header .bigtitre {
    display: block;
    &:before {
      font-size:30px;
      white-space:pre;
      letter-spacing: 4px;
      line-height: 20px;
    }
    &:after {
      margin-top: 5px;
      font-size: 12px;
      line-height: 15px;
      letter-spacing: 1px;
    }
  }

  .onepage-index-index .lf-billing-address-form {
    width: calc(100% + 24px);
  }

  .customer-account-validatephone,
  .customer-account-create  {
    #header {
      min-height: 50px!important;
      .container {
        height: 50px!important;
      }
      .menu {
        display: none!important;
      }
    }
  }
  .customer-account-create,
  .customer-account-validatephone,
  .customer-account-logoutsuccess,
  .checkout-onepage-success,
  .onepage-index-index,
  .customer-account-login,
  .customer-account-forgot-password,
  .account {

    h3 {
      font-family: @eatFontFamily;
    }
    fieldset legend {
      font-weight: bold;
      color:@lf-blue;
      font-family: @eatFontFamily;
    }
    #header {
      min-height: 60px;
      .container {
        height: 60px;
      }
      .delivery .step0,
      .delivery.fixednoanim .step1,
      .delivery .step1,
      .commande {
        display:none!important;
      }
      .step3 {
        background:none;
      }
      .filtres {
        box-shadow:none;
        &.open {
          top:92px!important;
        }
      }
      .loader {
        display: none;
      }

    }


    .block-order-details-view,
    .order-details-items {
      width: 100%;
      padding-left: 5%;
      padding-right: 5%;
      margin-left: 0;
      .grand_total .price {
        font-size: 24px;
        font-weight: bold;
      }
    }
    .order-details-items {
      width: 100%;
      padding: 0;
      .order-title {
        padding-left: 15px;
      }
    }
  }

  .account #header {
    min-height: 80px;
    .container {
      height: 90px;
    }
    .menu {
      display: block!important;
      &:before {
        content:'';
        display:block;
        position:absolute;
        box-shadow:inset 0px 10px 10px -11px #656565;
        margin-top:32px;
        width:~"calc(100% + 20px)";
        height:5px;
        margin-left:-20px;
      }
    }

  }
  .account .sidebar-main {
    display: none;
  }
  .account .filtres  .block {
    padding-top: 0;
  }
  .account .block {
    background:rgba(240,240,240,0.6);
    padding-top: 15px;
  }

  .account .filtres   .nav.items {
    font-family: 'DinProBold';
    li {
      padding-left: 3px;
    }
  }

  //CMS


  .cms-devenir-partenaire {

    #header {
      background: url(../images/cms/bgpartner.jpg) no-repeat top 90px center / cover!important;
      background-position: 60% bottom!important;
      .bigtitre {
        top:115px;
        display: block;
        width: 100%;
        left:0;
        &:before {
          white-space:initial;
          line-height: 26px;
          font-size: 26px;
        }
        &:after {
          display: none;
        }
      }
    }
    .columns {

    }
    .partner {
      display: flex;
      flex-direction: column;
      background: white;
      margin-top: 70px;
      font-family: 'DinPro';
      height: auto;
      margin-bottom: 50px;
      font-size: 16px;
      text-align: left;
      padding-left: 20px;
      padding-right: 20px;
      img {
        order:2;
        margin-right: -20px;
        margin-left: -20px;
        margin-top: 40px;
        max-width:~"calc(100% + 40px)";
      }
      h3 {
        font-size: 24px;
        font-family: @eatFontFamily;
        text-align: center;
        padding:0;
        margin-right: -20px;
        margin-left: -20px;
        padding-top: 30px;
        margin-bottom: 40px;
        max-width:~"calc(100% + 40px)";

      }
      img {
        float: left;
      }
    }
    h2 {
      visibility: hidden!important;
    }



  }
  .parrainPopup .head  {
    padding:20px 20px 0;

  }
  .parrainPopup .head img {
    float:none;
    max-width: ~"calc(100% + 40px)";
    margin:-20px;
  }
  .parrainPopup  h3 {
    margin-bottom: -30px!important;
  }
  .parrainPopup  .hilite2 {
    display: block!important;
  }
  .parrainPopup  .img {
    margin-bottom: 30px;
  }
  .parrainPopup  .bloc {
    margin:auto;
    margin-top: 50px;
    display: block;
    margin-bottom: -20px;
  }
  .parrainPopup .bottomblue {
    padding-top: 1px;
    margin-top:290px;
    input, .btn {
      width: ~"calc(100% - 60px)";
      margin-top:0;
    }
  }

  .cms-comment-ca-marche {

    #header {
      background: url(../images/cms/bgcomment.jpg) no-repeat top 90px center / cover!important;
      background-position: 80% bottom!important;
      .bigtitre {
        width: 100%;
        left:0;
        top:105px;
        &:before {
          white-space:initial;
          line-height: 28px;
          font-size: 24px;
        }
      }
    }

    .columns {
      max-width: none;
      margin:auto;
      text-align: center;
      margin-top: -300px;
      max-height: 2660px;
      h3 {
        font-size: 32px;
        margin-top: 220px;
      }
      .column.main .btn {
        font-size: 16px;
      }
      .bloc {
        font-family: 'DinPro';
        font-size: 16px;
        color:@lf-blue;
        position: absolute;
        width: 100%;
        h3 {
          margin-top:56px;
          font-size: 24px;
          text-align: center!important;
          &:before {
            content:'';
            border-right: 1px solid @lf-blue;
            display: inline-block;
            width: 1px;
            height: 30px;
            position:absolute;
            left:50%;
            margin-top: -61px;
          }
        }
      }
      .b1 {
        margin-top:-2060px;
        br + h3 { margin-top:-10px; }
      }
      .b2 {
        margin-top:640px;
      }
      .b3 {
        margin-top:-990px;
      }
      .b4 {
        margin-top:1400px;
        margin-bottom: 30px;
        &:before {
          top:-5px!important;
        }
      }
      .b5 {
        margin-top:-260px;
      }

      .bloc:before {
        content:attr(data-text);
        top:-5px;
      }
      .demi1 {
        width:100%;
        height: 2000px;
        border-right: 0px;
        padding-right: 0px;
        text-align: center;
        margin-top: 36px;
        margin-bottom: 0;
        padding-top: 20px;
        .bloc:before {
          left:~"calc(50% - 30px)";
        }
      }
      .contain {
        border-top: 0px;
        top:-130px;
      }
      .demi2 {
        width:100%;
        height: 220px;
        margin-top: 0;
        padding-left: 0px;
        text-align: center;
        margin-left: 0;
        .bloc:before {
          left:~"calc(50% - 30px)";
        }
      }
      .tiers {
        max-width: 100%;
        margin-left: 0px;
        margin-bottom: 0px;
      }
      .bloc_bleu {
        margin-top: 50px;
      }
      .fid {
        width: auto;
        border-bottom: 1px solid @lf-blue;
        border-right: 0px;
        height:auto;
        margin-bottom: 70px;
        padding-bottom: 30px;
        margin-top: 0;
      }

    }

  }

  .cms-qui-sommes-nous {
    #header {
      background:  url(../images/cms/bgconcept.jpg) no-repeat center center / cover!important;
      background-position: 70% bottom!important;
      .bigtitre {
        width: 100%;
        left:0;
        top:102px;
        &:before {
          font-size:24px;
          white-space: normal;
        }
      }
    }
    .columns {
      margin-top: -150px;
      .comlumn.main {
        padding-bottom:0;
      }}.cms-left:not(.concept2) {
          margin-top:50px;
        }
    .cms-left,
    .cms-right {
      width: 100%;
      height: auto!important;
      max-height: none!important;
      float: none;
      padding-bottom: 50px;
      margin-bottom: 0px!important;
      h3 {
        font-size: 24px!important;
        padding:30px!important;
        margin-top: 0px!important;
        padding-top: 40px!important;
        margin-bottom: 20px;
      }
      div {
        margin:30px;
        width:~"calc(100% - 60px)";
      }
      button {
        margin-top: 30px;
        font-size: 16px;
      }
      &.w40 {
        width:100%;
        height: auto;
        padding-bottom: 440px;
        br {
          display: none;
        }
      }

      .centertext {
        font-size: 16px!important;
        min-width: auto!important;
        margin-bottom: 30px;
        text-align: left!important;
      }
      .centertext br, .centertext br + br {
        display: none;
      }
      .centertext br + span:not(.or) {
        display: block;
        & + br {
          display: block;
          height: 25px;
        }
      }

    }
    .cms-right.concept1 {
      margin-top: -20px;
      height: 100vw!important;
      margin-bottom: 0px!important;
    }
    .concept2 {
      display: none!important;
    }

  }
  .bloc_bleu {
    h3 br {display: none!important; }
  }
  .bloc_blanc {
    br {display: none;
      &+ br {
        display: block;
        height: 25px;
      }}
  }


  .pac-container.fixednoanim {
    position:fixed!important;
    top:190px!important;
  }

  #header {

    height:auto;
    background:@eatbeige;
    backface-visibility: hidden;
    top:0;
    z-index: 16;
    min-height: 500px;
    background-position: left!important;
    background-color: @eatbeige!important;
    .container {
      padding: 0;
      background:@eatbeige;
      position:fixed;
      width: 100%;
      z-index: 16;




      &.okdelivery {
        height: 93px;

        .delivery {
          transition: unset;
        }
        .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
          margin-top: -219px;
          margin-right: -13px;
          .smallcart {
            height:9px;
            background: none;
          }
          .overlaysmallcart {
            height: 70px;
          }
        }

        .delivery.fixednoanim {
          .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
            //margin-top: -211px;

          }
          .commande{
            display:none!important;
          }
        }
        .step1.OK{
          display:none!important;
        }


      }
      .links {
        position:absolute;
        background:@lf-blue;
        left: -300px;
        width: 300px;
        transition: .28s ease-in-out;
        text-align: left;
        padding-left:0;
        &.open {
          left:0;
          z-index: 15;
          border-right: 1000px solid rgba(0,52,86,0.8);
          border-left: 0;
          background-clip: padding-box;
          color:@lf-blue;
          bottom: 0;
          position: fixed;
          top: 0;
          padding-top: 140px;
          margin: 0;
          padding-left:0;
          a.identified {
            padding-top:10px!important;
            border-top: 12px solid @eatbeige!important;
            color:transparent!important;
          }
          a.identified:after  {
            background-color:@eatbeige!important;
            color:@lf-blue!important;
          }

          a.identified {
            &.f-20 {border-top: 10px solid #a3a198!important;   }
            &.f-20,
            &.f-20:after {
              background-color: #a3a198!important;
            }
            &.f-30 { border-top: 10px solid @lf-gold!important; }
            &.f-30,
            &.f-30:after {
              background-color: @lf-gold!important;
            }
          }
          a:last-child {
            padding:0;
            white-space: nowrap;
            position: fixed;
            top: 0;
            left: 0;
            color:@lf-blue;

            font-size: 17px;
            width:274px;
            height: 80px;
            padding-left:26px;
            background-color: @eatbeige!important;
            text-align: left;
            border-top: 20px solid @eatbeige;
            border-bottom: 10px solid @eatbeige;
            display: block;
            padding-top: 30px;

            img {
              display:none;
            }
            &:after {
              line-height:20px;
              top: 0;
              padding-top: 50px;
              background: none;
              color: @lf-blue;
              height: 70px;
              padding-left: 25px;
              font-size: 18px;
              width: 155px;
            }
          }
          .aspan {
            left:0;
            a.close {
              color:inherit;
              &:before {
                content:'Déconnexion';

                font-size:8px;
                padding-top:11px;
                display: inline-block;
                vertical-align: top;
                letter-spacing: 1px;
              }
              &:after {
                color:@lf-blue;
              }
            }
          }
        }
        .aspan {
          width: 300px;
          &.fixednoanim {
            position:fixed!important;
            top:50px!important;
            right:unset;

            padding:inherit;
            height: auto;
            padding-top: 5px;
            margin-left: 0px!important;
            &:after {
              line-height:inherit;
            }
          }

          left: -300px;

        }
        a {
          display:block;
          border:0;
          margin:20px;
          color:@lf-blue;
          padding-left: 6px;
          font-size: 14px;
          line-height: 18px;
          &.account:not(.identified) {
            color:@lf-blue!important;
          }
        }
      }
    }
    .div-logo {
        display: flex;
        width:~"calc(100% - 280px)";
        height: 58px;
        margin-left: 60px;
        margin-bottom: -3px;
        justify-content: center;
    }
    .logo {
        display: block;
        width: 200px;
        height: auto;
      background-image: url('@{baseDir}images/logob_new.svg')!important;
      border-left:0!important;
      background-size: 190px!important;
        margin-left: auto;
        margin-right: auto;

        &.fixednoanim {
            display: block;
        width:200px;
        max-width:~"calc(100% - 280px)";
        top:unset;
        left:unset;
            margin-top: 9px;
            height: auto;
            margin-left: -100px;
            margin-right: auto;
        background: transparent url('@{baseDir}images/logob_new.svg')!important;

            background-size: 190px!important;
        background-repeat: no-repeat!important;
      }
    }
    .menu  {
      display:block;
      background:@lf-blue;
      color:@eatbeige;
      text-transform: uppercase;
      font-family:'DinProBold';
      padding: 12px 20px;
      cursor:pointer;
      box-shadow: inset 0px 10px 10px -8px #656565;

      &:after {
        content: '';
        border-bottom: 2px solid @eatbeige;
        border-right: 2px solid @eatbeige;
        transform: rotate(45deg);
        width: 8px;
        height: 8px;
        display: block;
        float: right;
        margin-top: 5px;
      }
    }
    .burger {
      position: fixed;

      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      width: 60px;
      height: 50px;
      top: 0;
      left: 0;
      background-color:transparent;
      -ms-transform: rotate(0);
      -webkit-transform: rotate(0);
      transform: rotate(0);
      -webkit-transition: .28s ease-in-out;
      transition: .28s ease-in-out;
      display: table-cell;
      vertical-align: middle;
      margin: 0;
      z-index: 16;
      cursor:pointer;
      &.open {
        height: 51px;
        background: none;
        span:first-child {
          top: 24px;
          -ms-transform: rotate(135deg);
          -webkit-transform: rotate(135deg);
          transform: rotate(135deg);
        }
        span:nth-child(2) {
          opacity: 0;
          left: -60px;
          top: 24px;
        }
        span:nth-child(3) {
          top: 24px;
          -ms-transform: rotate(-135deg);
          -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
        }
        span {
          background:@lf-blue!important;
        }
      }
      div {
        position: relative;
        width: 20px;
        margin: 0 auto;
      }
      span {
        position: absolute;
        height: 3px;
        width: 20px;
        background-color: @lf-blue;
        opacity: 1;
        left: 0;
        -ms-transform: rotate(0);
        -webkit-transform: rotate(0);
        transform: rotate(0);
        -webkit-transition: .28s ease-in-out;
        transition: .28s ease-in-out;
        &:first-child {
          top: 16px;
        }
        &:nth-child(2) {
          top: 24px;
        }
        &:nth-child(3) {
          top: 32px;
        }

      }
    }
    .delivery, .delivery.fixednoanim {
      position: fixed;
      top:225px;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      margin:25px 13px;
      height: auto;
      width: ~"calc(100% - 26px)";
      background:none;
      left:auto;
      right: auto;

      .step0 {

        &:not(.OK) + .step1:not(.OK) {
          display: none!important;
        }
        .question {
          padding:1px 10px!important;
          margin-top:10px!important;
          z-index: 2;
        }
        .ok {
          height: 160px!important;
          &:before {
            background-position: 9px 0;
            width: 30px!important;
          }
        }
        .choix-type{
            height: 110px;
            z-index:1;
            padding-top:35px;
            top:0px;
            background:white;
            left:44px;
            width: ~"calc(100% - 45px)";
            .btn-radio {
              margin-left: 18px;
            }
        }
        .poplocalities a {
          cursor:pointer;
          display: inline-block;
          padding:3px;
        }

        .poplocalities {
          margin-left:52px;
          margin-top:40px;
          & + label + .poplocalities {
              margin-left:52px;
              margin-top:100px;
             & + label + .poplocalities {
              margin-left:52px;
              margin-top:1-0px;
            }
          }
        }

      }


      &:after {
        border-bottom:0;
      }

      .block-minicart .product-item-details {
        max-width: ~"calc(100% - 90px)";
      }
      .block-minicart .product-item-name {
        max-width: 100%;
      }
      .txt {
        margin:0;
        padding: 10px;
        position: relative;
      }
      &.okdelivery {
        margin-top:-80px!important;
        position:unset;
        background: none;
        border: 0px;
        transition:0s;
        .step1 {
          display:none;
        }
        .step1.OK {
          display:block!important;
        }
        .step3 {
          position: absolute!important;
        }
      }
      .nok, .check {
        width: ~"calc(100% - 60px)";
        background: white;
        padding: 15px 0px 15px 15px;
        margin: 0px;
        margin-top: 4px;
      }
      .check {
        left:-1px;
        width: ~"calc(100% - 28px)";
        top:71px;
        background: #fb7576;
        padding: 15px;
        border-radius:0;
        &:before {
          top:-6px;
          left:55px;
          z-index: -1;
        }

      }

      .oldAddresses {
        width: ~"calc(100% - 79px)";
        background: white;
        margin: 0px;
        margin-top: -8px;
        margin-left: 60px;
        position:absolute;
        cursor:pointer;
        > div {
          max-height:360px;
          overflow-x:hidden;
          overflow-y:auto;
        }
      }
      .step1.OK {
        display:none!important;
      }
      .step1:not(.OK) + .step2 {
        display:none;
      }

      .question {
        border: 0;
        padding: 3px 10px;
        margin-left: 50px;
        height: 10px;
        position: absolute;
        font-size: 16px;
        margin-top: 5px!important;
      }
      .adresse {
        width: ~"calc(100% - 113px)";
        padding-left: 5px;
        font-size: 14px;
        margin-left: 60px;
        margin-top: 23px;
        border-left: 0;
        margin-bottom: 20px;
        background: white;
      }
      .step1 {
        display: block!important;
      }

      .step1-cancel {
        display: inline-block;
        margin-top: 23px;

        .inner {
          display: flex;
          flex-direction: column;
          align-items: center;

          &:before {
            background: url(../images/edit.svg);
            content: '';
            width: 15px;
            background-size: 15px;
            height: 15px;
            display: inline-block;
            right: 0;
            top: calc(50% - 7px);
          }
        }
      }

      .step1, .step2 {
        background:white!important;
        width: 100%;
        height: auto;


      }
      .step1.OK:after {
        display: none!important;
      }
      .step0 .ok, .step1 .ok, .step2 .ok {
        position: absolute;
        zoom: .75;
        -moz-transform:scale(0.75);
        width: 60px;
        text-indent: -1000px;
        color: @lf-gold;
        padding-top: 33px;
        height: 67px;
        display: inline-block!important;
        margin-top:0px;
        left: -1px;
        padding-left: 0;

        &:before {
          margin-right:0;
          width: 30px;
          height: 27px;
          background-size: 20px;
          background-position: 9px 0;
          margin-left: 10px;
        }
      }
        .step0 .ok{
            margin-top:0px;
            height: 100%;
            box-sizing: content-box;
        }
        @supports (-moz-appearance:none) {
            .step0 .ok {
                box-sizing: content-box;
                margin-top: -24px;
                left: -8px;
            }
        }
        .step1 .ok{
            margin-top: 0px;
            left: -8px;


        }
        @supports (-moz-appearance:none) {
            .step1 .ok {
                margin-top: -13px;
            }
        }
        .step2 .ok{
            margin-top:-134px;
            left: -8px;
            height: 100%;
            box-sizing: border-box;
        }
        @supports (-moz-appearance:none) {
            .step2 .ok {
                box-sizing: content-box;
                margin-top:-117px;
            }
        }

      .btn-radio {
        margin-left:48px;

        width: ~"calc(100% - 90px)";
        display: inline-block;
        float: left;
        clear: both;
        margin-top: 3px;
        margin-bottom: 10px;
        border-bottom: 1px solid #d9d9d9;
        border-radius: 0;
        padding-bottom: 14px;
        padding-left: 12px;
        &:last-of-type {
          border-bottom: 0px;
        }
      }
      .step2 {
        background: white;
        margin-top: 0px;

        .ok {
        }

        &.OK {

          &[style="display: none;"] ~ .step1.OK {
            display:none!important;
          }
        }

        .ok:before {
          width: 32px;

          background-size: 31px;
          background-position: center;
          height: ~"calc(100% - 40px)";
          margin-left: 0px;
        }
        .question{
          margin:0;
          vertical-align: top;
          float: left;
          margin-left: 50px;
        }
        .choix-livraison {
          height: 60px;
          position: relative;
          top:unset;
          display:block!important;
          clear:both;
          margin-top: 40px;
          span {
            position: fixed;
            right: 20px;
            margin-top: -20px;
          }

          label + .advice {
            height: 61px;
            margin-top: 44px!important;
            margin-left: 43px;
            width: ~"calc(100% - 143px)";
            &:after {
              top:-5px;
              left:20px;
              margin-top: 0!important;
            }
          }
        }
        .creneau1, .creneau2, .creneau3 {
          position: fixed;
          top: 209px;
          margin-left: 45px;
          width: ~"calc(100% - 93px)";
          .tooltip {
            width: 100%;
            margin-top:-5px;
            &:before {
              margin-left: 9px;
            }
          }
        }
        .creneau2 {
          top: 262px;
        }
        .creneau3 {
          top: 318px;
        }
        &.noToday {
          .creneau2 {
            top: 209px;
          }
          .creneau3 {
            top: 263px;
          }
        }
        &.noTomorrow {
          &.noToday {
            .creneau3 {
              top: 192px !important;
            }
          }
          .creneau3 {
            top: 244px !important;
          }
        }



      }



      .step1.OK + .step2.OK + .step3 {
        padding: 10px;
        width: ~"calc(100% - 20px)";
        left:0;
        height: 170px;
        margin: 0;
        top:0;
        background: white;
        padding-bottom: 0;
        .step {
          display:none;
        }
        .smallcart{
          opacity:1;
          position: fixed;
          top: 0;
          right: 0;
          margin: 0;
          padding: 10px 0 9px;
          border-radius: 0;
        }
        .overlaysmallcart {
          height:70px;

        }
        .question {
          float: left;
          position: relative;
          margin-left: 0;
          display: block;
          width: 100%;
        }
        .resetPopin {
          top: 167px;
          width: 100%;
          right: 0px;
          span {
            width: auto;
          }
          &._place {
            top: 181px;
          }
        }
        .shippingPopin {
            width: 350px;
            top: 173px;
        }

        .commande {
          margin:0;
          line-height: 14px;
          font-size: 13px;
          margin-top: 6px;
          height: auto;
          padding-bottom: 4px;
          width: 100%;
          transition: 0.5s all ease;
          top:0;
          opacity:1;
          .question {
            margin-top:10px!important;
          }
          &.hidden {
            top:-100px;
            position:absolute;
            opacity:0;
          }
          .picto {
            display:none;
          }
          b {
            width: 90px;
            min-width: 90px;
            float: left;
          }
          #_when {
            border-bottom:0;
            border-bottom: 0;
            padding-bottom: 10px;
          }
          .grey.blue {
            display: block;
            float: left;
            width:100%;
            margin-bottom: 6px;
            border-left:0px;
            padding-left: 10px;
            padding-top: 15px;
            max-width: ~"calc(100% - 35px)";
            border-bottom: 1px solid #d9d9d9;
            padding-right: 25px;
            padding-bottom: 15px;
            top: unset;
            &:after {
              right:8px;
            }
          }


          #_place span:before,
          #_when span:before {
            position: absolute;
            top:-12px;
            right: 60px;
          }
          #_when span:before {
            right: 113px;
          }
          #_place span:after,
          #_when span:after {
            right: 8px;
            top: -12px;
          }
          #_when span ,
          #_place span {
            width: 100%;
            height: 18px;
          }
        }
      }
    }
    .delivery.fixednoanim {
      position: relative;
      top:-10px;

      & + .menu {
        top: 52px;
        position: fixed;
        right: 0;
        left: 0;
        & + .bigtitre {
          visibility: hidden;
        }
      }
      .creneau1, .creneau2, .creneau3 {
        .tooltip {
          margin-top:15px!important;
        }
      }
      .step3 {
        border:0px;
        top: -500px!important;
      }

      .creneau1 {
        top: 259px!important;
      }
      .creneau2 {
        top: 312px!important;
      }
      .creneau3 {
        top: 368px!important;
      }
      .noTomorrow {
        .creneau3 {
          top: 312px!important;
        }
      }
      .noToday {
        .creneau2 {
          top: 259px!important;
        }
        .creneau3 {
          top: 312px!important;
        }
        &.noTomorrow {
          .creneau3 {
            top: 259px!important;
          }
        }
      }
    }
  }
  .filtres.fixednoanim + .recap_filtres + .categorie {
    position: relative;
    z-index: 15;
    & ~ .categorie {
      position: relative;
      z-index: 15;
    }
  }
  .filtres .filtre:not(.selected):hover:after {
    background: none;
  }
  .filtres.fixednoanim .filtre.last  {


    width: ~"calc(100% - 60px)!important";

    &.selected  {
      width: ~"calc(100% - 70px)!important";
    }
    &.clickable {
      width: ~"calc(100% - 70px)!important";
    }
    .tooltip .item:after {
      right:-10px!important;
    }

  }
  .filtres .filtre.last  {

    &.selected  {
      width: ~"calc(100% - 10px)!important";
    }
    &.clickable {
      width: ~"calc(100% - 10px)!important";
    }
    .tooltip .item:after {
      right:0px!important;
    }

  }
  .filtres.fixednoanim .container {
    box-shadow: none!important;

  }
  .produits .filtres,  .produits .filtres.fixednoanim {
    position: fixed;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    width: 100%;
    background: white;
    transition: .28s ease-in-out;
    height: auto!important;
    top: -1012px!important;
    padding-top:0;
    z-index: -1;
    box-shadow: 0px 4px 15px -2px #656565;
    padding-bottom: 20px;

    .container {
      background:none;
      padding: 15px 30px;
      display: block!important;

      box-shadow: inset 0px 10px 10px -8px #656565;
      height:280px!important;
      .inside {
        display:block!important;
      }
    }
    &.open,  &.fixed {
      opacity:1;
      top:92px!important;
      z-index: 16;
      border-top: 1px solid #f7F7F7!important;
      bottom: 0px;
      overflow-y: auto;
      overflow-x: hidden;
    }
    &.hasSelected .filtre {
      padding-left: 30px;
    }
    .filtre {
      font-size: 16px!important;
      padding: 8px 0!important;
      display: block;
      clear: both;
      text-align: left;
      &:hover {
        background: none!important;
        color:@lf-blue!important;
      }

      &.last:before {
        border-left:0!important;
      }
      &.last {
        color:@lf-blue!important;
        border-top: 1px solid @eatbeige;
        margin-top: 15px;
        padding-top:15px!important;
        width: 100%;
        transition:unset!important;
        &.selected:hover .tooltip {
          opacity:1;
        }
        &:not(.selected):hover {
          background: none!important;
          color:@lf-blue!important;
          transition:unset!important;
          &:after {
            background: none!important;
          }
          .f1 {
            display:inline;
          }
          .f2 {
            display:none;
          }
        }
        &.clickable  {
          background: none!important;
          transition:unset!important;
          .f1 {
            display:inline;
          }
          .f2 {
            display:none;
          }
        }
      }
      &.last .tooltip {
        position: unset;
        border: 0;
        background: none!important;
        margin: -22px!important;
        margin-top: 0!important;
        width: 100%;
        display: block!important;
        opacity:1!important;
        ul {
          padding:0;
        }
        h5 {
          font-size: 16px;
          padding:20px 10px;
          color:@lf-blue!important;
        }
        &:before {
          display:none;
        }
        .item {
          padding:6px 18px 10px;
          width: 100%;
          font-size: 16px;
          height: 22px;
          color:@lf-blue!important;
          &:after {
            right:0;
            height: 24px;
            width: 24px;

          }
          &.selectedfilter:hover {
            color:@lf-blue;
            &:after {
              outline:6px solid @lf-blue;
              outline-offset: -13px;
            }
          }
          &.selectedfilter::after {
            outline:6px solid @lf-blue;
            outline-offset: -13px;
          }
          &[data-content='veggie'] {
            color:#2a8e50!important;
            &.selectedfilter:after  {
              outline: 6px solid #2a8e50;
            }
          }
          &:hover {
            background:none;
          }
        }
      }

      &.selected {
        transition:unset!important;
        padding-left: 10px !important;
        padding-right: 10px !important;
        margin-left: -10px;
        &.last {
          width: 100%;

          &.clickable  {
            background: none!important;
            transition:unset!important;
            .f1 {
              display:inline-block;
            }
            .f2 {
              display:none;
            }
          }


        }

        &.last:after {
          background: none!important;
          color:@lf-blue!important;
          transition:unset!important;
        }

        &:after {
          width: 100%;
          opacity: 1!important;
        }
        &:hover:after {
          width: 100%;
          opacity: 1!important;
        }
      }
      &.selected.clickable .tooltip {
        display:block!important;
      }
      &.clickable {

        padding-left: 10px !important;
        padding-right: 10px !important;
        margin-left: -10px;
        transition:unset!important;
        .tooltip {
          display:block;
          opacity:1;
          transition:0.2s all ease;
          padding-bottom:60px;
        }


        &.last:after {
          background: none!important;
          color:@lf-blue!important;
        }

        &:after {
          background:@lf-gold!important;
          width: 100%;
          opacity: 1!important;
        }
        &:hover:after {
          background:@lf-gold;
          width: 100%;
          opacity: 1!important;
        }
      }
    }
  }

  .fixednoanim .filtre.last .tooltip .item:after {
    right:0px!important;
    position:relative;
    float:right;
    transition:unset!important;
  }
  .recap_filtres {
    display:none;
  }

  .produits .categorie .container .produit.added:hover div.visu div:before {
    content: attr(data-count)!important;
    font-family:inherit!important;
    font-size:inherit!important;
    background:none;
    color:@lf-blue!important;
  }
  .produits .categorie .container .produit.added:hover div.visu div:after {
    display:none;
  }

  .produits {
    h2 {
      margin-bottom: 0!important;
      margin-top: 30px;
    }
    .categorie .container .produit.added:hover div.visu div:before {

      content: attr(data-count);
    }
    .categorie .container .produit {
      width: 48%;
      height: ~"calc(50vw + 115px)";
      margin-top:20px;
      .smalldesc + hr {
        display: none;
      }
      &:hover {
        margin-top:17px!important;
      }
      .smalldesc {
        overflow: hidden;
        white-space: normal;
        line-height: 20px;
        margin-top: 5px;
      }
      label {

        top: -10px;
        img {

        }
      }
      &.added div.visu div {
        margin-top: -65%!important;
        content: attr(data-count);
      }
      h4 {
        height: 30px;
        margin-top: 10px;
        font-size:14px;
        overflow: hidden;
      }
      .desc {
        display:none;
      }
      .content {
        height:120px;
      }
      div.visu {
        padding-top: 100%;
        overflow: hidden;
        height: auto;
        position: relative;
        img.prod {
          min-width: 133%!important;
          height: 100%!important;
          position: absolute;
          top: 0;
          left:-17%;
        }
      }
      &.cms {
        width: ~"calc(48% - 15px)";
        padding-left:15px;
        display:none;
        &:before {
          transform: scale(0.6);
          margin-left: -67px;
        }
        h3 {
          font-size: 16px;
          min-width: unset;
        }
        &:hover {
          width: ~"calc(48% - 15px)";
          padding-left:15px;
        }
      }
      .pic {
        display: none;
      }
      .addtocart {
        font-size:20px;
        white-space:nowrap;
        img {
          margin:0;
        }
        div.ttc, div.ht {
          transition:all 0.5s ease;
          z-index: 4;
          zoom: 0.75;
          -moz-transform:scale(0.75);
        }

        &.prixbarre {
          &:before {
            margin-top:0px;
          }
          div.ttc, div.ht {
            top:-26px;
          }
          div.barre {
            top: -10px;
            zoom: 0.6;
            -moz-transform:scale(0.6);
          }
        }
        div.ht {
          margin-left:5px;
        }
        span {
          vertical-align: super;

          &.tax {

            margin-left: 3px;
            width: 30px;
            display: inline-block;
            font-size:11px;
            top: 0px;
            position: absolute;
            left: 50px;
            color:#999;
            height: 19px;
            cursor:pointer;
            font-family:'DinPro';
            opacity:1!important;
            &:hover {
              text-decoration:underline;
            }
            &.selected {
              text-decoration:none!important;
              font-family:'DinProBold';
              color:@lf-blue;
            }
            & + .tax {
              top: 12px;
              left:50px;
            }
          }
        }
        .quantity {
          position: absolute;
          right: 0;
          bottom: 10px;
          div  {
            position: relative;
            z-index: 2;
            margin-left: -10px;
            margin-right: 5px;

            background:@lf-blue;
            border-radius: 20px;
            color:white;
            width: 36px;
            &.plus {
              margin-top: -90px;
            }
            &.moins {
              margin-right: -26px;
            }
          }
          input {
            display: none;
          }
        }
      }
    }
  }

  .productlayer.shown, .productlayer._show {
    z-index: 20;
    top:0;

    .close {
      right: -5px;
      top: -5px;
      transform: scale(0.6);
      z-index: 12;
    }
    .cross {
      display:none;
    }
    hr {
      height: 1.2px;
    }
    .jssorb051 {display:block;position:relative;cursor:pointer; top: 88%; }
    .jssorb051 .i {position:relative }
    .jssorb051 .i .b {fill:#fff;fill-opacity:0.5;stroke:#000;stroke-width:400;stroke-miterlimit:10;stroke-opacity:0.5;}
    .jssorb051 .i:hover .b {fill-opacity:.7;}
    .jssorb051 .iav .b {fill-opacity: 1;}
    .jssorb051 .i.idn {opacity:.3;}

    .gzoom {
      margin:0;
      width:unset;
      height: unset;
      .added:first-child {
        background:@lf-blue;
        &:before {
          position: absolute;
          display: inline-block;
          width: 70px;
          text-align: center;
          margin-top: 25vw;
          font-family: 'DinProBold';
          font-size: 50px;
          background: white;
          border-radius: 200px;
          opacity: 1;
          margin-left:~"calc(50% - 35px)";
          line-height: 64px;
          transition: 0.2s all ease;
          height: 70px;
          letter-spacing: -1px;

          content: attr(data-count);
          color: @lf-blue;

        }
        .slides img {
          opacity:0.3;
        }
      }
      img.big, img.vignette {
        left:0!important;
        top:0!important;
        right:0!important;
        height:auto!important;
        width:100%!important;
        position: relative!important;
      }
      .slides {
        position: absolute; left: 0px; top: 0px; width:100%; overflow: hidden;
      }
    }
    .produit {
      margin: 0;
      margin-top: 10px;
      width: 100%;
      zoom: 0.7;
      -moz-transform:scale(0.7);
      position: fixed;
      left: 0;
      top: 45%;
      bottom: 0;
      overflow-y: auto;
      padding-bottom: 10px;

      .addtocart {
        height:32px;
        img {
          right:5px;
        }
        .quantity input {
          height:26px;
        }
      }
      .content {
        max-height: none!important;
      }
    }
  }
  .pac-container {
    margin-left: -15px;
    width: ~"calc(100% - 71px)!important";
  }

  .form.password.forget {
    padding:20px;
  }
  .customer-account-logoutsuccess,
  .customer-account-login,
  .customer-account-forgotpassword {
    #header .menu {
      display:none!important;
    }
  }


  .changeAdress .action-close {
    top:10px;
    right:10px;
  }

  .cart-container .action.update {
    position:absolute;
    width:~"calc(100% - 40px)";
    height:55px;
    color:@lf-blue!important;
    z-index:-1;
    opacity:0;
    pointer-events:none;
    &.visible {
      z-index:2;
      opacity:1;
      pointer-events:all;
    }

  }
}



.fidelite {
  span {
    display:inline-block;
    font-weight: bold;
    &:after {
      font-size:18px;
      display: inline-block;
      vertical-align: top;
      line-height: 18px;
      margin-left:5px;
    }
  }
  &.f-10 span {
    color: #71662b!important;
    &:after {
      content:' \2605';
    }
  }
  &.f-20 span {
    color: #a3a198!important;
    &:after {
      content:' \2605\2605';
    }
  }
  &.f-30 span {
    color: @lf-gold!important;
    &:after {
      content:' \2605\2605\2605';
    }
  }
}


.iwd_empty_cart_powered_by {
  display:none;
}







// petits telephones (iphone 5)

@media only screen and (min-width: 300px) and (max-width: 374px) {


  #header .delivery {
    &.fixednoanim {
      top:0px;
      & + .menu {
        top:50px;
      }
    }
    .adresse {
      font-size: 11px!important;
    }
  }
  .produits .categorie .container .produit .addtocart div.ttc,
  .produits .categorie .container .produit .addtocart div.ht {
    zoom: 0.62;
    -moz-transform:scale(0.62);
  }

}
@media only screen and (min-width: 300px) and (max-width: 765px) {

  .account .block-addresses-list .items.addresses > .item {
    width: auto!important;
  }
}
//ipad

@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
  body, html {
    zoom:0.75;
  }
  #header .container .links a {
    font-size:9px!important;
  }
  .commande .grey.blue + .question + .grey.blue {
    max-width: 376px;
  }
  .fixednoanim.commande .grey.blue + .question + .grey.blue {
    max-width: 208px;
  }
  .account .filtres {
    display: none;
  }
}

// grands ecrans

@media screen and (min-width: 1000px) {

  .step1-cancel {
    cursor: pointer;
    display: inline-block;
    margin-top: 36px;

    &:hover {
      text-decoration: none;
    }

    .inner {
      display: flex;
      align-items: center;

      &:before {
        background: url(../images/edit.svg);
        content: '';
        width: 15px;
        background-size: 15px;
        height: 15px;
        display: inline-block;
        right: 0;
        top: calc(50% - 7px);
        margin-right: 7px;
      }
    }
  }


  header#header .logo.fixednoanim {
  }
  .block-addresses-list .items.addresses > .item,
  .account .column.main .block:not(.widget) .block-content .box   {
    background:rgba(240,240,240,0.6);
    width: ~"calc(39% - 40px)"!important;
    padding:20px;
    height:175px;
    padding-top:  10px;
    &.box-order-billing-address,
    &.box-order-billing-method {
      height: 235px;
    }
  }
  .account .column.main .block-dashboard-addresses:not(.widget) .block-content .box   {
    height:220px;
  }

  .account .column.main .systempay-conditions {
    margin-left: 10%;
    text-align: center;
    width: 49% !important;
  }

  .account .column.main .block.block-new-alias {
    margin-bottom: 0;
  }

  .account .column.main .block:not(.widget).block-aliases  .block-content .box,
  .account .column.main .block:not(.widget).block-new-alias  .block-content .box   {
    width: ~"calc(49% - 40px)" !important;
  }
  .account .column.main .block:not(.widget).block-new-alias .block-content .box   {
    height:300px;
  }
  .account .column.main .block:not(.widget).block-addresses-list {
    margin-left:  0;
  }

  .block-addresses-list .items.addresses > .item {
    margin-left:  10%!important;
    height:135px;
    &:nth-child(even) {
      width: ~"calc(39% - 30px)"!important;
    }
  }
  .table-wrapper.orders-history table {
    background:rgba(240,240,240,0.6);
  }
  .order-details-items {
    background:rgba(240,240,240,0.6);
    padding:5px 15px;
  }

  .block-order-details-view .box:nth-child(even) {
    margin-left: 10%!important;
  }

  .account-nav .content {
    border:1px solid @lf-blue;
    border-bottom:  2px solid @lf-blue!important;
    border-right: 2px solid @eatred!important;
  }
  div.mage-error {
    margin-bottom: 0px;
  }
  .account-nav .item.current a:hover {
    background:@lf-blue;
  }

  .block.block-customer-login,
  .block.block-new-customer {
    height: 400px;

  }
  .account .filtres {
    display: none;
  }
  .block-authentication .login .actions-toolbar > .secondary  {
    margin-top: -50px!important;
  }


  body:not(.cms-home):not(.cms-page-view):not(.contact-index-index) {
    #header {
      height: 90px!important;
      background:none!important;
    }
    .bigtitre {
      display:none;
    }
    .aspan, header .logo {
      position:fixed!important;
      top:0!important;
    }
    .delivery {

      position:fixed!important;
      margin-top:0px!important;
      top:0!important;
      transition:none!important;
      height: 91px!important;
      background:#f7f7f7!important;

      .loader {
        background:#f7f7f7!important;
      }


    }
  }
}


@media screen and (min-width: 1000px) and (max-width: 1100px) {
  body, html {zoom:0.848;}
  .produits .filtres .filtre {
    font-size:12px;
  }

}

@media screen and (min-width: 1100px) and (max-width: 1200px) {
  body, html {zoom:0.9;}

}
@media screen and (min-width: 1200px) and (max-width: 1330px) {
  body, html {zoom:0.949;}

}



@media screen and (min-width: 1000px) and (max-width: 1450px) {

  header .container .delivery:not(.fixednoanim) .commande  #_place {
    max-width: 380px;
  }
  header .container .delivery:not(.fixednoanim) .commande  #_when {
    max-width: 350px;
  }

  header .container .delivery.fixednoanim .commande {
    #_place span:before,
    #_when span:before {
      position: absolute;
      top:40px;
    }
    #_when span:before {
      left:18px;
      right:unset;
    }

    #_when span ,
    #_place span {
      width: 100%;
    }
    #_when span:after,
    #_place span:after {
      position: absolute;
      top:40px;
      right: 75px;
    }
    #_when span:after {
      right: 80px;
    }
    .grey.blue {
      line-height: 40px!important;
    }
  }


  header .container .delivery.fixednoanim {
    max-width: 970px;
  }
  .fixednoanim .commande {
    max-width: 900px;
    margin-top: 2px;
  }
  .fixednoanim .commande .grey.blue {
    max-width: 250px;
    line-height: 40px;
  }
  .fixednoanim .commande .grey.blue+.question+.grey.blue {
    max-width: 230px!important;
  }

}

@media screen and (min-width: 1451px) and (max-width: 1550px) {
  header .container .delivery:not(.fixednoanim) .commande  #_place {
    max-width: 380px!important;
  }
  header .container .delivery:not(.fixednoanim) .commande  #_when {
    max-width: 350px!important;
  }

  header .container .delivery.fixednoanim .commande {
    #_place span:before,
    #_when span:before {
      position: absolute;
      top:40px;
    }
    #_when span:before {
      left:18px;
      right:unset;
    }
    #_when span ,
    #_place span {
      width: 100%;
    }
    #_when span:after,
    #_place span:after {
      position: absolute;
      top:40px;
      right: 125px;
    }
    #_when span:after {
      right: 130px;
    }
    .grey.blue {
      line-height: 40px!important;
    }
  }
  header .container .delivery.fixednoanim {
    max-width: 1070px;
  }
  .fixednoanim .commande {
    max-width: 1000px;
    margin-top: 2px;
  }
  .commande .grey.blue {
    max-width: 300px;
  }
  .fixednoanim .commande .grey.blue {
    max-width: 300px;
  }
  .fixednoanim .commande .grey.blue+.question+.grey.blue {
    max-width: 300px !important;
  }

  .resetPopin {
    width: 615px!important;
    &._place {
      width: 434px!important;
    }
  }


}


@media screen and (min-width: 1551px) and (max-width: 1600px) {

  header .container .delivery:not(.fixednoanim) .commande  #_place {
    max-width: 380px!important;
  }
  header .container .delivery:not(.fixednoanim) .commande  #_when {
    max-width: 350px!important;
  }

  header .container .delivery.fixednoanim .commande {
    #_place span:before,
    #_when span:before {
      position: absolute;
      top:40px;
    }
    #_when span:before {
      left:18px;
      right:unset;
    }
    #_when span ,
    #_place span {
      width: 100%;
    }
    #_when span:after,
    #_place span:after {
      position: absolute;
      top:40px;
      right: 165px;
    }
    #_when span:after {
      right: 190px;
    }
    .grey.blue {
      line-height: 40px!important;
    }
  }


  header .container .delivery.fixednoanim {
    max-width: 1170px;
    right:187px;
  }

  .fixednoanim .commande .grey.blue {
    max-width: 340px;
    line-height: 40px;
  }
  .fixednoanim .commande .grey.blue+.question+.grey.blue {
    max-width: 360px!important;
  }
}


@media  screen and (min-width: 1330px) {
  body, html {zoom:1;}
  .tooltip .item.btn-success3d + span:hover:after { right:7.6%; }
  .produits .filtres .filtre {  padding: 22px 17.7px; }

}


@media screen and (min-width: 1601px)  and (max-width: 1910px) {

  .gzoom {
    margin-top:-150px;
  }
  .jssorb051 {
    margin-top:-80px;
  }
  .productlayer .modal-inner-wrap .produit {
    height:~"calc(100% - 465px)";
    margin-top:-200px;
  }
  .productlayer .modal-inner-wrap .cross {
    height:~"calc(100% + 93px)";
  }
}

@media screen and (min-width: 1601px) and (max-width: 1910px) {
    .gzoom {margin-top: 0px;}
    .productlayer .modal-inner-wrap .produit {height: calc(100% - 10px); margin-top: 0px;}
    .productlayer .modal-inner-wrap .produit .content {top: 0px!important;}
}

@media screen and (min-width: 1601px) {

  header#header .logo.fixednoanim {

    width:15%!important;
    max-height:70px!important;
    border-top:7px solid @eatbeige;
    max-width: 250px!important;
    border-bottom: 17px solid @eatbeige;
    border-left:2em solid @eatbeige!important;
    margin-left: 0px;
    background-size: 100%;
  }
  header#header .logo  {
    margin-left: 30px;
  }


  #header .container .delivery.fixednoanim {
    width: ~"calc(100% - 508px)";
    right:231px;
    max-width:none;
    .commande .grey.blue {
      max-width:310px!important;
      & +.question + .grey.blue {
        max-width: ~"calc(100% - 612px)!important";
      }
    }

  }
  .tooltip .item.btn-success3d + span:hover:after {
    right:7.6%;
  }

  #header .container .delivery {
    width:1440px;
    .commande .grey.blue {
      max-width:490px!important;
      & +.question + .grey.blue {
        max-width:480px!important;
      }
    }
    .resetPopin {
      width:665px;
    }
  }
    #header .container .fixednoanim {
        .resetPopin._place {
            position: absolute;
            width:480px!important;
            right: unset;
            left: -15px;
        }
    }

  #header .container .links .aspan {
    width: 232px!important;
  }
  #header .container .links .aspan.fixednoanim {
    width: 231px!important;
  }
  #header .container .links a:last-child,
  #header .container .links.fixednoanim {
    width: 205px!important;
    text-align: left;
  }


}

@media screen and (min-width: 1800px) {


  h2 {
    font-size: 33px!important;
    letter-spacing: 5px;
  }
  .commande {
    width: ~"calc(100% - 260px)!important";

    .grey.blue {
      font-size: 17px;
    }
  }
  .smallcart {
    width:242px;
    margin-top: -1px;
    .total {
      font-size:30px;
      span {
        font-size:15px;
      }
    }
    img {
      width:30px;
    }
    .count {
      font-size: 20px;
      height: 32px;
      width:34px;
      border-radius: 22px;
    }

  }
  .minicart-wrapper .action.showcart {
    margin-top: -4px;
  }
  .produits{
    h2 {
      font-size: 33px;
    }
    .categorie .container{

      .produit {
        height:515px;
        .desc {
          height: 62px;
          overflow:hidden;
        }
        &.cms h3 {
          font-size:32px;
        }
        .pic img {
          width: 30px;
        }
        h4 {
          padding-right: 10px;
        }
        .no-delivery h5 {
          font-size:13px;
        }
        .no-delivery img  {
          margin-top:3px;
        }
        .addtocart div.plus {
          margin-right: 13px;
        }
      }
    }
    .filtres .filtre {
      font-size:14px;
      padding: 20px 17.7px 21px;
      &.last {
        margin-left:22px;
      }
    }

  }
  #header .container .links a:last-child img {
    height:30px;
    margin-right: 20px;
    margin-left: 10px;
  }
  .oldadresses  {
    margin-left:312px;
  }

  #header .container .delivery {
    .question {
      font-size: 20px;
    }
    .step2 {
      .question {
        padding-right: 8px;
      }
      .choix-livraison label {
        font-size: 17px;
      }
      .creneau2 {
        padding-left:10px;
      }
      .creneau3 {
        padding-left:17px;
      }
    }
    .commande .grey.blue {
      max-width:400px!important;
      & +.question + .grey.blue {
        max-width:450px!important;
      }
    }
    .resetPopin  {
      width:559px;
      right: 242px;
    }
  }
  #header .container .delivery.fixednoanim {

    .block-minicart {
      margin: -1px 17px;
    }

    .resetPopin  {
      width: ~"calc(100% - 851px)!important";
    }

    .commande .grey.blue {
      max-width:350px!important;
      & +.question + .grey.blue {
        max-width: ~"calc(100% - 722px)!important";
      }
    }


  }

}

// fix iphone zoom on inputs & scroll
@media only screen and (-webkit-min-device-pixel-ratio:2) {

  .noscroll { overflow: hidden; }
  @supports (-webkit-overflow-scrolling: touch) {
    #header .delivery .step1 .adresse,
    #header .delivery .step1 .adresse:focus,
    #header .delivery .step1 .adresse::placeholder,
    select:focus,
    textarea:focus,
    input[type='text']:focus,
    input[type='text']  {
      font-size: 16px!important;
    }
  }
}
@media only screen and (min-device-width : 300px) and (max-device-width : 320px)   {
  #header .delivery  input.adresse,
  #header .delivery  input.adresse::placeholder {  font-size: 11px!important;line-height: 11px;white-space: normal; }
  #header .delivery  input.adresse {margin-top:13px; height: 42px;}
  #_place span:before,
  #_when span:before {
    font-size: 11px!important;
  }
}
@media only screen and (min-device-width : 321px) and (max-device-width : 359px)   {
  #header .delivery  input.adresse,
  #header .delivery  input.adresse::placeholder {  font-size: 12px!important;line-height: 12px;white-space: normal;}
  #header .delivery  input.adresse {margin-top:13px; height: 42px;}
  #_place span:before,
  #_when span:before {
    font-size: 12px!important;
  }
}
@media only screen and (min-device-width : 360px) and (max-device-width : 370px)  {
  #header .delivery  input.adresse,
  #header .delivery  input.adresse::placeholder {  font-size: 13px!important;line-height: 13px;white-space: normal;}
  #header .delivery  input.adresse {margin-top:13px; height: 42px;}
}
@media only screen and (min-device-width : 370px) and (max-device-width : 390px)  {
  #header .delivery  input.adresse,
  #header .delivery  input.adresse::placeholder {  font-size: 14px!important;line-height: 14px;white-space: normal;}
  #header .delivery  input.adresse {margin-top:13px; height: 42px;}
}
@media only screen and (min-device-width : 391px) and (max-device-width : 415px)  {
  #header .delivery  input.adresse,
  #header .delivery  input.adresse::placeholder {  font-size: 15px!important;line-height: 15px;white-space: normal;}
  #header .delivery  input.adresse {margin-top:13px; height: 42px;}
}

//ipad

@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
  body, html {
    zoom:0.75;
  }

  .eye { top:18px!important; }
  .control input, select {
    height: 54px!important;
    line-height:30px!important;
  }
  .form.create.account fieldset label.label {
    font-size: 14px;
  }
  .login-container {
    max-width: 1700px;
  }
  .fieldset > .field:not(.choice) > .label {
    width: 35.8%;
  }

  footer .prefooter {
    white-space:nowrap;
    font-size: 12px;
    a.sep {
      margin: 0 80px 0 0;
      &:after {
        margin-left: 60px;
      }
    }
  }

  .cart.table-wrapper .cart.items .col .product-item-details .smalldesc {
    font-size: 12px;
  }

  .modal-slide.parrain .action-close  {
    margin-top:-30px;
  }
  .modal-slide .action-close:before {
    font-size: 40px!important;
    text-indent:-5px;
  }
  .step3 .commande {
    width: ~"calc(100% - 255px)";
  }
  .cartOverlay:after,
  .cartOverlay:before {
    display:none!important;
  }
  .produits .categorie .container .produit.added.hovered div.visu div.cartOverlay:before,
  .produits .categorie .container .produit.added:hover div.visu div.cartOverlay:before,
  .produits .categorie .container .produit.added div.visu div.cartOverlay:before {
    font-size:50px!important;
    line-height:50px!important;
    display: block!important;
    content: attr(data-count)!important;
    background:none;
    color:@lf-blue!important;
  }
  .bigtitre:after {
    line-height:34px!important;
  }
  header .container .delivery:not(.fixednoanim) .commande  #_when {
    max-width: 310px!important;
  }

  header .container .delivery .step0 {
     #type-1.poplocalities {
          margin-top:50px;
        }

       #type-2.poplocalities {
          margin-top:50px;
        }
        .ok {
          font-size: 10px;
          letter-spacing:2px;
        }
       .ok:before {
      margin-left: 15px;
      margin-right: -15px;
    }
  }
  #_place span:before,
  #_when span:before {
    position: absolute!important;
    top:35px!important;
    left: 20px!important;
    font-size: 10px!important;
  }
  #_when span:before {
    right: 113px!important;
  }
  #_place span:after,
  #_when span:after {
    left: 230px!important;
    top: 33px!important;
    font-size: 10px!important;
    right:unset!important;
    padding:3px!important;
  }
  #_when span:after {
    left: 150px!important;
    right:unset!important;
  }

  #_when span ,
  #_place span {
    width: 100%!important;
    height: 18px!important;
    line-height: 16px;
  }
  #_when:after,
  #_place:after {
    top:2px!important;
    height:15px;
  }
  .fixednoanim .commande .grey.blue + .question + .grey.blue {
    max-width: 330px;
  }

  .hilite {
    padding:7px 5px;
  }
  .modal-slide .action-close {
    padding:40px 60px;
    &:before {
      margin-right:-20px;
    }
  }
  .parrainPopup .bloc {
    width: 370px;
  }

  .parrainPopup .head {
    color:black;
    line-height: 20px;
  }

  .bloc_bleu {
    max-width: 1200px;
    .btn {
      width: 450px;
    }
  }
  .cms-devenir-partenaire .columns   {
    max-width: 2000px;
  }
  .cms-qui-sommes-nous .columns   {
    height: 2700px;
    max-height: 2700px;
  }
  .cms-qui-sommes-nous .columns h3 {
    font-size: 18px;
  }
  .cms-comment-ca-marche .columns .demi2,
  .cms-comment-ca-marche .columns .demi1 {
    zoom:1.33;
    width:450px;
    height: 1940px;
  }

  .cms-comment-ca-marche .columns .demi1 .bloc:before {
    left:489px!important;
  }
  .cms-comment-ca-marche .column.main {
    max-height: 1880px;
    .btn {
      width: 350px;
      font-size: 16px;
    }
  }
  .cms-comment-ca-marche .columns .b2 {
    margin-top: 600px;
  }
  .cms-comment-ca-marche .columns .b5 {
    margin-top: 340px;
  }
  .cms-comment-ca-marche .columns .b4 {
    margin-top: 340px;
  }
  .cms-comment-ca-marche .columns .b3 {
    margin-top: 340px;
  }
  .cms-comment-ca-marche  .contain {
    top: -537px;
    border-top: 252px solid white;
  }
  .popup-authentication.modal-popup.modal-slide .modal-inner-wrap {
    width:1500px!important;
  }

  footer .container  {
    zoom:1.33;
    margin-top: 140px!important;
  }
  footer .bloc:nth-of-type(2) {
    width: 8%!important;
  }

  footer .prefooter {
    white-space:nowrap;
    font-size: 12px!important;
    a.sep {
      margin: 0 80px 0 0;
      &:after {
        margin-left: 60px;
      }
    }
  }
  footer .prefooter + .container {
    margin-top: 0px!important;
    padding-top: 0px!important;
  }

  .ui-autocomplete {
    margin-top: 120px;
    max-width: 650px;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method .payment-method-content iframe {
    width: 930px!important;
    background:none!important;
  }
  .produits .categorie .container .produit.added div.visu div:before {
    zoom:0.8!important;
  }
  .produits .categorie .container .produit.cms p {
    background-size: contain;
    background-repeat: no-repeat;
  }

  .popup-authentication.modal-popup .action-close:before {
    color: white!important;
  }

  .productlayer .modal-inner-wrap .action-close:before {
    font-size: 15px!important;
    line-height: 25px!important;
    color: @lf-blue!important;
    margin-top:3px!important;
    margin-left: 3px!important;
  }

  .cart.table-wrapper {
    min-height: 560px;
  }

  .iwd_opc_option_with_image[data-value="edenred"] {
    height: 100px!important;
    img {
      float: right;
      position: unset;
      margin-top: 60px!important;
    }
    &:after {
      top: -18px !important;
      margin-top:26px!important;
    }
  }

  .iwd_opc_option_with_image[data-value="systempay_standard"] .iwd_opc_option_image {
    width: auto !important;


  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image[data-value="edenred"] .iwd_opc_option_image {
    right: 0px !important;
    width: 128px !important;
    height: 45px !important;
    top:-35px!important;
  }
  .iwd_opc_option_with_image[data-value="systempay_standard"] {

    img {
      width: auto!important;
      margin-top: 60px!important;
    }
    &:after {
      left: 4px !important;
      top: -70px !important;
      margin-top:-55px!important;
    }
  }
  .iwd_opc_option_with_image[data-value="wallet"] {
    line-height: 40px;
  }

  .cart.table-wrapper .cart.items .col .product-item-photo img {
    margin-top: -45px!important;
  }

  .cart.table-wrapper .cart.items .col .product-item-details {
    margin-left:180px!important;
    margin-top: -120px!important;
    min-height: 145px;
    width:100%!important;
  }
  .cart.table-wrapper .cart.items .col .product-item-details .smalldesc {
    font-size: 12px!important;
  }
  .edenred-limit {
    font-size:12px;
  }
  .iwd_opc_option_with_image[data-value="systempay_standard"] .iwd_opc_option_image {
    height: auto!important;
    margin-top:20px!important;
    margin-left: 15px!important;
  }
  .iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected:after {
    top:1px!important;
  }

  .iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected:after {
    width:12px!important;
    height:12px!important;
  }

  .iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option.selected[data-value="systempay_standard"]:after,
  .iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected[data-value="systempay_standard"]:after {
    top:5px!important;
  }

  .iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option.selected[data-value="edenred"]:after {
    top:-25px!important;
  }
  .iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected[data-value="edenred"]:after {
    top:-25px!important;
  }

  .iwd_main_wrapper .field .iwd_opc_select_container  .iwd_opc_select_option.selected.iwd_opc_option_with_image[data-value="wallet"]:after,
  .iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected.iwd_opc_option_with_image[data-value="wallet"]:after {
    margin-top: -20px!important;
    left:5px!important;
  }

  .iwd_opc_wrapper .iwd_opc_alternative_wrapper .iwd_opc_column.iwd_opc_payment_column {
    height: 940px;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method:last-child._active   {

    margin-top: -22px;
    border-top: 2px solid #f5f5f5;
  }



  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image[data-value="wallet"]   {
    line-height: 40px!important;
    padding-top: 21px!important
    height: 110px;
  }

  .iwd_success_page_wrapper .iwd_opc_alternative_wrapper .iwd_opc_success_page_column .iwd_opc_alternative_column .iwd_success_page_info button {
    width: 450px;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .iwd_opc_field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image,
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image {
    padding-right: 0;
    &[data-value="wallet"] {
      padding-right: 100px;
    }
  }


.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.iwd_opc_option_with_image[data-value=edenred] .iwd_opc_option_image {

    top: -10px !important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option[data-value=systempay_standard]:after {
    margin-top: -65px!important;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option[data-value=wallet]:after,
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option.selected[data-value=wallet]:after {
    top: -17px !important;
}
.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_option_with_image[data-value=wallet] {
    height: auto!important;
    margin-bottom: 0!important;
    padding-bottom: 36px!important;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container.selected .iwd_opc_select_option.iwd_opc_option_with_image[data-value=wallet] {
    padding-bottom: 36px !important;
}

  .iwd_main_wrapper .iwd_opc_universal_wrapper button {

    width: 450px;
  }

  .block-customer-login {
    border-top:0!important;
  }
  .block-authentication .block[class] + .block {
    margin-top:0;
    padding-top:0;
  }
  .block-authentication .actions-toolbar > .primary .action {
    margin-bottom: 20px!important;
  }
  .block-authentication .block-new-customer .actions-toolbar > .primary .action {
    margin-bottom: 40px!important;
  }

  .fieldset > .field,
  .fieldset > .fields > .field {
    margin: 0 0 40px;
  }
  .block-customer-login:before {
    display: none!important;
    content:'';
  }
  .modal-custom .action-close:before,
  .modal-popup .action-close:before,
  .modal-slide .action-close:before {
    margin-top:-35px!important;
    font-size: 36px!important;
    line-height: 50px!important;
    color:@lf-blue!important;
  }



  .password-bubble {
    margin-left: 220px!important;
    width: 530px!important;
    li:before {
      width:15px!important;
      height:15px!important;
    }
  }
  .block.crosssell {
    margin-top:300px!important;
  }

  footer .bloc,  footer .bloc a {
    font-size: 10px!important;
    line-height: 25px!important;
  }
  .login-container .actions-toolbar {
    width:75%!important;
  }
  .login-container .field label {
    font-size:14px;
    white-space: nowrap;
    width:32%;
  }
  .block.block-customer-login {
    height: 500px!important;
  }
  .account-nav .item a {
    font-size: 13px;
  }
  .account h3.box-title > span {
    font-size: 18px;
  }
  .account .block-content * {
    font-size: 13px!important;
    line-height: 36px!important;
  }
  .account .block-actions a {
    font-size: 13px!important;
  }
  .account .page-title {
    font-size: 24px!important;
  }
  .account .action.edit {
    font-size: 13px!important;
  }
  .fieldset > .legend {
    font-size: 1.6rem;
  }
  .table-order-items td {
    font-size: 13px!important;
    &.actions a {
      white-space: nowrap;
    }
  }
  .form-address-edit ,
  .form-edit-account
  {
    .field {
      font-size: 14px;
    }
    .actions-toolbar {
      .action {
        &.primary {
          font-size: 1.4rem;
        }
      }
    }
  }
  .order-status,
  .order-date,
  .action.print {
    font-size: 13px;
  }
  .order-status::before {
    content:'(';
  }
  .order-status::after {
    content:')';
  }
  .tooltip {
    width: 320px!important;
    &::before {
      margin-top:-14px!important;
    }
    div.part {
      font-size: 12px;
    }
    div.part[value="0"] {
      font-size: 10px;
    }
  }
  .ll-skin-latoja {
    zoom:1.5;
  }
  .ll-skin-latoja .ui-datepicker th span {
    width:8px;
    &[title="mardi"] {
      width: 12px;
    }
    &[title="mercredi"] {
      width: 12px;
    }
    &[title="samedi"] {
      width: 9px;
    }
    &[title="dimanche"] {
      width: 10px;
    }
  }
  .ll-skin-latoja .ui-datepicker td a {
    font-size: 14px;
  }
  #header .logo.fixednoanim {
    border-left-width: 45px!important;
  }

  footer .bloc,
  footer .bloc a {
    font-size: 10px;
    line-height: 25px;

  }
  .closest h4 {
    font-size: 15px;
  }
  .closest input[type="submit"] {
    padding:0;
  }
  .closest input[type="text"] {
    font-size: 12px!important;
  }
  .cms-qui-sommes-nous #header .bigtitre {
    width: 800px;
    left: 550px;
    top:176px;
  }
  .centertext {
    line-height: 35px;
    font-size: 16px;
  }
  .cms-left.w40:not(.concept3),
  .cms-left.w40:not(.concept4) {
    height: 800px;
    width:41%;
    div {
      width: 101%;
    }
    & + .cms-right {
      width:58%;
    }
  }
  .formulaire div input {
    width: ~"calc(100% - 200px)";
    height: 46px;
  }
  .formulaire div  {
    height: 56px;
    line-height: 56px;
  }
  .popup-authentication .block-authentication {
    height: 490px;
    .block-title {
      font-size: 2rem;
    }
    .block-content {
      font-size: 12px;
    }
    .block[class] {
      height: 500px;
    }
  }
  #header .container {
    .bigtitre:before {
      font-size:28px;
    }
    .delivery  {
      width: 1340px;
      .resetPopin {
        line-height: 24px;
        right: 241px;
        width: 620px;
        &._place {
          width: 481px;
        }
      }
      .nok,
      .check {
        width:450px;
        margin-top: -75px;
        line-height: 24px;
        background: white;
      }
      .adresse {
        width:600px;
        height: 52px;
        margin-top: 20px;
        line-height: 20px;
        font-size: 20px;
      }
      .moment {
        font-size: 12px;
      }
      .question {
        font-size: 13px;
      }
      &.fixednoanim {
        max-width:1280px;
        right: 241px;
        .resetPopin {

          width: 609px;
          &._place {
            right: 241px;
          }
        }

      }

      .step-coming-soon {
        .ok {
          font-size: 10px;
        }
        .coming-soon {
          font-size: 13px;
        }
        .modify-shipping {
          font-size: 13px;
        }
      }
      .step1 .ok {
        font-size: 9px;
        padding-top: 30px;
        height: 61px;
      }
      .step1.OK + .step2 .ok {
        font-size: 9px;
        height: 63px;
      }
      .step2 .choix-livraison {
        top:21px;
        label:after {
          margin-top:11px;
        }

        label + .advice {
          width: 450px;
          font-size: 12px;
        }
      }
      .step2 {
        .creneau1 {
          margin-left: 445px;
        }
        .creneau2 {
          margin-left: 705px;
        }
        .creneau3 {
          margin-left: 917px;
          .tooltip {
            width: 400px!important;
          }
        }
        &.noToday {
          .creneau2 {
            margin-left: 445px;
          }
          .creneau3 {
            margin-left: 657px;
          }
        }
      }



    }

    .links {

      .aspan {
        width:238px;
        z-index:9;
        a {
          width: 228px;
          padding:0 15px 0 0;
        }
        a.close {
          position:absolute;
          width: 40px!important;
          right: -1px;
          &:after {
            font-size:27px;
          }
        }
      }
      a {
        font-size:9px!important;
      }
    }
  }
  .delivered .logos {
    transition:none;
  }
  .commande, .fidexnoanim .commande {
    width:~"calc(100% - 255px)";
  }

  .commande .grey.blue {
    font-size: 13px;
  }
  .fixednoanim .commande  {
    max-width: 1030px;
  }
  .fixednoanim .commande .grey.blue {
    line-height: 23px;
    max-width: 280px;
  }
  .commande .grey.blue + .question + .grey.blue {
    max-width: 400px;
    &:after {
      right:-22px;
    }
  }

  .commande .grey.blue:after {
    height:14.5px;
  }
  .produits .filtres {
    height: 69px;

    .filtre {
      padding:19px 17.7px;
      &:not(.selected):hover {
        color:@lf-blue!important;
        background:unset!important;
        &:after {
          background:transparent;
        }
      }
      &.last:before {
        margin-top:1px;
      }
      &.last:not(.selected):hover:after {
        background:transparent;
        color:@lf-blue;
      }
      &.last .tooltip {
        width:400px!important;
        left:-255px;
        h5 {
          margin-bottom: 10px;
        }
        .item {
          height: 40px;
          width: 372px;
          &:hover {
            background:transparent;
          }
        }
      }
    }
  }
  .fixednoanim.commande .grey.blue + .question + .grey.blue {
    max-width: 208px;
  }
  .productlayer, .productlayer .modal-inner-wrap {
    max-width: 1000px;
  }
  .productlayer .modal-inner-wrap  .action-close {
    left:950px;
  }
  .productlayer .modal-inner-wrap  .action-close:before {
    font-size: 31px!important;
    margin-left:-13px!important;
    color:@lf-blue!important;
  }

  .productlayer .modal-inner-wrap {
    .produit {
      /*width: 962px;*/
    }
  }
  .smallcart {
    width: 242px;
  }
  .smallcart .count {
    width: 32px;
    height: 30px;
    border-radius: 16px;
  }
  .minicart-wrapper {
    .action.close {
      right: 6px;
    }
    .block-minicart {
      width:450px!important;
      .product-item-details {
        font-size: 13px;
        .details-qty {
          vertical-align: bottom;
          min-width: 42px;
          width: auto;
        }
      }
      .product-item-name {
        width: 320px;
      }
      .subtitle {
        width:400px!important;
        font-size: 13px!important;
      }
    }
    .action.showcart {
      margin-top: -8px;
    }
  }
  .order_minimum_amount {
    font-size: 11px;
  }
  .produits {
    h2 {
      font-size: 24px;
    }
    .categorie .container {
      max-width: 1800px;
      .produit {
        width:23.5%;
        height: 635px;
        margin-bottom: 20px;

        &:hover div.visu div {
          line-height:29px;
          background:none;
          &:not(.hovered) {
            &:before {
              font-size:40px!important;
              line-height:25px;
            }
            &:after {
              margin-left:6px;
            }
          }
        }
        .desc {
          font-size: 12px;
          line-height: 20px;
        }
        .pic img {
          width: 40px;
        }
        .tt:before {
          top:73px;
        }
        .tt:after {
          top:58px;
        }
        .content {
          height: 310px;
        }

        &.added div.visu div:before {
          font-size: 39px!important;
          height: 41px;
          margin-top: 5px;
          line-height: 27px;
          vertical-align: top;
        }
        div.visu div:after,
        &:hover div.visu div:after {
          display: none;
        }
        &.added  div.visu div:before,
        &.added:hover div.visu div:before {
          font-size: 39px!important;
          height: 41px;
          margin-top: 5px;
          line-height: 27px;
          vertical-align: top;
          display: block;
        } &:hover div.visu div:not(.hovered):before {
            line-height: 26px;
          }
        &.added:hover div.visu div:not(.hovered):before {
          line-height: 35px;
        }
        &.added:active div.visu div:before {
          font-size: 26px!important;
          height: 41px;
          margin-top: 5px;
          line-height: 27px;
          vertical-align: top;
          transform:none!important;
          display: block;
        }

        &.added:hover div.visu div:after {
          display: inline-block;
          width: 3px;
          height: 30px;
          background: white;
          content:'';
          transform: rotate(-50deg);
          position: absolute;
          margin-left: 6px;
          margin-top: 34px;
        }
        &:active div.visu div:after,
        &.added:active div.visu div:after {
          display: none;
        }
        &.added div.visu div {
          font-size: 18px!important;
        }
        .addtocart {
          font-size: 16px;
          .quantity {
            margin-top: -15px;
            input {
              height: 45px;
              width: 64px;
              vertical-align: middle;
            }
          }
          &.prixbarre {
            .quantity {
              margin-top: -42px;
            }
            div.ttc, div.ht {
              top: -20px!important;
            }
            div.barre {
              zoom:0.9;
              top:-20px!important;
              &:before {
                margin-top:8px;
              }
            }
          }
        }
        .smalldesc {
          height: 34px;
          line-height: 35px;
          font-size: 13px;
        }
        h4 {
          font-size: 15px;
          height: 60px;
          margin-bottom: 10px;
        }
        &.cms h3 {
          font-size: 18px;
        }
        .no-delivery h5 {
          font-size:7px;
        }
      }
      h4 {
        font-size:14px;
      }
    }
  }
  .productlayer.crosssell {
    max-width: 1404px;
  }
  .productlayer .modal-inner-wrap {
    &.crosssell {
      max-width: 1404px;
      cross .produit .addtocart img {
        margin:10px!important;
      }
      .cross .produit .addtocart div.ht,
      .cross .produit .addtocart div.ttc {
        font-size: 15px;
        margin-top: -20px!important;
      }
      .cross .produit .addtocart.prixbarre .quantity {
        margin-top: -46px!important;
      }
      .cross .produit .addtocart.prixbarre div.ht,
      .cross .produit .addtocart.prixbarre div.ttc {
        margin-top:8px!important;
      }
    }
    .gzoom, .gzoom .slides {
      width: 1000px;
      height: 750px;
    }
    .cross h3 {
      font-size: 16px;
    }
    .cross .sell .produit .no-delivery h5 {
      font-size:6px;
    }
    .cross .sell .produit .content h4 {
      font-size:15px;
      height: 57px;
    }
    cross .sell .produit  .addtocart {
      height: 55px!important;
      padding-top: 7px!important;
    }
  }
  .productlayer .modal-inner-wrap .produit {
    height: 100%;
    width:996px;
    .desc {
      line-height: 30px;
    }
    h4 {
      font-size:20px;
      height: 75px;
      max-width: 800px;
    }
    h5 {
      max-width: 430px!important;
    }
    .addtocart {
      height: 40px!important;
      padding-top: 7px!important;
      span {
        font-size: 8px!important;
      }
      div.ht, div.ttc {
        font-size: 24px;
        margin-top: -17px!important;
        &.barre {
          top: -14px!important;
        }
      }
    }
    .smalldesc {
      line-height: 25px;
      height: 30px;
    }
    .pic img{
      width:40px;
    }
  }
  .pac-item {
    padding:6px!important;
  }

  .action.update {
    font-size: 8px!important ;
    height: 68px!important;
    max-width: 415px!important;
  }

  .checkout-cart-index {
    .columns {
      max-width: 1760px!important;
    }
    .price {
      font-size:16px!important;
    }
    .action.continue {
      font-size:13px!important;
    }
    .cart.table-wrapper .cart.items .col {
      font-size:14px!important;
    }
    .cart.table-wrapper .cart.items .qty input {
      height: 45px!important;
      font-size:18px!important;
    }
    .cart.table-wrapper .cart.items .qty div {
      margin-top: -44px!important;
    }
    .cart.table-wrapper .item .col.item {
      min-height: 115px!important;
    }
    .cart.table-wrapper .cart.items .col.qty {
      padding-top: 42px!important;
    }
  }
  .tunnel {
    div {
      font-size:13px!important;
    }

  }
  .accept-cgv {
    max-width: 580px;
    font-size:11px;
  }
  .payment-method-content iframe {
    height: 800px!important;
    background:none!important;
  }
  .iwd_main_wrapper {
    max-width:1750px!important;
    .iwd_opc_review_total_cell {
      line-height: 30px!important;
    }


    .iwd_opc_field.iwd_opc_input,
    .field.iwd_opc_input,
    .iwd_opc_field .input-text,
    .field .input-text,
    {

      height: 67px !important;
      font-size: 14px!important;

    }

    .field .selectize-control.single .selectize-input {
      font-size: 14px!important;
    }


    #reference::placeholder {
      font-size: 14px!important;
    }

    #iwd_opc_review_totals {
      h3 {
        font-size:17px!important;
      }
    }
    button.iwd_opc_place_order_button {
      max-width:570px!important;
      background-position:17% 15px!important;
    }
    .iwd_opc_payment_column,
    .iwd_opc_column_name {
      font-size:15px!important;
      .field .input-text {
        height:57px!important;
        line-height:57px!important;
      }
      .field textarea.input-text {
        line-height:20px!important;
        height:140px!important;
      }
    }
  }
  .shipping-address-button-container {
    margin-top:50px!important;
  }
  .selectize-control.single .item {
    line-height:30px!important;
  }
  .cart-container .checkout-methods-items .action.primary {
    font-size:9px!important;
  }
  .cart.table-wrapper .product-item-name {
    font-size:16px!important;
  }
  .cart-summary .discount .content .btn {
    margin-top:-52px!important;
    font-size:13px!important;
  }
  #coupon_code {
    height:50px!important;
    line-height:50px!important;
  }
  .nbPiecesConvives {
    height:56px!important;
    width:56px!important;
    border-radius:50px!important;
    font-size:22px!important;
  }
  .guestCalculator {
    margin-top:220px!important;
    .guestCalculatorIn {
      font-size:14px!important;
      select {
        height:60px!important;
      }
      .nbPieces {
        font-size:20px!important;
      }
    }
  }
  .cart-container .action.primary[data-role=proceed-to-checkout] {
    background-position:80px 18px!important;
  }

}


body .noportrait,
html .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
  top:0;

  visibility: hidden;
  right: 0;
  left:0;
  pointer-events: none;
  height: 100%;
  opacity: 1;
  text-align: center;
  background: white url('@{baseDir}images/logoa.svg') left 50% center no-repeat;
  background-size: 15%;
}


.nodisplayportrait  {

  body.handheld, html.handheld {

  }
}


.nodisplaypaysage  {
  body.iPad, html.iPad,
  body.handheld, html.handheld {
    zoom:0.75;
    .page-wrapper *,
    .modals-wrapper {
      visibility: hidden;
    }
    .noportrait {
      position:fixed;
      display: block;
      z-index: 9000;
      visibility: visible;
      pointer-events: unset;
      background-position: bottom;
      background-size: 50%;
      text-align: center;
      width:100%;
      &:after {
        content:'Merci de consulter notre site en mode paysage';
        color:@lf-blue;
        font-size: 40px;
        text-align: center;
        font-family: 'DinProBold';
        position: relative;
        margin-top:40%;
        display: inline-block;
      }

    }
  }

}
@media only screen and (min-device-width : 1900px)  {
  .desk footer .prefooter br {
    display: none;
  }
}
@media only screen and (min-device-width : 900px) and (max-device-width : 1900px)  {
  .desk footer .prefooter .sep + .sep {
    margin:0 0 0 20px;
    &:after {display:none;}
  }
}



@media only screen and (min-device-width: 370px) and (max-device-width : 1000px)  {
  .checkout-cart-index .cart.main.actions {
    margin-top: 460px;
  }
}




//phone generic paysage (largeur) : affichage blanc

@media only screen and (min-device-width : 567px) and (max-device-width : 1100px) {
  .nodisplayportrait;
  body {
    z-index:1;
  }
}

//iphone 10  paysage  :  affichage blanc

@media only screen and (min-device-width : 812px) and (max-device-width : 812px)  and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 3) {
  .nodisplayportrait;
  body {
    z-index:2;
  }
}



//phone paysage :  affichage blanc

@media only screen and (max-device-width : 1270px) and (min-device-width : 700px) and (-webkit-min-device-pixel-ratio: 3)  and (orientation : landscape)  {
  .nodisplayportrait;
  body {
    z-index:3;
  }
}

@media only screen and (max-device-width : 1270px) and (min-device-width : 700px) and (-webkit-min-device-pixel-ratio: 4) and (orientation : landscape)  {
  .nodisplayportrait;
  body {
    z-index:4;
  }
}


// oneplus6 paysage  :  affichage blanc

@media only screen and (min-device-width: 414px) and (max-device-width: 1270px) and (orientation: landscape) {
  .nodisplayportrait;
  body {
    z-index:5;
  }
}

// iphone 5 paysage  :  affichage blanc

@media only screen and (min-device-width: 320px) and (max-device-width: 1000px)  and (orientation: landscape)  {
  .nodisplayportrait;
  body {
    z-index:60;
  }
}
// iphone 6 paysage  :  affichage blanc

@media only screen and (min-device-width: 375px) and (max-device-width: 1000px)  and (orientation: landscape)  {
  .nodisplayportrait;
  body {
    z-index:61;
  }
}
// iphone 7/8 paysage  :  affichage blanc

@media only screen and (min-device-width: 375px) and (max-device-width: 1000px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: landscape) and (min-aspect-ratio: 16/9) {
  .nodisplayportrait;
  body {
    z-index:6;
  }
}


// iphone 7/8 portrait

@media only screen and (min-device-width: 375px) and (max-device-width: 736px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait) {

  .modal-popup.modal-slide.productlayer .produit h4 {
    font-size:26px;
  }

}

//ipad portrait :  affichage blanc

@media only screen and (max-device-width : 768px) and (min-device-width : 768px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
  .nodisplaypaysage;
  body {
    z-index:7;
  }
}

//ipad paysage :  affichage ok

@media only screen and (max-device-width : 768px) and (min-device-width : 768px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
  body.iPad .noportrait,
  html.iPad .noportrait {
    display: none;
    z-index: -1;
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }
  body {
    z-index:77;
  }
  body.iPad  .page-wrapper *,
  body.iPad  .modals-wrapper {
    visibility: visible!important;
  }
}



//  ipad 11 portrait  :  affichage blanc

@media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
  .nodisplaypaysage;
  body {
    z-index:8;
  }
}


//  ipad 12.9 portrait  :  affichage blanc

@media only screen and (min-device-width : 1023px) and (max-device-width : 1026px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
  .nodisplaypaysage;
  body {
    z-index:9;
  }
}
















//  ipad 11 paysage  :  affichage OK

@media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {

  body.iPad .noportrait,
  html.iPad .noportrait {
    display: none;
    z-index: -1;
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }
  body {
    z-index:10;
  }
  body.iPad  .page-wrapper *,
  body.iPad  .modals-wrapper {
    visibility: visible!important;
  }
}


//  ipad 11 paysage  :  affichage ok

@media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2.5) {

  body.iPad .noportrait,
  html.iPad .noportrait {
    display: none;
    z-index: -1;
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }
  body {
    z-index:10;
  }
  body.iPad  .page-wrapper *,
  body.iPad  .modals-wrapper {
    visibility: visible!important;
  }
}


//  ipad 11 paysage  :  affichage ok

@media only screen and (min-device-width : 833px) and (max-device-width : 835px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {

  body.iPad .noportrait,
  html.iPad .noportrait {
    display: none;
    z-index: -1;
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }
  body {
    z-index:10;
  }
  body.iPad  .page-wrapper *,
  body.iPad  .modals-wrapper {
    visibility: visible!important;
  }
}

//  ipad 12.9 paysage  :  affichage ok

@media only screen and (min-device-width : 1023px) and (max-device-width : 1026px) and (orientation : landscape)  {

  body.iPad .noportrait,
  html.iPad .noportrait {
    display: none;
    z-index: -1;
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }
  body.iPad  .page-wrapper *,
  body.iPad  .modals-wrapper {
    visibility: visible!important;
  }
  body {
    z-index:11;
  }
}
// correctifs oneplus 7T pro
@media only screen and (max-width: 892px) and (min-width: 421px){
  .iwd_opc_option_with_image[data-value="systempay_standard"]:after {
       margin-top: -20px !important;
  }
}

@media only screen and (max-width: 321px) and (min-width: 300px) {
  .scroll-wrapper .iwd_opc_option_with_image[data-value=systempay_standard]:after {
      margin-top: -34px !important;
  }
}

@media only screen and (max-width: 361px) and (min-width: 322px) {
  .scroll-wrapper .iwd_opc_option_with_image[data-value=systempay_standard]:after {
      margin-top: -36px !important;
  }
}

@media only screen and (max-width: 600px) and (min-width: 410px) {
   .iwd_opc_option_with_image[data-value=systempay_standard]:after {
      margin-top: -20px !important;
  }
}


.produits .categorie .container .produit:hover div.visu div:not(.hovered):after {
    width: 5px;
    background: #003456;
    content: '';
    margin-left: 5px;
    margin-top: 39px;
}

.produits .categorie .container .produit:hover div.visu div:not(.hovered):before {
    content: '' !important;
    color: transparent!important;
    background: transparent!important;
}

.produits .categorie .container .produit:hover div.visu div {
    background: transparent;
    border-color: #003456;
    border: 4px;
    border-style: solid;
}






.shipping_summary {
    font-size: 18px;
    display: inline-block;
    height: 92px;
    vertical-align: middle;
    width:~"calc(100% - 167px)";
}
.shipping_summary .text{
    color: @lf-blue;
    cursor: pointer;
    display: inline-block;
    font-family: Eczar, arial;
    font-size: 13px;
    font-weight: bolder;
    line-height: 26px;
    padding-top: 4px;

    text-transform: uppercase;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
}
.text-uppercase{
    text-transform: uppercase;
}
.shipping_summary .margin-10{
    margin-left: 10px;
}

.shipping_summary .place_after {
    background: url(../images/edit.svg);
    cursor: pointer;
    width: 15px;
    background-size: 15px;
    height: 15px;
    display: inline-block;
    margin-left: 15px;
    vertical-align: middle;
    margin-top: -14px;
}
.shipping_summary_left, .shipping_summary_right {
    display: inline-block;
    height: 62px;
    width: 49%;
    padding: 30px 0px 0px 0px;
}
.shipping_summary_right {
    border-left: 1px solid #aaa;
}

.shipping_summary .split{
    width: 49%;
    text-align: center;
    box-sizing: border-box;
    display: inline-block;
}

.summary_date {
    line-height: 13px;
    font-size: 13px;
    width: auto;
    display: inline-block;
    background-color:@eatred;
    text-transform: uppercase;
    font-family: 'DinProBold';
    color:white;
    padding: 4px 4px 4px 4px;
    vertical-align: middle;
    margin-top: -14px;
}

.fixednoanim div.shipping_summary {
    width: ~"calc(100% - 165px)";

    .shipping_summary_left, .shipping_summary_right {
        height: 62px;
        padding: 30px 0px 0px 0px;
    }
    .text {
        font-size: 12px;
        max-width: 140px;
    }
    .shipping_summary_right {
        width: 54%;
    }
    .shipping_summary_left {
        width: 45%;
    }

}

@media screen and (min-width: 1800px) {
    .shipping_summary {
        width: ~"calc(100% - 255px)";
    }
    .fixednoanim div.shipping_summary {
        width: ~"calc(100% - 255px)";
    }
    #header .container .delivery {
        .resetPopin._place {
            width:calc(~'(100% - 246px)*0.49') !important;
        }
    }
}

@media screen and (max-width: 1000px) {
    .ss2 {
    }
    .shipping_summary {
        width: 100%;
        display: block;
    }
    .shipping_summary_right, .shipping_summary_left {
        width: 100%;
        padding: 10px 0px 10px 0px;

    }
    .shipping_summary_right {
        border-left: 0px solid #aaa;
        border-top: 1px solid #aaa;
    }
    .shipping_summary .split {
        width: 100%;
        text-align: left;
        margin-left: 5px;
    }
    .shipping_summary .margin-10{
        margin-left: 0px;
    }
    .shipping_summary_right .text{
        width: 130px;
    }
    .shipping_summary_left .text{
        min-width: 190px;
    }
}

//tootip shippingbar
.shippingbar-tooltips {
    position: relative;
    float: left;
    margin-bottom: 10px;
    display: block;
    width: 0px;
    height: 0px;
    top: 33px;
    left: 1005px;
}
@media screen and (min-width: 1600px) {
    .shippingbar-tooltips {
        left: 1230px;
    }
}
@media screen and (min-width: 1800px) {
    .shippingbar-tooltips {
        left: 1140px;
    }
}

.shippingbar-tooltips i {
    font-size: 28px;
    color: @lf-blue;
    margin-left: 5px;
}
#shippingbarTooltipPopin {
    top: 70px;
    right: 157px;
    padding-bottom: 15px;
    width: 420px;
    z-index: 100;
}
#shippingbarTooltipPopin:before {
    right: 15px;
}
.fixednoanim .shippingbar-tooltips {
    float: right;
    left: unset;
    right: 188px;
}
.fixednoanim .shipping_summary .margin-10{
    margin-left: -25px;
}
@media screen and (min-width: 1800px) {
    .fixednoanim .shippingbar-tooltips {
        right: 285px;
    }
}
@media screen and (max-width: 1000px) {
    .shippingbar-tooltips {
        left: 300px;
        top: 127px;
    }

    .shippingPopin {
        left: -2px;
    }
}


@media screen and (max-width: 456px) {
    .minicart-link {
        display: none;
    }
    .overlaysmallcarticone {

        padding-top: 17px;
    }
    .minicart-wrapper{
        min-width: 100px;
    }
    .smallcart{
        width: 100px;
    }
    #header .div-logo{
        width: 280px;
        margin-left: 10px;
    }
}
@media screen and (min-width: 457px) {
    .overlaysmallcarticone {
        display: none;
    }
}

#adresse-input {
    border: none;
}
@media screen and (min-width: 1001px) and (max-width: 1799px) {
    header .container .delivery .resetPopin._place {
        width: calc(~'(100% - 157px)*0.49') !important;
    }
}

@media screen and (max-width: @screen__xs) {
    header {
        .logo {
            &.fixednoanim {
                max-width: 200px !important;
            }
        }
    }
}


@media screen and (max-width: 456px) {
    .minicart-link {
        display: none;
    }
    .overlaysmallcarticone {

        padding-top: 17px;
    }
    .minicart-wrapper{
        min-width: 100px;
    }
    .smallcart{
        width: 100px;
    }
    #header .div-logo{
        width: 280px;
        margin-left: 10px;
    }
}
@media screen and (min-width: 457px) {
    .overlaysmallcarticone {
        display: none;
    }
}

#adresse-input {
    border: none;
}
@media screen and (min-width: 1001px) and (max-width: 1799px) {
    header .container .delivery .resetPopin._place {
        width: calc(~'(100% - 157px)*0.49') !important;
    }
}

@media screen and (max-width: @screen__xs) {
    header {
        .logo {
            &.fixednoanim {
                max-width: 200px !important;
            }
        }
    }
}


@media screen and (max-width: 456px) {
    .minicart-link {
        display: none;
    }
    .overlaysmallcarticone {

        padding-top: 17px;
    }
    .minicart-wrapper{
        min-width: 100px;
    }
    .smallcart{
        width: 100px;
    }
    #header .div-logo{
        width: 280px;
        margin-left: 10px;
    }
}
@media screen and (min-width: 457px) {
    .overlaysmallcarticone {
        display: none;
    }
}

#adresse-input {
    border: none;
}
@media screen and (min-width: 1001px) and (max-width: 1799px) {
    header .container .delivery .resetPopin._place {
        width: calc(~'(100% - 157px)*0.49') !important;
    }
}

@media screen and (max-width: @screen__xs) {
    header {
        .logo {
            &.fixednoanim {
                max-width: 200px !important;
            }
        }
    }
}

.control.customer-dob {
    input {
        background-color: rgba(255,255,255,0);
        width: 100% ;
    }

}
.customer-account-validatephone,
.customer-account-create {
    h2 {
        padding-bottom: 0px !important;
    }
}
.form-create-account-phone {
    .legend-phone {
        color: #003456;
        font-family: 'DinProBlack';
        text-transform: uppercase;
        line-height: 1.2;
        font-size: 2rem;
        letter-spacing: normal;
        font-style: normal;
        font-weight: 400;
    }
}

.form-validate-account {
    .changenumber, .resentagainnumber {
        font-family: Eczar, arial;
        font-weight: normal;
        margin-bottom: 20px;
        font-size: 15px;
        text-align: left;
        a {
            text-decoration: underline;
            color: black;
        }
    }
    .changenumber {
        a {
            padding-left: 10px;
            white-space:nowrap;
        }
    }
    .legend {
        width: auto;
    }
    .actions-toolbar {
        margin-top: -15px;
    }
}
@media only screen and (min-device-width : 768px) {
    .form-validate-account {
        .changenumber, .resentagainnumber {
            margin-left: 25.8%;
        }
        .actions-toolbar {
            margin-left: 25.8%;
        }
    }
}

.phone-help {
    float: left;
    text-align: left;
    margin-top: 20px;
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .page-main {
        .account &,
        .cms-privacy-policy & {
            padding-top: 0px;
            position: relative;
        }
    }
}

//Correctifs logo sur ios
//iPhone XR
/* 1792x828px at 326ppi */
@media only screen
and (device-width : 414px)
and (device-height : 896px)
and (-webkit-device-pixel-ratio : 2) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}
//iPhone XS
/* 2436x1125px at 458ppi */
@media only screen
and (device-width : 375px)
and (device-height : 812px)
and (-webkit-device-pixel-ratio : 3) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}
//iPhone XS Max
/* 2688x1242px at 458ppi */
@media only screen
and (device-width : 414px)
and (device-height : 896px)
and (-webkit-device-pixel-ratio : 3) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}

.message-partenaire {
    position: relative;
    background-color: #fec9df;
    width: 100%;
    min-height: 48px;
    color: #003456;
    font-family: @eatFontFamily;
    font-size: 14px;
    text-align: center;

    #message-partenaire-txt {
        display: inline-block;
        text-align: center;
        width: 75%;
        padding-top: 12px;
        padding-bottom: 10px;
    }
    .message-partenaire-close {
        position: absolute;
        top: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 30px;
        display: block;
        width: 14px;
        height: 14px;
        background-position: right -1px center;
        background-size: 16px;
        background-repeat: no-repeat;
        background-image: url(../images/cross.svg);
        background-color: #003456;
        border-radius: 8px;
        border: 4px solid transparent;
        cursor: pointer;
    }
}
.message-partenaire.fixednoanim {
    position: fixed;
    top: 149px;
    margin-top: 0px;
    z-index: 9;
}

@media screen and (max-width: 1000px) {
    .message-partenaire {
        position: fixed;
        top: 99px;
        margin-top: 0px;
        z-index: 50;
    }
    .message-partenaire.fixednoanim {
        top: 96px;
    }
}

.customer-account-validatephone,
.customer-account-create {
    h2 {
        padding-bottom: 0px !important;
    }
}
.form-create-account-phone {
    .legend-phone {
        color: #003456;
        font-family: 'DinProBlack';
        text-transform: uppercase;
        line-height: 1.2;
        font-size: 2rem;
        letter-spacing: normal;
        font-style: normal;
        font-weight: 400;
    }
}

.form-validate-account {
    .changenumber, .resentagainnumber {
        font-family: Eczar, arial;
        font-weight: normal;
        margin-bottom: 20px;
        font-size: 15px;
        text-align: left;
        a {
            text-decoration: underline;
            color: black;
        }
    }
    .changenumber {
        a {
            padding-left: 10px;
            white-space:nowrap;
        }
    }
    .legend {
        width: auto;
    }
    .actions-toolbar {
        margin-top: -15px;
    }
}
@media only screen and (min-device-width : 768px) {
    .form-validate-account {
        .changenumber, .resentagainnumber {
            margin-left: 25.8%;
        }
        .actions-toolbar {
            margin-left: 25.8%;
        }
    }
}

.phone-help {
    float: left;
    text-align: left;
    margin-top: 20px;
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .page-main {
        .account &,
        .cms-privacy-policy & {
            padding-top: 0px;
            position: relative;
        }
    }
}

//Correctifs logo sur ios
//iPhone XR
/* 1792x828px at 326ppi */
@media only screen
and (device-width : 414px)
and (device-height : 896px)
and (-webkit-device-pixel-ratio : 2) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}
//iPhone XS
/* 2436x1125px at 458ppi */
@media only screen
and (device-width : 375px)
and (device-height : 812px)
and (-webkit-device-pixel-ratio : 3) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}
//iPhone XS Max
/* 2688x1242px at 458ppi */
@media only screen
and (device-width : 414px)
and (device-height : 896px)
and (-webkit-device-pixel-ratio : 3) {
    #header .logo.fixednoanim {
        margin-left:-50px;
    }
}

.ui-datepicker .ui-datepicker-calendar td .ui-state-hover {
    background: #07203f;
    color: #ffffff;
}

.ui-datepicker .ui-datepicker-calendar td a {
    padding: 4px;
    display: block;
    text-align: center;
}

.ui-datepicker .ui-datepicker-title .ui-datepicker-year {
    width: 49%;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
    border: 1px solid #07203f;
    background: #07203f url(images/ui-bg_highlight-soft_75_ffe45c_1x100.png) 50% top repeat-x;
    color: #ffffff;
}
.ui-datepicker .ui-datepicker-calendar .ui-state-active {
    border: 1px solid #07203f;
    background: #07203f url(images/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x;
    font-weight: bold;
    color: #ffffff;
}

.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {
    border: 1px solid #07203f;
    background: #07203f url(images/ui-bg_glass_100_fdf5ce_1x400.png) 50% 50% repeat-x;
    font-weight: bold;
    color: #ffffff;

}


.customer-account-edit .ui-datepicker-trigger,
body.customer-account-create .ui-datepicker-trigger {
    top: -42px;
    position: relative;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-shadow: none;
    font-weight: 400;
    float: right;
    text-decoration: none;
    display: flex;
    vertical-align: middle;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #iwd_opc_top #iwd_opc_review #iwd_opc_review_items_totals #iwd_opc_review_totals .iwd_opc_review_total .iwd_opc_review_total_cell:last-child {
  font-size: 16px;
}

