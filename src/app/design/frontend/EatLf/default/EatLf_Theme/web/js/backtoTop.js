define([
    'jquery',
], function ($) {
    'use strict';

    return function (config, element) {
 

        $(element).hide();
        $(function () {
            
            if(jQuery(".checkout-cart-index .message div:contains('devis')").length) {
                 jQuery(".checkout-cart-index .message:not(.cookie, .empty) div:contains('devis')").parent().insertAfter(jQuery(".checkout-methods-items"));
            }
            $("body:not(.checkout-cart-index) .messages").show();

            if (!String.prototype.endsWith) {
              String.prototype.endsWith = function(searchString, position) {
                  var subjectString = this.toString();
                  if (typeof position !== 'number' || !isFinite(position) 
                      || Math.floor(position) !== position || position > subjectString.length) {
                    position = subjectString.length;
                  }
                  position -= searchString.length;
                  var lastIndex = subjectString.indexOf(searchString, position);
                  return lastIndex !== -1 && lastIndex === position;
              };
            }

            $("body").on("click mousedown", ".message:not(.cookie, .empty)", function(e) { 
               $(this).hide();
            });
            
            $("body").on("DOMSubtreeModified", ".messages", function(){
                setTimeout('jQuery(".messages").show()', 300);
            });

            $("body").on("mousedown", ".step1", function(e) { 
                e.preventDefault(); 
                if( $("#adresse-input").is(":focus") ) { 
                    $(".check").show().addClass("visible");
                } else {     
                    $("#adresse-input").focus();
                    $(".check").hide().removeClass("visible");
                    if($(".pac-item").length > 0) {
                        $(".pac-container").show();
                    }
                }
            });


            var closeClick = false;

            $("body").on("mousedown", ".aspan a.close", function(e) {  
                var closeClick = true;   
            });


            $("body").on("click", ".aspan a.account", function(e) { 
                e.preventDefault(); 
                if(closeClick != true ) {
                    window.location=$(".aspan a.account").attr("href"); 
                }  
            });


            $("body").on("click", ".burger", function () {   
                if($(".burger").hasClass("open")) {
                    $(".burger, .links, .filtres, .menu").removeClass("open"); 
                    $(".delivery").removeClass("d-none");
                    $(".filtre.last").addClass("clickable");
                } else {
                    $(".burger, .links").addClass("open"); 
                    $(".menu, .filtres").removeClass("open"); 
                    $(".delivery").addClass("d-none");
                    $(".filtre.last").removeClass("clickable");
                } 
                if($(".aspan .identified").length == 1) {
                    $(".burger").addClass("identified");
                } 
            });

            $("body").on("click", ".menu", function () {  
                
                if($(".menu").hasClass("open")) {
                    $(".menu, .filtres, .burger").removeClass("open");  
                    $(".page-bottom, .page-footer, .commande").removeClass("d-none");
                    $(".produits .categorie").removeClass("d-nope");  
                    $(".filtre.last").removeClass("clickable");
                } else {
                    $(".menu").addClass("open");
                    $(".filtres").addClass("open");  
                    $(".burger").removeClass("open"); 
                    $(".page-bottom, .page-footer, .commande").addClass("d-none");  
                    $(".produits .categorie").addClass("d-nope"); 
                    $(".filtre.last").addClass("clickable"); 
                }
            });

            $("body").on("click", ".links.open", function () {  
                $('.burger').click();
            });

            $("body").on("click", ".guestCalculator h3", function () {  
                $(this).toggleClass("open");
            }); 

           
            $("body").on("mousedown", ".overlaysmallcart", function () {  
                if(window.screen.width > 1400 && $("a.action.viewcart").length == 1) {
                    $("a.action.viewcart div").click();
                }   
            }); 
           
 
            $(window).scroll(function () {
                if ($(this).scrollTop() > 100) {
                    $(element).fadeIn();
                } else {
                    $(element).fadeOut();
                }
            });

            $(element).click(function () {
                $('body,html').animate({
                    scrollTop: 0
                }, 300);
                return false;
            });

            $("body").on("click", ".delivered .logos", function () {
                var activelogo = $(".delivered .logos.active");
                activelogo.removeClass("active");
               
                if(!activelogo.next().hasClass('logos')) {
                    $(".delivered .logos1").addClass("active"); 
                } else {
                     activelogo.next().addClass("active"); 
                }
            }); 

            if($("body[class*='account']").length != 0) { 
                $(".menu").html("Mon compte");
                $("<div>").addClass("filtres").html($(".sidebar-main").html()).appendTo($(".page-wrapper")); 
            }



        });
    }
});