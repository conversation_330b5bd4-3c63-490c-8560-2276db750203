define([
    'jquery',
], function ($) {
    'use strict';


    return function (config, element) { 

       if(/Android/.test(window.navigator.userAgent)) {
         $("body, html").addClass("handheld");
       } else {
        if(navigator.platform.indexOf('iPad') > -1) {
          $("body, html").addClass("iPad");
         }
          if(navigator.platform.indexOf('Mac') > -1 || navigator.platform.indexOf('iPad') > -1 || navigator.platform.indexOf('Win') > -1 || navigator.platform.indexOf("Linux") > -1) {
          $("body, html").addClass("desk");
         } else {
          $("body, html").addClass("handheld");
         }
       }   
       
 

        if (!Array.prototype.find) {
              Object.defineProperty(Array.prototype, 'find', {
                value: function(predicate) {
                 // 1. Let O be ? ToObject(this value).
                  if (this == null) {
                    throw new TypeError('"this" is null or not defined');
                  }

                  var o = Object(this);

                  // 2. Let len be ? ToLength(? Get(O, "length")).
                  var len = o.length >>> 0;

                  // 3. If IsCallable(predicate) is false, throw a TypeError exception.
                  if (typeof predicate !== 'function') {
                    throw new TypeError('predicate must be a function');
                  }

                  // 4. If thisArg was supplied, let T be thisArg; else let T be undefined.
                  var thisArg = arguments[1];

                  // 5. Let k be 0.
                  var k = 0;

                  // 6. Repeat, while k < len
                  while (k < len) {
                    // a. Let Pk be ! ToString(k).
                    // b. Let kValue be ? Get(O, Pk).
                    // c. Let testResult be ToBoolean(? Call(predicate, T, « kValue, k, O »)).
                    // d. If testResult is true, return kValue.
                    var kValue = o[k];
                    if (predicate.call(thisArg, kValue, k, o)) {
                      return kValue;
                    }
                    // e. Increase k by 1.
                    k++;
                  }

                  // 7. Return undefined.
                  return undefined;
                }
              });
            }



        function stickyheader() {
            var header1 = $(".delivery");
            var header2 = $("#barre-filtres");
            var header3 = $("#logo");
            var header4 = $(".aspan");
            var header5 = $(".pac-container");
            var sticky = 250;
            var sticky2 = 240;
            if($('#adresse-input').length > 0) {
                var left = $('#adresse-input').offset().left;
                var top = $('#adresse-input').offset().top + 32;
            } else {
                var left;
                var top;
            }
            
            if( $("body.cms-home").length == 0 && 
                $("body.cms-page-view").length == 0 && 
                $("body.contact-index-index").length == 0) {
                   
                    header1.addClass("fixed fixednoanim");
                    header2.addClass("fixednoanim");
                    header3.addClass("fixednoanim");
                    header4.addClass("fixednoanim");
                    header5.addClass("fixednoanim").css("left", left).css("top", top);
                    $("#logo").css("border-left", Math.floor(($('.delivery').css('left').replace('px','') - $('#logo').css('width').replace('px','')) / 2) + "px solid #ff7b6a")

            } else {    

                if (window.pageYOffset >= sticky) {

                    if(header1.hasClass("fixednoanim")) {
                    } else {
                        if($("body.cms-home").length > 0) {
                            $(".step1").click();
                            $("#adresse-input").css("background","#003456").animate({backgroundColor: '#FFFFFF'}, 1000);
                        }
                    } 
                    header1.addClass("fixednoanim");
                    header2.addClass("fixednoanim");
                    header3.addClass("fixednoanim");
                    header4.addClass("fixednoanim");
                    header5.addClass("fixednoanim").css("left", left).css("top", top);
                    $("#logo").css("border-left", Math.floor(($('.delivery').css('left').replace('px','') - $('#logo').css('width').replace('px','')) / 2) + "px solid #ff7b6a")


                } else {
                    header1.removeClass("fixednoanim");
                    header1.removeClass("fixed");
                    header2.removeClass("fixednoanim");
                    header3.removeClass("fixednoanim");
                    header4.removeClass("fixednoanim");
                    header5.removeClass("fixednoanim").css("left", left).css("top", top);
                    $("#logo").css("border-left","");
                }


                if (window.pageYOffset >= sticky2) { 
                    header4.css("top",311-window.pageYOffset);
                } else { 
                    header4.css("top","");
                }
            }


        }
        stickyheader();
        window.onload = function() {stickyheader()};
        window.onresize = function() {stickyheader()};
        window.onscroll = function() {stickyheader()};
    }
});