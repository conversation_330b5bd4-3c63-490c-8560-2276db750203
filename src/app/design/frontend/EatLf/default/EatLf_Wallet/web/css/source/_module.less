//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .block-wallet-info .box-wallet {
    height: unset !important;
    color: #003456;
  }

  .block-wallet-info .box-content {
    line-height: 16px;
    margin-top: 10px;
    margin-bottom: 10px;
    color: #003456;
  }

  .box-title-big {
    font-size: 24px;
  }

  .box-step {
    font-weight: bold;
    font-size: 18px;
    text-transform: uppercase;
    margin-top: 18px;
    margin-bottom: 10px;
  }

  .wallet-wallet-credit {
    .wallet-customer-amount {
      padding: 0 10px;
    }

    .wallet-customer-amount::placeholder {
      color: #999;
      opacity: 1;
    }

    .wallet-custom-box {
      color:#003456;
      font-weight: bold;
      text-transform: capitalize;
    }

    .wallet-form-container {
      width:min-content
    }
  }
}

