@import 'libs/selectize.default.less';

@active_color: @button-primary__background;
@active_color_hover: @button-primary__hover__background;
@disabled_opacity: @button__disabled__opacity;
@error_color: @form-validation-note__color-error;
@focus_color: @focus__color;

@title_color: #343434;
@general_color: #676767;
@border_color: #E5E5E5;
@sub_color: #999999;

//@active_color: blue;
//@active_color_hover: darkblue;
//@disabled_opacity: 0.4;
//@error_color: yellow;
//@focus_color: red;
//
//@title_color: black;
@general_color: @lf-blue;
//@border_color: grey;
//@sub_color: lightblue;

@loader_color_spinner: @sub_color;
@loader_color_spinner_background: fade(@border_color, 40%);

@font_family_awesome: 'FontAwesome';
@default_border_radius: 4px;

@default_padding_top: 8px;
@default_padding_right: 16px;
@default_padding_bottom: 8px;
@default_padding_left: 16px;

@default_margin: 20px;
@default_margin_section: 24px;

@column_max_width: 368px;
@column_min_width: 288px;
@column_padding: @default_margin_section * 2;
@breakpoint_width: ((@column_min_width * 3) + (@column_padding * 3));

.onepage-index-index {
  .column.main {
    text-align: center;
  }
  .page-bottom {
    margin-top: 200px;
  }
}

.systempay-iframe.overlay {
  z-index:1!important;
}

.padding-default {
  padding-top: @default_padding_top;
  padding-right: @default_padding_right;
  padding-bottom: @default_padding_bottom;
  padding-left: @default_padding_left;
}

.one-column-breakpoint(@rules) {
  @media (max-width: @breakpoint_width) { @rules();
  }
}

@column_max_width_virtual: 590px;

.iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected,
.iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option.selected,
.iwd_main_wrapper .field .iwd_opc_select_container .iwd_opc_select_option:hover {
  background-color: rgba(229,229,229,0.4)!important;
}

.payment-method-content fieldset.systempay-form {
  padding: 0 30px;
}

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option {
  overflow:visible!important;
  white-space:normal!important;
  padding-bottom: 20px;
  border-bottom:1px solid @lf-blue!important;
  margin-bottom: 0!important;
  padding-top:20px;
}

.iwd_main_wrapper {

  margin: auto;
  display: inline-block;
  color: @general_color;
  font-size: 14px;
  max-width: 1400px;
  padding-left:30px;
  width: ~"calc(100% - 30px)";
  font-family: 'Open Sans', sans-serif;
  * {
    box-sizing: border-box;
  }
  .field-error {
    text-align: left;
  }
  .iwd_opc_column_name {
    line-height: 24px;
    font-size: 18px;
    text-align: center;
    font-weight: 600;
    color: @title_color;
    margin-bottom: @default_margin_section;
  }
  .iwd_opc_alternative_wrapper {
    position: relative;
    .iwd_opc_two_column_wrapper {
      .iwd_opc_column {
        &:first-child {
          padding-right: @column_padding - (@column_padding/2);
        }
        &:last-child {
          float: right;
          padding-left: @column_padding - (@column_padding/2);
          .iwd_opc_alternative_column {
            float: right;
            .iwd_opc_button {
              display: none;
            }
            .iwd_opc_review_items_totals {
              display: block;
            }
          }
        }
      }
    }
    .iwd_opc_alternative_column {

      text-align:center;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      .iwd_opc_column_content, .iwd_opc_column {
        outline: none;
        box-shadow: none;
      }
    }
  }

  .iwd_opc_collapsible_container {
    line-height: 19px;
    &.iwd_opc_collapsible_opened {
      .iwd_opc_collapsible_title {
        &:after {
          content: '\f106';
        }
      }
    }
    .iwd_opc_collapsible_title {
      color: @active_color;
      cursor: pointer;
      border-radius: @default_border_radius;
      display: block;
      &:after {
        font-family: @font_family_awesome;
        content: '\f107';
        padding-left: @default_padding_left/4;
      }
      &:hover {
        color: @active_color_hover;
      }
    }
    .iwd_opc_collapsible_content {
      .iwd_opc_collapsible_content_scrollable {
        border: 1px solid @border_color;
        overflow-wrap: break-word;
        .padding-default;
        max-height: 140px;
        font-size: 12px;
        line-height: 17px;
        width: 100%;
        border-radius: 4px;
        &:focus {
          box-shadow: 0 0 3px 1px @focus_color;
        }
      }
    }
  }

  .iwd_opc_clear {
    clear: both;
  }

  .iwd_opc_section_delimiter {
    height: 1px;
    // background: @border_color;
    width: ~'calc(100% - '(@default_margin*2)*3 ~')';
    margin: @default_margin_section auto @default_margin_section auto;
  }

  .iwd_opc_universal_wrapper {
    margin-bottom: @default_margin;
    position: relative;
    width: 100%;
    font-family: 'DinProBlack';
    color:@lf-blue;
    text-align: left;
    &.iwd_opc_message {
      font-size: 12px;
      text-align: center;
      line-height: 17px;
      padding: 0 @default_padding_right 0 @default_padding_left;
      cursor: pointer;
      .iwd_opc_message_success {
        color: @active_color;
      }
      .iwd_opc_message_error {
        color: @error_color;
      }
    }
    button {
      margin: 30px 30px 0;
      width: 240px;
      &.iwd_opc_place_order_button {
        width:100% !important;
        max-width: 350px;
        margin:0!important;
        background-image: url(../images/basket.svg);
        background-repeat: no-repeat;
        padding-left: 40px;
        padding-right: 10px;
        background-position:17% 10px;
        &:hover {
          background-image: url(../images/basket2.svg);
          color:@lf-blue!important;
        }
      }
    }
  }
  .iwd_opc_short_fields {
    display: flex;
    .iwd_opc_universal_wrapper {
      display: block;
      margin: 0;
      box-sizing: border-box;
      width: 50%;
      float: left;
      &:first-child {
        padding-right: calc(@default_padding_right / 2);
      }
      &:last-child {
        padding-left: calc(@default_padding_left / 2);
      }
    }
  }
  .iwd_opc_field, .field {
    select {
      /*margin: 0;
      padding: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      height: 1px;
      opacity: 0;
      z-index: -1;*/
      &.mage-error ~ .scroll-wrapper, &.mage-error ~ .iwd_opc_select_container {
        border-color: @error_color;
      }
    }
    .scroll-wrapper .scroll-content {
      border: none;
    }

    #iwd_opc_payment_method_select,
    #iwd_opc_shipping_method_group
    {
      & + div  div.iwd_opc_select_option {
        padding-left:0px!important;
        display: block!important;
        color: @lf-blue;
        font-size: 16px;
        min-height: 40px;
        height: auto!important;
        &.iwd_opc_cc_option_long {
          height:60px;
        }
        &.selected {
          background-color: #F5F5F5;
        }
        &.selected:after {
          width:12px;
          height: 12px;
          content:'';
          border-radius: 10px;
          background:@lf-blue;
          display: inline-block;
          position: relative;
          float: left;
          left:-28px;
          top:1px;
          margin-right: -14px;
        }

        &:before {
          width:20px;
          height: 20px;
          content:'';
          border-radius: 10px;
          border:1px solid @lf-blue;
          display: inline-block;
          margin-right: 10px;
          position: relative;
          float: left;
          margin-top: -3px;
          margin-bottom: 1px;
        }
        &:after {
          display:none;
        }
      }
    }
    .scroll-wrapper,
    .iwd_opc_select_container {
      &[data-element-id="iwd_opc_shipping_method_group"], &[data-element-id="iwd_opc_shipping_method_rates"] {
        max-height: 368px;
      }
      border:0;
      outline: none;
      width: 100%;
      max-height: 250px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      padding-bottom:5px;
      box-shadow:none!important;
      &.selected {
        .iwd_opc_select_option {
          display: none;
          border-bottom: 0;
          &.selected {
            display: block;
            background-color: inherit;
            padding-left:0;
            padding-right: 30px;
            &[data-value=''] {
              color: @general_color;
            }
          }
          &:after {
            content: '';
            border-bottom: 2px solid #003456;
            border-right: 2px solid #003456;
            transform: rotate(45deg);
            width: 11px;
            height: 11px;
            display: block;
            position: absolute;
            top: 0px;
            right: 5px;
          }
          &:hover {
            background-color: inherit;
          }
        }
      }
      &.disabled {
        cursor: default;
        .iwd_opc_select_option, .iwd_opc_select_option.selected {
          opacity: @disabled_opacity;
          color: @general_color;
          &[data-value=''] {
            color: @general_color;
          }
        }
        &:focus {
          box-shadow: none;
        }
      }
      .iwd_opc_select_option {
        .padding-default;
        padding-left: 0;
        color: @title_color;
        font-size: 16px;
        line-height: 19px;
        border-bottom: 1px solid fade(@border_color, 40%);
        overflow: hidden;
        box-sizing: border-box;
        text-align: left;
        &.selected, &:hover {
          // background-color: fade(@border_color, 40%);
        }
        &:last-child {
          border-bottom: none;
        }
      }
      &:focus {
        box-shadow: 0 0 3px 1px @focus_color;
      }
    }
    &:not(.choice) {
      label, label.label,
      legend.label,
      .label {
        display: none;
      }
      .control {
        width:100%!important;
      }
    }

    .selectize-control {
      .selectize-dropdown [data-selectable] {
        font-size: 16px;
      }
      .selectize-dropdown-content {
        text-align: left;
      }
      &.single {
        .selectize-input {
          padding-top:0!important;
          .item {
            padding:0!important;
          }
          &:after {

            content: '';
            border-bottom: 2px solid #003456;
            border-right: 2px solid #003456;
            transform: rotate(45deg);
            width: 11px;
            height: 11px;
            display: block;
            position: absolute;
            top: 0px;
            right: 5px;

          }
        }
      }
    }

    &.iwd_opc_input,
    .input-text,
    textarea,
    .selectize-input,
    .iwd_opc_hosted_field,
    &.iwd_opc_textarea {
      background: inherit;
      border:0!important;
      border-radius: 0!important;
      border-bottom: 1px solid @lf-blue!important;
      font-family: 'Eczar', arial;
      .padding-default;
      padding-left: 0!important;
      font-size: 16px;
      line-height: 19px;
      outline: none;
      padding-bottom:18px;
      height: 37px;
      width: 100%;
      color: @lf-blue!important;
      text-align: left;
      &:focus {
        box-shadow: 0 0 3px 1px @focus_color;
      }
      &:invalid, &:-moz-ui-invalid {
        outline: none;
      }
      &::-moz-placeholder {
        color: #BBBBBB;
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      &:-moz-placeholder {
        color: #BBBBBB;
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      &:-ms-input-placeholder {
        color:#BBBBBB;
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      &::-webkit-input-placeholder {
        opacity: 1;
        transition: opacity 0.3s ease;
        color: #BBBBBB;
      }
      &:disabled, &[readonly] {
        opacity: @disabled_opacity;
        pointer-events: none;
      }
      &.mage-error {
        border-color: @error_color;
      }
    }
    textarea,
    input[name='reference'] {

      &::-moz-placeholder {
        font-size:15px;
      }
      &:-moz-placeholder {
        font-size:15px;
      }
      &:-ms-input-placeholder {
        font-size:15px;
      }
      &::-webkit-input-placeholder {
        font-size:15px;
      }
    }
    .iwd_opc_hosted_field {
      height: 37px;
      &.braintree-hosted-fields-focused {
        box-shadow: 0 0 3px 1px @focus_color;
      }
      &.braintree-hosted-fields-invalid {
        border-color: @error_color;
      }
    }
    &.iwd_opc_textarea,
    textarea {
      max-width: 100%;
      overflow: hidden;
      //line-height: inherit;
      resize: none;
      vertical-align: top;
      //display: block;
      height: 100px!important;
    }
    &[type="password"]:not(:-ms-input-placeholder) {
      font-size: 34px;
      line-height: 19px;
      height: 37px;
      letter-spacing: -1px;
    }
    &[type="password"]:not(:placeholder-shown) {
      font-size: 34px;
      line-height: 19px;
      height: 37px;
      letter-spacing: -1px;
    }
  }

  .fieldset {
    margin: 0;
    padding: 0;
    .field {
      padding: 0;
      margin: 0 0 15px 0;
      &._required:not(._disabled) .input-text::-ms-input-placeholder {
        border: 3px solid @lf-gold !important;
        padding: 6px!important;
        height: 45px!important;
      }

      &._required:not(._disabled) .input-text:placeholder-shown {
        border: 3px solid #bda25a !important;
        padding: 6px!important;
        height: 45px!important;
      }



      &.choice {
        position: relative;
        text-align: left;
        &:before  {
          display:none;
        }
        input[type="checkbox"] {
          position: absolute;
          top: 0;
          left: 0;
          opacity: 0;
          z-index: -1;
          &:focus ~ label:before {
            box-shadow: 0 0 3px 1px @focus_color;
          }
          &:checked ~ label:before {
            background: @lf-blue;
            content:'✓';
            color:white;
          }
          &.mage-error ~ label {
            &:before {
              border-color: @error_color;
            }
          }
          & ~ label {
            cursor: pointer;
            font-size: 16px;
            line-height: 19px;
            position: relative;
            display: block;
            padding-left: 30px;
            padding-top: 5px;
            &:before {
              content: '';
              font-family: @font_family_awesome;
              width: 20px;
              height: 20px;
              border-radius: @default_border_radius;
              border: 1px solid @active_color;
              display: inline-block;
              position: absolute;
              left: 0;
              top: 2px;
              font-size: 25px;
              text-align: center;
            }
          }
        }
      }
      .control {
        position: relative;
        .message.warning {
          display: none;
        }
      }
      ._with-tooltip {
        .input-text {
          width: 100%;
          margin: 0;
        }
      }
      .field-tooltip {
        display: none;
      }
      &._error, &._warn {
        .control {
          input, textarea, .scroll-wrapper, .iwd_opc_select_container {
            border-color: @error_color;
          }
        }
      }
      &.additional {
        margin-bottom: @default_margin/2;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  div.mage-error[generated] {
    display: none !important;
  }

  .iwd_opc_show_hide_password {
    font-family: @font_family_awesome;
    color: @active_color;
    font-size: 14px;
    position: absolute;
    width: 15px;
    height: 15px;
    display: inline-block;
    right: @default_padding_right + 2px;
    top: 11px;
    line-height: 1;
    cursor: pointer;
    &.active {
      &:after {
        content: "\f070";
      }
    }
    &:after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: "\f06e";
    }
    &:hover {
      color: @active_color_hover;
    }
  }

  .iwd_opc_collapsible_opened {
    .with_angle:after {
      content: '\f106' !important;
    }
  }

  button.iwd_opc_small_button,
  a.iwd_opc_small_button,
  div.iwd_opc_small_button,
  a.iwd_opc_button,
  div.iwd_opc_button,
  button.iwd_opc_button {
    font-weight: 400;
    display: block;
    box-sizing: border-box;
    line-height: 19px;
    box-shadow: none;
    font-size: 14px;
    text-align: center;
    border-radius: @default_border_radius;
    width: 100%;
    .padding-default;
    outline: none;
    margin: 0;
    background: #fff;
    border: 1px solid @general_color;
    color: @general_color;
    cursor: pointer;
    position: relative;
    &.with_angle:after {
      font-family: @font_family_awesome;
      font-size: 14px;
      position: absolute;
      right: @default_padding_right;
    }
    &.with_angle.opened:after {
      content: '\f106';
    }
    &.with_angle:after {
      content: '\f107';
    }
    &:focus, &:active, &:hover {
      box-shadow: none;
      color: @title_color;
      border-color: @title_color;
      text-decoration: none;
    }
    &:disabled {
      opacity: @disabled_opacity;
      cursor: default;
    }
    &.active {
      color: @active_color;
      border-color: @active_color;
      &:hover, &:focus, &:active {
        color: @active_color_hover;
        border-color: @active_color_hover;
      }
    }
  }

  button.iwd_opc_small_button,
  div.iwd_opc_small_button,
  a.iwd_opc_small_button,
  div.iwd_opc_small_button {
    padding-right: @default_padding_right/2;
    padding-left: @default_padding_left/2;
    display: block;
    box-sizing: border-box;
    width: 48%;
    overflow: hidden;
    &:first-child {
      float: left;
    }
    &:last-child {
      float: right;
    }
  }

  .iwd_opc_small_button_container {
    overflow: hidden;
  }

  .loading-mask {
    .loader {
      width: 71px !important;
      height: 71px !important;;
    }
  }

  .iwd_opc_field_tooltip {
    position: absolute;
    cursor: pointer;
    top: @default_padding_top + 1px;
    right: @default_padding_right + 1px;
    line-height: 19px;
    &:hover {
      .iwd_opc_field_tooltip_content {
        display: block;
      }
    }
    &:after {
      content: attr(data-icon);
      font-size: 14px;
      line-height: 14px;
      font-family: @font_family_awesome;
    }
    .iwd_opc_field_tooltip_content {
      box-shadow: 0 4px 8px 0 fade(@title_color, 20%);
      background: #fff;
      width: 197px;
      .padding-default;
      z-index: 100;
      display: none;
      position: absolute;
      text-align: left;
      line-height: 17px;
      font-size: 12px;
      right: 100%;
      margin-right: 14px;
      border-radius: @default_border_radius;
      &:after {
        left: ~'calc(100% - 12px)';
        top: ~'calc(50% - 6px)';
        content: " ";
        height: 24px;
        width: 12px;
        position: absolute;
        background: #fff;
      }
      &:before {
        box-shadow: 1px 1px 8px 0 fade(@title_color, 20%);
        content: '';
        position: absolute;
        left: ~'calc(100% - 6px)';
        top: 50%;
        width: 12px;
        height: 12px;
        background: #fff;
        transform: rotate(45deg);
        z-index: -1;
      }
    }
  }
  //.control .scroll-wrapper{
  //  border-radius: @default_border_radius;
  //  border: 1px solid @border_color;
  //}
  .scroll-wrapper {
    overflow: hidden;
    padding: 0;
    position: relative;
    outline: none;
    margin: 0;
    //&.focused, &:focus {
    //  box-shadow: 0 0 3px 1px @focus_color;
    //}
    //.iwd_opc_select_container:focus {
    //  box-shadow: none;
    //}
    .scroll-content {
      outline: none;
      //border: none !important;
      //box-sizing: content-box !important;
      height: auto;
      left: 0;
      //margin: 0;
      max-width: none !important;
      overflow-y: scroll !important;
      //padding: 0;
      position: relative !important;
      top: 0;
      width: auto !important;
      &::-webkit-scrollbar {
        height: 0;
        width: 0;
      }
      &.scroll-scrolly_visible:not(.selected) {
        ~ .scroll-y.scroll-scrolly_visible {
          display: block;
        }
      }
    }
    .scroll-element {
      display: none;
      box-sizing: content-box;
      background: none;
      border: none;
      margin: 0;
      padding: 0;
      position: absolute;
      z-index: 10;
      div {
        box-sizing: content-box;
        background: none;
        border: none;
        margin: 0;
        padding: 0;
        position: absolute;
        z-index: 10;
        display: block;
        height: 100%;
        left: 0;
        top: 0;
        width: 100%;
      }
      &.scroll-y {
        height: 100%;
        min-height: 100%;
        right: 0;
        top: 0;
        width: 0;
        &.scroll-scrolly_visible {
          //display: block;
          .scroll-element_outer {
            display: block;
            height: 100%;
            left: 0;
            width: 100%;
            top: 8px;
            .scroll-element_size {
              top: -16px;
            }
            .scroll-element_track {
              display: none;
            }
            .scroll-bar {
              left: -16px;
              min-height: 80px;
              width: 8px;
              cursor: default;
              background-color: @title_color;
              opacity: 0.4;
              display: block;
              border-radius: @default_border_radius;
              -webkit-transition: opacity 0.1s linear;
              -moz-transition: opacity 0.1s linear;
              -o-transition: opacity 0.1s linear;
              transition: opacity 0.1s linear;
              &:hover, &:active {
                background-color: @title_color;
              }
            }
          }
        }
      }
    }
  }
  .iwd-group {
    width: 100%;
  }
}

.alias-name {
  display: inline-block;
  width:46%;
  font-weight: bold;
  float: left;
}
.alias-pan {
  width: 0%;
  white-space: nowrap;
}

.iwd_opc_select_option.selected .payment-method:not(._active),
.iwd_opc_select_option:not(.selected) .payment-method,
.iwd_opc_select_option:not(.selected) .payment-method._active  {
  display:none!important;
}


.iwd_opc_select_option.selected .payment-method._active {
  display:block;
  margin-top:15px;
  .iwd_opc_universal_wrapper {
    margin-bottom: 0;
  }
}
.iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option:not(.selected):before {
  background: white;
  border: 1px solid @lf-blue;
  box-shadow: 0px;
  width: 20px;
  height: 20px;
  margin-top: 0px;
  margin-left: 0px;
  border-radius: 20px;
}
.iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option.selected:after {
  display: none;
}
.iwd_main_wrapper .field #iwd_opc_payment_method_select + div div.iwd_opc_select_option.selected:before {
  background: @lf-blue;
  border: 3px solid #fff;
  box-shadow: 0px 0px 0px 1px @lf-blue;
  width: 14px;
  height: 14px;
  margin-top: 1px;
  margin-left: 1px;
  margin-right: 11px;
}



.iwd_main_wrapper .field .iwd_opc_select_container {
  max-height: none;
}

.checkout-onepage-success {
  .tunnel {
    position: relative;
    margin-top: 40px;
    width: 100%;
    margin-bottom: 100px;
  }
}

.onepage-index-index,
.iwd_opc_success_page {

  .tunnel {
    width:100%;
    margin-top:40px;
  }




  #checkout {
    margin-top:110px;
  }
  .loading-mask {
    background: rgba(255, 255, 255, 0.4);
    .loader {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 100001;
      margin: auto;
      border-top: 11px solid @loader_color_spinner_background;
      border-right: 11px solid @loader_color_spinner_background;
      border-bottom: 11px solid @loader_color_spinner_background;
      border-left: 11px solid @loader_color_spinner;
      -webkit-transform: translateZ(0);
      -ms-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-animation: load8 1.1s infinite linear;
      animation: load8 1.1s infinite linear;
      border-radius: 50%;
      width: 49px;
      height: 49px;
      @-webkit-keyframes load8 {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
      @keyframes load8 {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
      img {
        display: none;
      }
    }
  }
}


.iwd_success_page_wrapper {
  .iwd_opc_alternative_wrapper {
    .iwd_opc_success_page_column {
      .iwd_opc_alternative_column {
        margin: 0 auto;
        .iwd_success_page_info, .iwd_opc_success_page_create_account_info {
          padding: 0 @default_margin_section 0 @default_margin_section;
          text-align: center;

          h3 {
            letter-spacing:3px;
            small {
              color:@lf-blue;
              font-size: 16px;
              font-family: 'DinProBlack';
              text-transform: uppercase;
            }
          }
          a {
            color: @active_color;
            &:hover {
              color: @active_color_hover;
              text-decoration: none;
            }
          }
          button {
            margin:10px 50px;
            width:220px;
            background:white;
            font-size:14px;
            color:@lf-blue;
            font-family:'DinProBold';
            border:2px solid @lf-blue;
          }
        }
      }
    }
  }
}

.iwd_empty_cart_wrapper {
  .iwd_opc_universal_wrapper {
    text-align: center;
  }
  .iwd_opc_alternative_wrapper {
    .iwd_opc_empty_cart_column {
      .iwd_opc_alternative_column {
        margin: 0 auto;
        .iwd_opc_empty_cart_info {
          padding: 0 @default_margin_section 0 @default_margin_section;
          text-align: center;
        }
        .iwd_empty_cart_powered_by {
          text-align: center;
          margin-bottom: 0;
          a {
            color: @active_color;
            &:hover {
              color: @active_color_hover;
              text-decoration: none;
            }
          }
        }
      }
    }
  }
}

.iwd_opc_wrapper {
  #iwd_opc_powered_by_logo {
    overflow: hidden;
    a {
      float: right;
      display: inline-block;
      img {
        width: 69px;
      }
    }
  }
  .iwd_opc_alternative_wrapper {
    > .iwd_opc_two_column_wrapper:first-child {
      .iwd_opc_column {
        width: 50%;
        .iwd_opc_alternative_column {
          max-width: @column_max_width_virtual;
          @media (max-width: @column_max_width) {
            max-width: @column_min_width;
          }
        }
      }
    }
    .iwd_opc_column {
      float: left;
      &.iwd_opc_iframe_payment_column {
        width: 100%;
        float: none;
        .iwd_opc_alternative_column {
          margin: 0 auto;
          max-width: @column_max_width_virtual;
          .iwd_opc_column_name {
            color: @active_color;
            line-height: 18px;
            cursor: pointer;
            &:hover {
              color: @active_color_hover;
            }
            &:before {
              font-family: @font_family_awesome;
              color: @general_color;
              content: '\f104';
              margin-right: 10px;
            }
          }
          .iwd_opc_column_content {
            text-align: center;
            margin: 0 auto;
          }
        }
      }
      &.iwd_opc_address_column {
        padding-right: @column_padding - (@column_padding/2);
        .iwd_opc_alternative_column {
          float: left;
        }
      }
      &.iwd_opc_shipping_column {
        padding: 0 @column_padding/2;
        .iwd_opc_alternative_column {
          margin: 0 auto;
          float: none;
        }
      }
      &.iwd_opc_payment_column {
        padding-left: @column_padding - (@column_padding/2);
        margin-bottom: 0 !important;
        .iwd_opc_alternative_column {
          float: right;
          .loading-mask {
            overflow: hidden;
          }
        }
        .checkout-agreement {
          div.mage-error[generated] {
            display: block !important;
          }
        }
      }
    }
  }
  .iwd_opc_alternative_wrapper {
    .iwd_opc_general_errors {
      cursor: pointer;
      .message.error, .message.success {
        margin-bottom: @default_margin;
      }
    }
    .iwd_opc_column {
      width: 32.6881%;
      position: relative;
      float: left;
      .iwd_opc_alternative_column {
        max-width: inherit;
      }
      &.iwd_opc_address_column {
        padding-right: @column_padding - (@column_padding/2);
        .iwd_opc_alternative_column {
          float: left;
        }
      }
      &.iwd_opc_shipping_column {
        padding: 0 @column_padding/2;
        width: 35.6233%;
        .iwd_opc_alternative_column {
          margin: 0 auto;
          float: none;
        }
      }
      &.iwd_opc_payment_column {
        padding-left: @column_padding - (@column_padding/2);
        margin-bottom: 0 !important;
        width:34%;
        margin-top: -6px;
        input[name='reference'] {
          margin-bottom: 25px;
          height: 48px;
          margin-top:5px;
        }
        .iwd_opc_alternative_column {
          float: right;
        }
      }
      .one-column-breakpoint({ float: none !important; width: 100% !important; padding: 0 !important; margin-bottom: @default_margin_section * 2; .iwd_opc_alternative_column {
        margin: 0 auto !important;
        float: none !important;
        max-width: @column_max_width;
      } });
      @media (max-width: @column_max_width) {
        .iwd_opc_alternative_column {
          max-width: 328px;
        }
      }
    }
    .iwd_opc_additional_payment_info {
      font-size: 12px;
      line-height: 21px;
      padding-left: @default_padding_left;
      padding-right: @default_padding_right;
      .iwd_opc_additional_payment_label {
        display: inline;
        padding-right: @default_padding_right/4;
      }
      .iwd_opc_additional_payment_value {
        display: inline;
        font-weight: bold;
      }
    }
    .iwd_opc_field_tooltip {
      &.iwd_opc_shipping_method_tooltip, &.iwd_opc_payment_method_tooltip {
        .iwd_opc_field_tooltip_content {
          top: -30px;
          &:after {
            top: ~'calc(34px - 6px)';
          }
          &:before {
            top: 34px;
          }
        }
      }
      &.iwd_opc_payment_method_tooltip_virtual {
        .iwd_opc_field_tooltip_content {
          top: -7px;
          &:after {
            top: ~'calc(11px - 6px)';
          }
          &:before {
            top: 11px;
          }
        }
      }
      &.iwd_opc_cvv_tooltip {
        .iwd_opc_field_tooltip_content {
          top: -24px;
          &:after {
            top: ~'calc(28px - 6px)';
          }
          &:before {
            top: 28px;
          }
        }
      }
      &.iwd_opc_cc_tooltip {
        .iwd_opc_field_tooltip_content {
          top: -38px;
          &:after {
            top: ~'calc(42px - 6px)';
          }
          &:before {
            top: 42px;
          }
        }
      }
    }
    #iwd_opc_top {
      margin-bottom: @default_margin_section*2;
      display: inline-block;
      .one-column-breakpoint({ margin-bottom: @default_margin_section; .iwd_opc_column {
        margin-bottom: @default_margin_section;
      } });
      #iwd_opc_review {
        .iwd_opc_review_grand_total_tax {
          color: @sub_color;
          display: inline;
        }
        #iwd_opc_review_items_totals {
          margin-top: 40px;
          background: #f6f6f6;
          position: absolute;
          width: 100%;
          z-index: 1;
          //.padding-default;
          color: @lf-blue;
          display: block!important;
          #iwd_opc_top_review_items_wrapper {
            max-height: 500px;
          }
          #iwd_opc_review_items {
            display: none;
            position: relative;
            width: 100%;
            background: #fff;
            //margin-bottom: @default_padding_top*2;
            padding: 0 @default_padding_left 0 @default_padding_right;
            .iwd_opc_review_item {
              display: table-row;
              &:first-child {
                .iwd_opc_review_item_cell {
                  padding-top: @default_padding_top;
                }
              }
              .iwd_opc_review_item_cell {
                display: table-cell;
                text-align: right;
                line-height: 21px;
                width: 40%;
                padding-bottom: @default_padding_bottom;
                border-bottom: 1px solid @border_color;
                padding-top: @default_padding_top*2;
                padding-left: @default_margin_section/2;
                &:first-child {
                  text-align: left;
                  width: 60%;
                  padding-right: @default_margin_section/2;
                  padding-left: 0
                }
                .iwd_opc_review_item_name {
                  padding-bottom: @default_padding_bottom;
                  font-weight: 600;
                  line-height: 21px;
                }
                .iwd_opc_review_item_subtotal {
                  padding-bottom: @default_padding_bottom;
                  &:before {
                    padding-right: @default_padding_right/2;
                    color: @sub_color;
                    content: attr(data-item-tax);
                  }
                  &:first-child:before {
                    content: attr(data-item-qty);
                  }
                }
                .iwd_opc_review_item_options {
                  font-size: 12px;
                  .iwd_opc_review_item_option {
                    padding-bottom: @default_padding_bottom;
                    .iwd_opc_review_item_option_label {
                      display: inline;
                      word-wrap: break-word;
                      word-break: break-word;
                    }
                    .iwd_opc_review_item_option_value {
                      color: @sub_color;
                      display: inline;
                      word-wrap: break-word;
                      word-break: break-word;
                      a {
                        color: @active_color;
                        font-size: 12px;
                      }
                    }
                  }
                }
              }
            }
          }
          #iwd_opc_review_totals {
            display: block;
            position: relative;
            width: 100%;
            background: #f6f6f6;
            font-weight: 600;
            margin-top: @default_padding_top*2;
            padding: 10px 26px 26px 26px;
            min-height: 500px;
            h3 {
              text-align: left;
              margin-top: 0;
              margin-bottom: 30px;
            }
            .iwd_opc_review_total {
              display: block;
              font-family: 'DinProBold';

              &.iwd_opc_review_total_tax {
                color: @sub_color;
              }
              .iwd_opc_review_total_cell {
                padding-bottom:16px;
                display: inline-block;
                width: 58%;
                line-height: 21px;
                text-align: left;
                text-transform: uppercase;
                font-size: 14px;
                &:last-child {
                  width: 39%;
                  text-align: right;
                  font-size: 18px;
                  white-space: nowrap;
                }
              }
              &.iwd_opc_grand_total {
                .iwd_opc_review_total_cell:last-child {
                  font-size:22px;
                  &:after {
                    content:' TTC';
                    font-size: 11px;
                    letter-spacing: 1px;
                    display: inline-block;
                    top: -8px;
                    position: relative;
                    margin-left: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
    #checkout-payment-method-load {
      .iwd_opc_cc_wrapper {
        position: relative;
        &:before {
          position: absolute;
          border-radius: @default_border_radius;
          display: inline-block;
          line-height: 1;
          width: 42px;
          height: 26px;
          background-image: url('../images/icons1x.png');
        }
        &[data-cc-type="AE"], &[data-cc-type="AMEX"] {
          &:before {
            content: '';
            background-position: -199px -19px;
          }
        }
        &[data-cc-type="VI"], &[data-cc-type="VISA"], &[data-cc-type="DELTA"], &[data-cc-type="UKE"] {
          &:before {
            content: '';
            background-position: -19px -19px;
          }
        }
        &[data-cc-type="MC"], &[data-cc-type="MCDEBIT"] {
          &:before {
            content: '';
            background-position: -79px -19px;
          }
        }
        &[data-cc-type="DI"], &[data-cc-type="DC"] {
          &:before {
            content: '';
            background-position: -139px -19px;
          }
        }
        &[data-cc-type="DICL"], &[data-cc-type="DN"] {
          &:before {
            content: '';
            background-position: -139px -63px;
          }
        }
        &[data-cc-type="CUP"] {
          &:before {
            content: '';
            background-position: -199px -63px;
          }
        }
        &[data-cc-type="SO"], &[data-cc-type="SOLO"] {
          &:before {
            content: '';
            background-position: -139px -107px;
          }
        }
        &[data-cc-type="ME"], &[data-cc-type="MAESTRO"], &[data-cc-type="MI"] {
          &:before {
            content: '';
            background-position: -19px -63px;
          }
        }
        &[data-cc-type="MD"] {
          &:before {
            content: '';
            background-position: -139px -151px;
          }
        }
        &[data-cc-type="JCB"] {
          &:before {
            content: '';
            background-position: -259px -19px;
          }
        }
        &[data-cc-type="SM"], &[data-cc-type="SWITCH"] {
          &:before {
            content: '';
            background-position: -79px -107px;
          }
        }
        @media (-webkit-min-device-pixel-ratio: 2), (min--moz-device-pixel-ratio: 2), (-o-min-device-pixel-ratio: 4 / 2), (min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx) {
          &:before {
            background-image: url('../IWD_Opc/images/icons2x.png');
            background-size: 258px;
          }

          &[data-cc-type="AE"], &[data-cc-type="AMEX"] {
            &:before {
              content: '';
              background-position: -158px 136px;
            }
          }

          &[data-cc-type="VI"], &[data-cc-type="VISA"], &[data-cc-type="DELTA"], &[data-cc-type="UKE"] {
            &:before {
              content: '';
              background-position: -9px 136px;
            }
          }

          &[data-cc-type="MC"], &[data-cc-type="MCDEBIT"] {
            &:before {
              content: '';
              background-position: -58px 136px;
            }
          }

          &[data-cc-type="DI"], &[data-cc-type="DC"] {
            &:before {
              content: '';
              background-position: -108px 136px;
            }
          }

          &[data-cc-type="DICL"], &[data-cc-type="DN"] {
            &:before {
              content: '';
              background-position: -108px 102px;
            }
          }

          &[data-cc-type="CUP"] {
            &:before {
              content: '';
              background-position: -158px 102px;
            }
          }

          &[data-cc-type="SO"], &[data-cc-type="SOLO"] {
            &:before {
              content: '';
              background-position: -108px 69px;
            }
          }

          &[data-cc-type="ME"], &[data-cc-type="MAESTRO"], &[data-cc-type="MI"] {
            &:before {
              content: '';
              background-position: -9px 102px;
            }
          }

          &[data-cc-type="MD"] {
            &:before {
              content: '';
              background-position: -108px 136px;
            }
          }

          &[data-cc-type="JCB"] {
            &:before {
              content: '';
              background-position: -207px 136px;
            }
          }

          &[data-cc-type="SM"], &[data-cc-type="SWITCH"] {
            &:before {
              content: '';
              background-position: -58px 69px;
            }
          }
        }
      }
      .iwd_opc_field, .field {
        .iwd_opc_select_container {
          &.selected .iwd_opc_select_option {
            &.iwd_opc_cc_option_short, &.iwd_opc_cc_option_long {
              .iwd_opc_cc_preview, .iwd_opc_cc_types_tooltip {
                display: none;
              }
            }

            &.iwd_opc_option_with_image {
              //padding-right: @default_padding_right + 40px + 5px + 18px;

            }
          }
          .iwd_opc_select_option {
            &.iwd_opc_cc_option_long {
              height:70px;
              white-space:initial;
            }
            &.iwd_opc_option_with_image {
              padding-right:0;
              position: relative;
              img.iwd_opc_option_image {
                width: 40px;
                height: 24px;
                right:0;
                position: absolute;
                bottom: 0;
                margin: auto;
                top: 0;
              }
            }
          }
          &:not(.selected) {
            .iwd_opc_select_option {
              &.iwd_opc_cc_option_short, &.iwd_opc_cc_option_long {
                padding-right:0;
                position: relative;
                overflow: visible;

                .iwd_opc_cc_wrapper {
                  width: 42px;
                  height: 26px;
                  float: left;
                  margin-left: 5px;
                }
                .iwd_opc_cc_preview {
                  display: inline-block;
                  position: absolute;
                  top: 30px;
                  height: 26px;
                  right: @default_padding_right + 24px + 5px;
                }
                .iwd_opc_cc_types_tooltip {
                  top: 31px;
                  width: 24px;
                  height: 24px;
                  border-radius: @default_border_radius;
                  border: 1px solid @active_color;
                  .iwd_opc_field_tooltip_content {
                    width: 215px;
                    position: fixed;
                    &.iwd_opc_cc_tooltip_content_small {
                      &:before {
                        top: 16px;
                      }
                      &:after {
                        top: ~'calc(16px - 6px)';
                      }
                    }
                    &.iwd_opc_cc_tooltip_content_big {
                      &:before {
                        top: 31px;
                      }
                      &:after {
                        top: ~'calc(31px - 6px)';
                      }
                    }
                    .iwd_opc_cc_wrapper {
                      margin-top: 5px;
                      margin-left: 0;
                      margin-right: 5px;
                      &:nth-child(4n) {
                        margin-right: 0;
                      }
                      &:nth-child(-n+4) {
                        margin-top: 0;
                      }
                    }
                  }
                  &:hover {
                    &:after, &:before {
                      background: @active_color_hover;
                    }
                  }
                  &:after, &:before {
                    background: @active_color;
                    content: '';
                    display: inline-block;
                    position: absolute;
                    border-radius: @default_border_radius;
                  }
                  &:after {
                    top: 10px;
                    left: 5px;
                    width: 12px;
                    height: 2px;
                  }
                  &:before {
                    top: 5px;
                    left: 10px;
                    content: '';
                    position: absolute;
                    height: 12px;
                    width: 2px;
                    background: @active_color;
                  }
                }
              }
              &.iwd_opc_cc_option_short {
                padding-right: @default_padding_right + 40px + 40px + 10px;
                .iwd_opc_cc_preview {
                  right: @default_padding_right + 1px;
                }
              }
            }
          }
        }
      }
      .payment-method {
        display: none;
        &._active {
          display: block;
        }
        .payment-method-title {
          display: none;
        }
        .payment-method-content {
          .iwd_opc_cc_wrapper {
            &:before {
              right: @default_padding_right;
              top: 5.5px;
            }
            &[data-https="1"] {
              &:before {
                right: @default_padding_right * 2.5;
              }
            }
          }
          iframe {
            display: block;
            height: 630px;
            min-height: 630px;
            overflow: hidden;
            z-index:2;
            width:450px;
            border:none;

            @media only screen and (max-width:1350px) {
              height: 660px;
              min-height: 660px;
              width:470px;
            }

            @media only screen and (max-width:1300px) {
              height: 620px;
              min-height: 620px;
              width:470px;
            }

            @media only screen and (max-width:1200px) {
              width:520px;
              height: 730px;
              min-height: 730px;
            }
            @media only screen and (max-width:1100px) {
              width:620px;
              height: 840px;
              min-height: 840px;
            }
          }
          .systempay-iframe.warning {
            z-index:2;
          }
          .iwd_opc_gateway_payment {
            height: 190px;
            background: url('../IWD_Opc/images/redirect.png') center center no-repeat;
            background-size: contain;
          }
          .iwd_opc_payment_instructions {
            .iwd_opc_payment_instructions_title {
              font-size: 14px;
              line-height: 21px;
              text-align: left;
            }
            .iwd_opc_payment_instructions_small {
              color: @general_color;
              text-align: left;
              max-width: 50%;
            }
            color: @active_color;
            font-size: 12px;
            line-height: 17px;
            text-align: center;
            padding: 0 @default_padding_right 0 @default_padding_left;
          }
          .actions-toolbar {
            display: none;
          }
        }
        .iwd_opc_hosted_label {
          display: block;
        }
      }
    }
    #iwd_opc_login {
      #iwd_opc_login_captcha {
        .iwd_opc_captcha_image {
          text-align: center;
        }
        .iwd_opc_captcha_reload_sensitive_container {
          font-size: 12px;
          line-height: 17px;
          text-align: center;
          .iwd_opc_case_sensitive_captcha {
            display: inline;
          }
          .iwd_opc_refresh_captcha_button {
            display: inline;
            cursor: pointer;
            color: @active_color;
            text-transform: capitalize;
            &:hover {
              color: @active_color_hover;
              text-decoration: underline;
            }
          }
        }
      }
    }
    #iwd_opc_gift_message {
      .iwd_opc_field, .field {
        .iwd_opc_select_container {
          .iwd_opc_select_option {
            img.iwd_opc_option_image {
              width: 19px;
              height: 19px;
              box-sizing: border-box;
              border: 1px solid @border_color;
              margin-left: @default_padding_left;
              border-radius: @default_border_radius;
            }
          }
        }
      }
    }
    .iwd_opc_field, .field {
      .iwd_opc_select_container {
        .iwd_opc_select_option {
          img.iwd_opc_option_image {
            float: right;
          }
        }
      }
    }
    button.iwd_opc_button.iwd_opc_place_order_button.active {
      color: #fff;
      background: @active_color;
      font-weight: 600;
      &:hover, &:focus, &:active {
        color: #fff;
        background: @active_color_hover;
      }
    }
  }
  #payment_form_iwd_authcim #iwd_authcim_cc_cid {
    max-width: inherit;
  }
}

.onepage-index-index {
  .nav-sections,
  .nav-toggle {
    display: none;
  }
  #iwd_opc_discount {
    display: none;
  }
  .logo {
    margin-left: 0;
  }
}

.iwd_opc_popup_wrapper {
  display: none;
  position: fixed;
  top: 0;
  margin: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 105;
  width: 100%;
  height: 100%;

  &.active {
    display: block;
  }
  .iwd_opc_popup {
    position: absolute;
    top: 40%;
    left: 50%;
    width: 34.6233%;
    padding: 0 @column_padding/2;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -40%);
    max-height: 85%;
    //overflow: auto;
    .one-column-breakpoint({ width: 100%; padding: 0; max-width: @column_max_width; min-width: @column_min_width; });
    .iwd_opc_popup_content {
      background: #fff;
      border-radius: @default_border_radius;
      outline: none;
      padding: @default_padding_right;
      //overflow: auto;
      box-shadow: 0 4px 8px 0 rgba(52, 52, 52, 0.2);
      max-width: @column_max_width;
      margin: 0 auto;
    }
  }
  .iwd_opc_popup_mask {
    position: relative;
    padding: inherit;
    width: 100%;
    height: 100%;
    background-color: fade(#000, @disabled_opacity * 100);
  }
}
.iwd_main_wrapper {
  .field {
    .input-text._has-datepicker {
      width: 53%;
    }
  }
}
._has-datepicker~.ui-datepicker-trigger {
  margin-left: -3rem;
}
#checkout-payment-method-load {
  .selectize-control.single {
    .selectize-dropdown.single {
      z-index: 9999;
    }
  }
}

//Firefox styles
@-moz-document url-prefix() {
  .iwd_main_wrapper .iwd_opc_field[type="password"]:not(:placeholder-shown) {
    font-size: 14px;
  }
}




@media only screen and (min-width: 300px) and (max-width: 1000px) {

  .alias-pan {
    width: 100px;
    white-space: break-spaces;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    direction: rtl;
  }
  .iwd_main_wrapper .iwd_opc_field #iwd_opc_shipping_method_group + div div.iwd_opc_select_option:after {
    display: none!important;
  }

  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container .iwd_opc_select_option {
    padding-bottom: 20px!important;
  }

  .iwd_main_wrapper .field {
    .iwd_opc_select_container:not(.selected)  div.iwd_opc_select_option {
      &:after {
        width:14px!important;
        height: 14px!important;
      }
    }
  }


  .iwd_main_wrapper .iwd_opc_universal_wrapper {
    button {
      &.iwd_opc_place_order_button {
        margin-top:60px!important;
      }
    }
  }

  .checkout-onepage-success {
    h3 {margin-top: 16.5rem}


    button {
      width:250px;
      margin:0!important;
      margin-bottom: 20px!important;
    }

    .tunnel div {
      &:nth-of-type(2) {
        background:@lf-blue!important;
        color:white!important;
      }
      &:nth-of-type(3) {
        background:white!important;
        color:@lf-blue!important;
        border-right: 1px solid white;
      }
    }

    #iwd_opc_discount {
      display: none;
    }
  }
  .onepage-index-index,
  .checkout-onepage-success {
    .atwork {
      margin-top:0;
    }
    .tunnel {
      margin-top: 0;
    }

    #header {
      height: 0px!important;
      min-height: 0!important;
    }
    #header .container {
      height: 57px!important;
      .commande {
        display: none;
      }
      .step3 {
        background:none!important;
      }
    }
    #header .menu {
      display: none;
    }
    .columns {
      .column.main {
        margin-left: 0;
        padding-bottom: 0!important;
      }
    }
    .block.crosssell {
      margin-top: 40px;
    }
    .tunnel div {
      &:nth-of-type(2) {
        background:white;
        color:@lf-blue;
      }
    }
    #checkout {
      margin-top:0;
      padding-left: 20px;
      padding-right: 20px;
      width:~"calc(100% - 40px)";
    }
    .iwd_main_wrapper .iwd_opc_alternative_wrapper {
      display: flex;
      flex-direction: column;

      .iwd_opc_address_column {
        order:0;
        height: auto;
      }
      .iwd_opc_shipping_column {
        order:3;
      }
      .iwd_opc_payment_column {
        order:4;
        height:auto;
      }
      #iwd_opc_top {
        order:5;
        .iwd_opc_column {
          margin-bottom: 0!important;
        }

      }

      .iwd_opc_select_option {
        &.iwd_opc_cc_option_long {
          &.selected:after {
            top:-18px!important;
          }
        }
      }
      #checkout-payment-method-load {
        .payment-method {
          .payment-method-content {
            iframe {
              height: 760px!important;
              min-height: 760px!important;
              width: 100% !important;
              margin:0!important;
            }
          }
        }
      }

      #iwd_opc_review_items_totals {
        position: relative!important;
        margin:-20px 0 0 -20px;
        width:~"calc(100% + 40px)!important";
        h3 {
          margin-top: 10px!important;
        }
        #iwd_opc_review_totals {
          min-height: unset!important;
        }
      }
    }

  }


}

@media only screen and (min-width: 300px) and (max-width: 474px) {
  .iwd_opc_universal_wrapper {

    button {

      &.iwd_opc_place_order_button {
        background-position:10% 10px!important;
      }
    }
  }
}

.accept-cgv {
  max-width:350px;
  font-size:12px;
}
