<?php
use Magento\Framework\App\Action\Action;

/** @var \Lf\Catalog\Block\Hp\ListProduct $block */

$categories = $block->getCategories();
?>
<script type="text/x-magento-init">
{
    "*": {
        "Lf_Catalog/js/productListHp": {},
        "Lf_Catalog/js/productCartOverlay": {}
            }
}
</script>
<?php foreach ($categories as $category): ?>
    <?php $productCollection = $block->getCategoryProducts($category); ?>

    <?php if ($productCollection
        && $productCollection->count()>0): ?>
        <div class="categorie">

            <h2><?= $category->getName(); ?></h2>

            <div class="container categorie-container">
                <div class="produit cms">
                    <?= $category->getDescription(); ?>
                </div>

                <?php $cpt = 1; ?>
                <?php foreach ($productCollection as $product): ?>
                    <div class="produit produit<?= $cpt++; ?> <?= $block->getFilters($product); ?> <?= !$product->getData('visible_fo') ? 'unavailable' : '' ?>"
                         data-id="<?= $product->getId(); ?>">
                        <div class="visu">
                            <div class="cartOverlay"></div>
                            <?= $block->getImage($product, 'category_page_list', [$cpt => $cpt])->toHtml() ?>
                        </div>
                        <?php if ($product->getData('label') && $product->getData('label')!="no_selection"): ?>
                            <label><?= $block->getImage($product, 'category_page_list_label', [$cpt => $cpt])->toHtml() ?></label>
                        <?php endif; ?>
                        <div class="content">
                            <?php if($product->getData('preference_alimentaire')): ?>
                                <?php $pictos = array_reverse(explode(',', $product->getData('preference_alimentaire')));
                                foreach($pictos as $value):
                                    ?>
                                    <?php
                                    $attr = $product->getResource()->getAttribute('preference_alimentaire');
                                    if ($attr->usesSource()): ?>
                                        <?php
                                        $option_value = $attr->getSource()->getOptionText($value);
                                        $fileName = $block->getValidFileName($option_value);
                                        ?>
                                        <div class="pic tt" data-text="<?php echo $option_value; ?>"> <img src="<?= /* @escapeNotVerified */ $block->getViewFileUrl('images/picto/'.$fileName.'.svg') ?>"></div>
                                    <?php endif;?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <h4><?= $product->getName(); ?></h4>
                            <div class="smalldesc"><?= $product->getShortDescription(); ?></div>
                            <hr>
                            <div class="no-delivery">
                                <img src="<?= /* @escapeNotVerified */
                                $block->getViewFileUrl('images/basket.svg') ?>" alt="Ajouter au panier">
                                <h5>Pour le tarif & la disponibilité,<br>renseignez votre société</h5>
                            </div>
                            <?php if ($product->isSaleable()): ?>
                                <?php $postParams = $block->getAddToCartPostParams($product); ?>
                                <form data-role="tocart-form" data-product-sku="<?= $block->escapeHtml($product->getSku()) ?>" action="<?= /* @NoEscape */ $postParams['action'] ?>" method="post">
                                    <input type="hidden" name="product" value="<?= /* @escapeNotVerified */ $postParams['data']['product'] ?>">
                                    <input type="hidden" name="<?= /* @escapeNotVerified */ Action::PARAM_NAME_URL_ENCODED ?>" value="<?= /* @escapeNotVerified */ $postParams['data'][Action::PARAM_NAME_URL_ENCODED] ?>">
                                    <?= $block->getBlockHtml('formkey') ?>
                                    <div class="addtocart" data-id="<?= $product->getId(); ?>">
                                        <div class="add">AJOUTER</div>
                                        <i class="fa fa-spinner fa-spin produit-spinner" data-role="produit-spinner" style="display: none;"></i>
                                        <div class="quantity" data-id="<?= $product->getId(); ?>"></div>
                                    </div>
                                </form>
                            <?php endif; ?>
                            <hr>
                            <div class="desc"><?= $product->getDescription(); ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; ?>
<?php if (!$block->isRedirectToCartEnabled()) : ?>
    <script type="text/x-magento-init">
            {
                "[data-role=tocart-form]": {
                    "catalogAddToCart": {}
                }
            }
            </script>
    <script>
        require(['jquery'], function($){
            $('div.add').click(function(e){
                $(e.target).closest('form').trigger('submit');
                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        });
    </script>
<?php endif; ?>
