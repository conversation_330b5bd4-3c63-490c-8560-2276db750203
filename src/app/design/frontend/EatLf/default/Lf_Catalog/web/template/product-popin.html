<div data-bind="afterRender: createModal">
    <!-- ko if: upsell().length > 0 -->
    <div class="cross">
        <h3>Complétez votre repas !</h3>
        <div class="sell">
            <!-- ko foreach: upsell-->
            <div class="produit" data-bind="attr: {'data-id': entity_id}">

                <div class="visu"><div class="cartOverlay"></div><img class="prod" data-bind="attr: {src: image_resized}"></div>
                <div class="content">
                    <h4 data-bind="html: name"></h4>
                     <!-- ko if: typeof short_description !== 'undefined' -->
                    <div data-bind="html: short_description" class="smalldesc"></div>
                    <!-- /ko -->
                    <hr>
                    <!-- ko ifnot: $parent.hasTimeslot -->
                    <div class="no-delivery">
                        <img data-bind="attr: { src: require.toUrl('')+'images/basket.svg' }">
                        <h5>Pour le tarif & la disponibilité,<br>renseignez votre société</h5>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $parent.hasTimeslot -->
                    <div class="tocart-form-container" data-bind="afterRender: $parent.duplicateCartForm">
                    </div>
                    <!-- /ko -->
                    <hr>
                </div>
            </div>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->
    <!-- ko with: product -->

    <div class="produit"  data-bind="attr: {'data-id': entity_id}">

         <div class="gzoom" id="gzoom" data-bind="afterRender: $parent.gzoom">
            <div data-u="slides" class="slides">
                <div class="slide0">
                    <img class="prod big" data-bind="attr: $parent.zoomAttributes">
                </div>

                <!-- ko foreach: $parent.images -->
                <div data-bind="css: 'slide' + ($index() + 1)">
                    <img class="vignette" data-bind="attr: {src: image_url}">
                </div>
                <!-- /ko -->

            </div>
             <!-- ko if: $parent.images().length > 0 -->
            <div data-u="navigator" class="jssorb051" data-autocenter="1" data-scale="0.5" data-scale-bottom="0.75">
                <div data-u="prototype" class="i" style="width:16px;height:12px;">
                    <svg viewBox="0 0 16000 16000" style="position:absolute;top:0;left:0;width:100%;height:100%;">
                        <circle class="b" cx="8000" cy="8000" r="5800"></circle>
                    </svg>
                </div>
            </div>
            <!-- /ko -->
        </div>

        <div class="content">
            <div class="pic"></div>
            <!-- ko if: typeof picto_filtre_1_resized !== 'undefined' -->
            <div class="pic tt"  data-bind="attr: {'data-text': picto_filtre_1_resized.label}">
                <img data-bind="attr: { src: picto_filtre_1_resized.image_url}"/>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof picto_filtre_2_resized !== 'undefined' -->
            <div class="pic tt"  data-bind="attr: {'data-text': picto_filtre_2_resized.label}">
                <img data-bind="attr: { src: picto_filtre_2_resized.image_url}"/>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof picto_filtre_3_resized !== 'undefined' -->
            <div class="pic tt"  data-bind="attr: {'data-text': picto_filtre_3_resized.label}">
                <img data-bind="attr: { src: picto_filtre_3_resized.image_url}"/>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof picto_filtre_4_resized !== 'undefined' -->
            <div class="pic tt"  data-bind="attr: {'data-text': picto_filtre_4_resized.label}">
                <img data-bind="attr: { src: picto_filtre_4_resized.image_url}"/>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof picto_filtre_5_resized !== 'undefined' -->
            <div class="pic tt"  data-bind="attr: {'data-text': picto_filtre_5_resized.label}">
                <img data-bind="attr: { src: picto_filtre_5_resized.image_url}"/>
            </div>
            <!-- /ko -->

            <h4 data-bind="html: name"></h4>
            <!-- ko if: typeof short_description !== 'undefined' -->
            <div data-bind="html: short_description" class="smalldesc"></div>
            <!-- /ko -->
            <hr>

            <!-- ko ifnot: $parent.hasTimeslot -->
            <div class="no-delivery">
                <img data-bind="attr: { src: require.toUrl('')+'images/basket.svg' }">
                <h5>Pour le tarif & la disponibilité,<br>renseignez votre société</h5>
            </div>
            <!-- /ko -->

            <!-- ko if: $parent.hasTimeslot -->
            <div class="tocart-form-container" data-bind="afterRender: $parent.duplicateCartForm">
            </div>
            <!-- /ko -->

            <hr>
            <!-- ko if: typeof description !== 'undefined' -->
            <div class="desc" data-bind="html: description"></div>
            <!-- /ko -->
            <!-- ko if: typeof ingredients !== 'undefined' -->
            <div class="details">
                <h5>Ingrédients</h5>
                <span data-bind="html: ingredients"></span>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof allergenes !== 'undefined' -->
            <div class="details">
                <h5>Composition et allergènes</h5>
                <span data-bind="html: allergenes"></span>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof preparation !== 'undefined' -->
            <div class="details">
                <h5>Préparation</h5>
                <span data-bind="html: preparation"></span>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof poids !== 'undefined' -->
            <div class="details">
                <h5>Poids</h5>
                <span data-bind="html: poids"></span>
            </div>
            <!-- /ko -->

            <!-- ko if: typeof energie !== 'undefined' || typeof matieres_grasses !== 'undefined' || typeof glucides !== 'undefined' || typeof fibres !== 'undefined' || typeof proteines !== 'undefined' || typeof sel !== 'undefined' -->
            <div class="details">
                <h5>Valeurs nutritionnelles pour 100g</h5>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof energie !== 'undefined' && energie !== null -->
            <div class="details2">
                <span data-bind="html: energie"></span>Kcal.
                <h6>Énergie</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof matieres_grasses !== 'undefined' && matieres_grasses !== null -->
            <div class="details2">
                <span data-bind="html: matieres_grasses"></span>g.
                <h6>Mat. grasses</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof acides_gras_satures !== 'undefined' && acides_gras_satures !== null -->
            <div class="details2">
                <span data-bind="html: acides_gras_satures"></span>g.
                <h6>Dont A. gras saturés</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof glucides !== 'undefined' && glucides !== null -->
            <div class="details2">
                <span data-bind="html: glucides"></span>g.
                <h6>Glucides</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof sucres !== 'undefined' && sucres !== null -->
            <div class="details2">
                <span data-bind="html: sucres"></span>g.
                <h6>Dont sucres</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof fibres !== 'undefined' && fibres !== null -->
            <div class="details2">
                <span data-bind="html: fibres"></span>g.
                <h6>Fibres</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof proteines !== 'undefined' && proteines !== null  -->
            <div class="details2">
                <span data-bind="html: proteines"></span>g.
                <h6>Protéines</h6>
            </div>
            <!-- /ko -->
            <!-- ko if: typeof sel !== 'undefined' && sel !== null -->
            <div class="details2">
                <span data-bind="html: sel"></span>g.
                <h6>Sel</h6>
            </div>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->
</div>
