<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**  @var $block \LF\Checkout\Block\Cart\Item\Accessories */
$block->getChildHtml('form_before');

$_items = $block->getAccessories();

?>
<?php if ($block->isThereAccessories()): ?>
    <span class="summary title titlecouverts">
        <?php echo $block->getAccessoriesTitle() ?>
        <img class="imgCover" src="<?php echo $block->getImage() ?>" alt=""/>
    </span>
    <div class="cart-couverts" id="cart-couverts">
        <table class="data table couverts-table">

            <tbody>
            <?php foreach ($_items

            as $_item): ?>
            <tr class="totals sub">
                <th class="cart-couverts-label" scope="row"><?php echo $_item['name'] ?></th>
                <td class="mark cart-couverts-price">
                    <?= $_item['productPrice'] ?>
                </td>

                <td class="qty quantitycouverts">
                    <span class="moinscouverts">-</span>
                    <input id="cart-couverts-qty" name="[qty]" value="<?= $block->escapeHtmlAttr($_item['qty']) ?>"
                           type="number"
                           data-product-id="<?= $block->escapeHtmlAttr($_item['product_id']) ?>"
                           data-cart-id="<?= $block->escapeHtmlAttr($_item['id']) ?>"
                           data-add-url="<?= $block->escapeHtmlAttr($_item['add_to_cart']) ?>"
                           title="Qté" class="qtycouverts"
                           data-validate="{required:true,'validate-greater-than-zero':true}">

                    <span class="pluscouverts">+</span>
                </td>

                <th id="cart-couverts-ttc" class="cart-couverts-ttc" scope="row">
                    <span><?= $_item['productTotals'] ?></span>
                    <sup>TTC</sup>
                </th>
                <?php endforeach; ?>
            </tr>
            </tbody>
        </table>
        <span class="primary couvertsfooter"><?php echo $block->getAccessoriesFooter() ?>
    </div>
<?php endif; ?>
