//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  .onepage-index-index {
    .loading-mask .loader p {
      display: none;
    }
  }

  #checkout textarea#comment::placeholder,
  #checkout input#reference::placeholder {
    color:@lf-gold!important;
  }

  .kr-payment-button {
    display: none !important;
  }

  .billing-same-adress {
    margin: 30px 0;
    text-align: left;

    button {
      text-transform: uppercase;
    }
  }

  .shipping-address-button-container {
    text-align: left;
    button {
      padding: 15px 40px;
      font-family: 'DinPro';
      font-size: 12px;
      font-family: 'DinProBlack';
      border:2px solid @lf-blue;
    }
  }

  .street {
    .field {
      margin: 0 !important;
    }
  }

  .billing-form-container {
    select.selectized {
      margin: 0;
      padding: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      height: 1px;
      opacity: 0;
      z-index: -1;
    }
  }

  .edenred-limit {
    text-align: left;
  }

  .iwd_opc_option_with_image {
    &[data-value="edenred"] {
      overflow: visible !important;

      .iwd_opc_option_image {
        display: block !important;
        width: 128px !important;
        height: 45px !important;
        right: 0 !important;
        top: 0 !important;
      }
    }

    &[data-value="systempay_standard"] {
      height: 57px !important;

      .iwd_opc_option_image {
        width: auto !important;
        height: 27px !important;
        display: block !important;
        position: relative !important;
        float: none !important;
        margin: 10px 0 0 0 !important;
      }

      &:after {
        left: 4px !important;
        top: -51px !important;
      }
    }
  }

  .iwd_opc_select_container {
    overflow: visible !important;
  }
}


@media only screen and (min-width: 300px) and (max-width: 1000px) {

  .iwd_opc_option_with_image {

    &[data-value="systempay_standard"] {

      &:after {
        top: -70px !important;
      }
    }
  }

}
