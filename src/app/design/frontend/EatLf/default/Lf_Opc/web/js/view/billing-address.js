define(
    [
        'jquery',
        'ko',
        'underscore',
        'Magento_Ui/js/form/form',
        'Magento_Customer/js/model/customer',
        'Magento_Customer/js/model/address-list',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/action/create-billing-address',
        'Magento_Checkout/js/action/select-billing-address',
        'Magento_Checkout/js/checkout-data',
        'Magento_Checkout/js/model/checkout-data-resolver',
        'Magento_Customer/js/customer-data',
        'Magento_Checkout/js/action/set-billing-address',
        'Magento_Ui/js/model/messageList',
        'mage/translate',
        'uiRegistry',
        'Magento_Checkout/js/model/postcode-validator',
        'Magento_Checkout/js/model/address-converter'
    ],
    function ($,
              ko,
              _,
              Component,
              customer,
              addressList,
              quote,
              createBillingAddress,
              selectBillingAddress,
              checkoutData,
              checkoutDataResolver,
              customerData,
              setBillingAddressAction,
              globalMessageList,
              $t,
              registry,
              postcodeValidator,
              addressConverter) {
        'use strict';

        let inlineAddress = "",
            newAddressOption = {
                /**
                 * Get new address label
                 * @returns {String}
                 */
                getAddressInline: function () {
                    return $t('New Address');
                },
                customerAddressId: null
            },
            countryData = customerData.get('directory-data'),
            addressOptions = addressList().filter(function (address) {
                const isDuplicate = inlineAddress === address.getAddressInline();
                inlineAddress = address.getAddressInline();

                return address.getType() === 'customer-address' && !isDuplicate;
            });

        addressOptions.push(newAddressOption);

        return Component.extend({
            defaults: {
                template: 'Lf_Opc/billing-address'
            },
            canHideErrors: true,
            postcodeElement: null,
            addressOptions: addressOptions,
            customerHasAddresses: addressOptions.length > 1,
            selectedAddress: ko.observable(null),
            quoteIsVirtual: quote.isVirtual(),
            isAddressFormVisible: ko.observable(true),
            isAddressSameAsShipping: ko.observable(false),
            isNewAddress: ko.observable(true),
            saveInAddressBook: ko.observable(true),
            canUseShippingAddress: ko.computed(function () {
                return !quote.isVirtual() && quote.shippingAddress() && quote.shippingAddress().canUseForBilling();
            }),


            optionsRenderCallback: 0,
            validateAddressTimeout: 0,
            validateDelay: 1400,
            decorateSelect: function (uid, showEmptyOption) {
                if (typeof showEmptyOption === 'undefined') {
                    showEmptyOption = false;
                }
                clearTimeout(this.optionsRenderCallback);
                this.optionsRenderCallback = setTimeout(function () {
                    var select = $('#' + uid);
                    if (select.length) {
                        select.decorateSelect(showEmptyOption, true);
                    }
                }, 0);
            },
            getCode: function (parent) {
                return (parent && _.isFunction(parent.getCode)) ? parent.getCode() : 'shared';
            },
            getNameForSelect: function () {
                return this.name.replace(/\./g, '');
            },
            getCountryName: function (countryId) {
                return countryData()[countryId] !== undefined ? countryData()[countryId].name : '';
            },
            addressOptionsText: function (address) {
                return address.getAddressInline();
            },

            /**
             * Init component
             */
            initialize: function () {
                this._super();

                // Si une adresse par défaut a été sélectionnée côté serveur on l'utilise
                if (!isNaN(this.billingAddressId)) {
                    this.isAddressSameAsShipping(false);
                    this.isNewAddress(false);
                    this.selectedAddress(this.billingAddressId);
                    this.populateFormWithExistingAddress(this.billingAddressId);
                } else {
                    this.isAddressSameAsShipping(true);
                    this.isAddressFormVisible(false);
                }

                // Listener sur le changement d'adresse du select
                this.selectedAddress.subscribe(this.onSelectedAddressChange.bind(this));

                // Computed qui active / désactive le formulaire en fonction de l'état de l'adresse choisie
                ko.computed(this.resetFormState.bind(this));

                return this;
            },

            resetFormState: function () {
                const self = this;

                this.selectedAddress(); // subscribe to address change

                $('#lf-billing-address-form select').each(function () {
                    if (this.selectize) {
                        if (self.isNewAddress()) {
                            this.selectize.enable();
                        } else {
                            this.selectize.disable();
                        }
                    }
                });

                this.elems().forEach(function (billingForm) {
                    billingForm.elems().forEach(function (formField) {
                        if (self.isNewAddress()) {
                            if (formField.enable) {
                                formField.enable();
                            } else {
                                // Champ street
                                formField.elems().forEach(function (subField) {
                                    subField.enable();
                                });
                            }

                        } else {
                            if (formField.disable) {
                                if (formField.inputName !== 'custom_attributes[email]') {
                                    formField.disable();
                                } else {
                                    formField.enable();
                                }
                            } else {
                                // Champ street
                                formField.elems().forEach(function (subField) {
                                    subField.disable();
                                });
                            }
                        }
                    });
                });
            },

            onSelectedAddressChange: function (newAddressId) {
                if (newAddressId === '') {
                    this.isNewAddress(true);
                    this.populateFormWithNewAddress();
                    return;
                }

                this.isNewAddress(false);
                this.populateFormWithExistingAddress(newAddressId);
            },

            populateFormWithNewAddress: function () {
                this.elems().forEach(function (billingForm) {
                    billingForm.elems().forEach(function (formField) {
                        if (formField.error) {
                            formField.value(formField.default);
                            formField.error(false);
                        } else {
                            // Champ street
                            formField.elems().forEach(function (subField) {
                                subField.value(formField.default);
                                subField.error(false);
                            });
                        }
                    });
                });
            },

            populateFormWithExistingAddress: function (addressId) {
                const billingAddress = $.extend(true, {}, addressList().find(function (address) {
                    return address.customerAddressId === addressId
                }));

                const convertedAddress = addressConverter.quoteAddressToFormAddressData(billingAddress);

                if (billingAddress.customAttributes && billingAddress.customAttributes.email && billingAddress.customAttributes.email.value) {
                    convertedAddress.custom_attributes.email = billingAddress.customAttributes.email.value;
                }

                this.source.set(this.dataScopePrefix, convertedAddress);
            },

            setBillingInformation: function () {
                if (this.isAddressSameAsShipping()) {
                    checkoutData.setSelectedBillingAddress(null);
                    selectBillingAddress(quote.shippingAddress());

                    return true;
                }

                this.source.set('params.invalid', false);
                this.source.trigger(this.dataScopePrefix + '.data.validate');

                if (this.source.get(this.dataScopePrefix + '.custom_attributes')) {
                    this.source.trigger(this.dataScopePrefix + '.custom_attributes.data.validate');
                }

                if (this.source.get('params.invalid')) {
                    this.focusInvalid();
                    return false;
                }

                if (this.isNewAddress()) {
                    let addressData = this.source.get(this.dataScopePrefix),
                        newBillingAddress;

                    addressData['save_in_address_book'] = 1;

                    newBillingAddress = createBillingAddress(addressData);

                    // The custom attributes property format is modified by createBillingAddress
                    newBillingAddress.customAttributes['email'] = addressData.custom_attributes['email'];

                    selectBillingAddress(newBillingAddress);

                    checkoutData.setSelectedBillingAddress(newBillingAddress.getKey());
                    checkoutData.setNewCustomerBillingAddress(addressData);

                    return true;
                }

                if (this.selectedAddress()) {
                    const addressData = this.source.get(this.dataScopePrefix);
                    const email = addressData['custom_attributes']['email'];

                    const billingAddress = addressList().find(function (address) {
                        return address.customerAddressId === this.selectedAddress();
                    }.bind(this));

                    billingAddress.customAttributes['email'] = email;

                    checkoutData.setSelectedBillingAddress(billingAddress);
                    selectBillingAddress(billingAddress);

                    return true;
                }

                return false;
            },

            useShippingAddress: function () {
                this.isAddressFormVisible(!this.isAddressSameAsShipping());

                if (!this.customerHasAddresses) {
                    this.isNewAddress(!this.isAddressSameAsShipping());
                }

                return true;
            },

            postcodeValidation: function () {
                let self = this,
                    countryId = $('.co-billing-form:visible').first().find('select[name="country_id"]').val(),
                    validationResult,
                    warnMessage;

                if (self.postcodeElement === null || self.postcodeElement.value() === null) {
                    return true;
                }

                self.postcodeElement.warn(null);
                validationResult = postcodeValidator.validate(self.postcodeElement.value(), countryId);

                if (!validationResult) {
                    warnMessage = $t('Provided Zip/Postal Code seems to be invalid.');

                    if (postcodeValidator.validatedPostCodeExample.length) {
                        warnMessage += $t(' Example: ') + postcodeValidator.validatedPostCodeExample.join('; ') + '. ';
                    }
                    warnMessage += $t('If you believe it is the right one you can ignore this notice.');
                    self.postcodeElement.warn(warnMessage);
                }

                return validationResult;
            },

            validateFields: function () {
                return true;
            }
        });
    }
);
