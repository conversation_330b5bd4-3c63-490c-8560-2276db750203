<div class="field choice billing-same-adress">
    <input type="checkbox" name="billing-address-same-as-shipping"
           data-bind="checked: isAddressSameAsShipping, click: useShippingAddress, attr: {id: 'billing-address-same-as-shipping-' + getCode($parent)}"/>
    <label data-bind="attr: {for: 'billing-address-same-as-shipping-' + getCode($parent)}"><span
            data-bind="i18n: 'Use same address for billing'"></span></label>
</div>
<div class="fieldset billing-form-container" data-bind="visible: (!isAddressSameAsShipping() || quoteIsVirtual)">
    <div class="iwd_opc_universal_wrapper iwd_opc_column_name" data-bind="i18n: 'Billing Address'"></div>
    <!-- ko template: 'IWD_Opc/billing-address/list' --><!-- /ko -->
    <!-- ko template: 'Lf_Opc/billing-address/form' --><!-- /ko -->
</div>

