<div class="iwd_opc_column iwd_opc_payment_column">
    <div class="iwd_opc_alternative_column">
        <div class="iwd_opc_universal_wrapper iwd_opc_column_name" data-bind="i18n: 'Commentaires livraison et facturation'"></div>

        <div class="field">
            <div class="control">
            <textarea class="input-text" type="text"  data-bind="mageInit: {'Lf_Checkout/js/limit-textarea':{}},value:commentValue,
        attr: {
            value: commentValue,
            name: 'comment',
            placeholder: commentPlaceholder,
            id: 'comment',
        }" ></textarea>
            </div>
        </div>

        <div class="field">
            <div class="control">
                <input class="input-text" type="text"  data-bind="value:referenceValue,
        attr: {
            value: referenceValue,
            name: 'reference',
            placeholder: 'Reference facture [sera inscrite sur votre facture]',
            id: 'reference',
        }" />
            </div>
        </div>

        <div class="iwd_opc_universal_wrapper iwd_opc_column_name" data-bind="i18n: 'Payment Method'"></div>
        <div class="iwd_opc_column_content" tabindex="-1" data-bind="iwdBlockLoader: isLoading">
            <!-- ko if: (quoteIsVirtual) -->
            <!-- ko foreach: getRegion('customer-email') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
            <!--/ko-->
            <form id="co-payment-form" class="form payments" novalidate="novalidate">
                <input data-bind='attr: {value: getFormKey()}' type="hidden" name="form_key"/>
                <div class="fieldset">
                    <!-- ko foreach: getRegion('beforeMethods') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!-- /ko -->

                    <!-- ko if: isPaymentMethodsAvailable()  -->
                    <div id="checkout-payment-method-load" class="opc-payment">
                        <!-- ko foreach: getRegion('payment-methods-list') -->
                        <!-- ko template: getTemplate() --><!-- /ko -->
                        <!-- /ko -->
                    </div>
                    <!-- /ko -->

                    <!-- ko ifnot: isPaymentMethodsAvailable()  -->
                    <div class="field">
                        <div class="control">
                            <input readonly="readonly"
                                   type="text"
                                   tabindex="-1"
                                   class="input-text"
                                   data-bind="attr: {value: $t('Options Unavailable'), title: $t('Options Unavailable')}"/>
                            <div class="iwd_opc_field_tooltip"
                                 data-icon="&#xf059"
                                 data-bind="css: quoteIsVirtual?'iwd_opc_payment_method_tooltip_virtual':'iwd_opc_payment_method_tooltip'">
                                <div class="iwd_opc_field_tooltip_content">
                                    <!-- ko if: !quoteIsVirtual -->
                                    <!-- ko i18n: 'Make sure your shipping address is completed, and a shipping method has been selected.' -->
                                    <!-- /ko -->
                                    <!-- /ko -->
                                    <!-- ko if: quoteIsVirtual -->
                                    <!-- ko i18n: 'No Payment Methods.' --><!-- /ko -->
                                    <!-- /ko -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                    <div class="iwd_opc_section_delimiter"></div>
                    <!-- ko foreach: getRegion('afterMethods') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!-- /ko -->
                    <!-- ko if: getRegion('afterMethods') -->
                    <div class="iwd_opc_section_delimiter"></div>
                    <!-- /ko -->
                </div>
            </form>
            <div class="iwd_opc_universal_wrapper">
                <button type="button"
                        data-bind="attr :{'title': $t('Place Order')}, click: placeOrder, visible: methodIsSelected, enable: isPlaceOrderActionAllowed"
                        class="btn btn-8h iwd_opc_place_order_button">
                    <!-- ko i18n: 'Place Order' --><!-- /ko -->
                </button>
            </div>
            <div class="accept-cgv" data-bind="visible: methodIsSelected">
                <br>
                En cliquant sur "passer la commande", vous validez définitivement votre commande et acceptez sans réserve les conditions générales de vente consultables en cliquant <a href="/conditions-generales-de-vente" target="_blank"><u>ici</u></a>.
            </div>
            <!-- ko if: isShowSubscribe -->
            <div class="fieldset">
                <div class="field choice">
                    <input type="checkbox" name="subscribe"
                           data-bind="
                       checked: isSubscribe,
                       attr: {
                            id: 'subscribe'
                       }"/>
                    <label data-bind="attr: {for: 'subscribe'}">
                        <span data-bind="i18n: 'Sign Up For Newsletter'"></span>
                    </label>
                </div>
            </div>
            <!-- /ko -->
            <!-- ko foreach: getRegion('before-place-order') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
        </div>
    </div>
</div>
