<?php
/**
 * @var $block \Magento\Checkout\Block\Cart\Coupon
 */
?>

<?= $block->getChildHtml('coupon.form.before') ?>

<div class="block discount" id="block-discount">
    <div class="content" data-role="content" aria-labelledby="block-discount-heading">
        <form id="discount-coupon-form"
              action="<?= /* @escapeNotVerified */ $block->getUrl('checkout/cart/couponPost') ?>"
              method="post"
              data-mage-init='{"discountCodeEat":{"couponCodeSelector": "#coupon_code",
                                               "removeCouponSelector": "#remove-coupon",
                                               "applyButton": "button.action.apply",
                                               "cancelButton": "button.action.cancel"}}'>

            <div class="fieldset coupon<?= !empty($block->getCouponCode()) ? ' applied' : '' ?>">
                <?= $block->getBlockHtml('formkey') ?>
                <input type="hidden" name="remove" id="remove-coupon" value="0" />
                <div class="field">
                    <div class="control">
                        <input type="text" class="input-text" id="coupon_code" name="coupon_code" value="<?= $block->escapeHtml($block->getCouponCode()) ?>" placeholder="<?php if (!empty($block->getCouponCode())): ?>Annuler v<?php else: ?>V<?php endif; ?>otre code" <?php if (!empty($block->getCouponCode())): ?> disabled="disabled" <?php endif; ?> />
                    </div>
                </div>

                    <?php if (empty($block->getCouponCode())): ?>

                        <button class="btn btn-8g action apply primary" type="button" value="<?= /* @escapeNotVerified */ __('Apply Discount') ?>">
                            <span>Valider</span>
                        </button>

                    <?php else: ?>

                            <button  type="button" class="btn btn-8g action cancel primary" value="<?= /* @escapeNotVerified */ __('Cancel Coupon') ?>"><span class="cross"></span></button>

                    <?php endif; ?>

                <?php echo $block->getChildHtml("coupontips"); ?>

            </div>
        </form>
    </div>
</div>
