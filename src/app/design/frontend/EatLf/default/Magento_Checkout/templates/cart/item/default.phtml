<?php
// @codingStandardsIgnoreFile

/** @var $block \Magento\Checkout\Block\Cart\Item\Renderer */

$_item = $block->getItem();
$product = $_item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();
/** @var \Magento\Msrp\Helper\Data $helper */
$helper = $this->helper('Magento\Msrp\Helper\Data');
$itemHelper = $this->helper(\Lf\Checkout\Helper\CartItem::class);
$canApplyMsrp = $helper->isShowBeforeOrderConfirm($product) && $helper->isMinimalPriceLessMsrp($product);
?>

<tbody class="cart item">
<tr class="item-info">
    <td data-th="<?= $block->escapeHtml(__('Item')) ?>" class="col item">

            <span class="product-item-photo">
            <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')->toHtml() ?>
             </span>
        <div class="product-item-details">
            <strong class="product-item-name">
                <?= $block->escapeHtml($block->getProductName()) ?>
            </strong>
            <?php if ($_options = $block->getOptionList()):?>
                <dl class="item-options">
                    <?php foreach ($_options as $_option) : ?>
                        <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                        <dt><?= $block->escapeHtml($_option['label']) ?></dt>
                        <dd>
                            <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                <?= /* @escapeNotVerified */ $_formatedOptionValue['full_view'] ?>
                            <?php else: ?>
                                <?= /* @escapeNotVerified */ $_formatedOptionValue['value'] ?>
                            <?php endif; ?>
                        </dd>
                    <?php endforeach; ?>
                </dl>
            <?php endif;?>
            <?php if ($messages = $block->getMessages()): ?>
                <?php foreach ($messages as $message): ?>
                    <div class="cart item message <?= /* @escapeNotVerified */ $message['type'] ?>"><div><?= $block->escapeHtml($message['text']) ?></div></div>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addInfoBlock): ?>
                <?= $addInfoBlock->setItem($_item)->toHtml() ?>
            <?php endif;?>

            <div class="smalldesc"> <?= $block->getProductShortDescription() ?> </div>

        </div>
    </td>

    <?php if ($canApplyMsrp): ?>
        <td class="col msrp" data-th="<?= $block->escapeHtml(__('Price')) ?>">
                <span class="pricing msrp">
                    <span class="msrp notice"><?= /* @escapeNotVerified */ __('See price before order confirmation.') ?></span>
                    <?php $helpLinkId = 'cart-msrp-help-' . $_item->getId(); ?>
                    <a href="#" class="action help map" id="<?= /* @escapeNotVerified */ ($helpLinkId) ?>" data-mage-init='{"addToCart":{"helpLinkId": "#<?= /* @escapeNotVerified */ $helpLinkId ?>","productName": "<?= /* @escapeNotVerified */ $product->getName() ?>","showAddToCart": false}}'>
                        <span><?= /* @escapeNotVerified */ __("What's this?") ?></span>
                    </a>
                </span>
        </td>
    <?php else: ?>
        <td class="col price <?php if($itemHelper->isBarre($_item)): ?>barre<?php endif; ?>" data-th="<?= $block->escapeHtml(__('Price')) ?>">
            <?php if($itemHelper->isBarre($_item)): ?>
                <div class="barre">
                    <span class="price-including-tax" data-label="TTC">
                        <span class="cart-price">
                            <span
                                class="price"><?= $itemHelper->getFormatedPrice($_item->getFranchiseProduct()->getPrice()) ?></span>
                        </span>
                    </span>
                </div>
            <?php endif; ?>
            <div>
                    <span class="price-including-tax" data-label="TTC">
                        <span class="cart-price">
                            <span class="price"><?= $itemHelper->getFormatedPrice($_item->getPriceInclTax()) ?></span>
                        </span>
                    </span>
            </div>
        </td>
    <?php endif; ?>
    <td class="col qty quantity" data-th="<?= $block->escapeHtml(__('Qty')) ?>">

        <div class="moins">-</div>
        <input id="cart-<?= /* @escapeNotVerified */ $_item->getId() ?>-qty"
               name="cart[<?= /* @escapeNotVerified */ $_item->getId() ?>][qty]"
               data-cart-item-id="<?= $block->escapeHtml($_item->getSku()) ?>"
               value="<?= /* @escapeNotVerified */ $block->getQty() ?>"
               type="number"
               size="4"
               onChange="jQuery('.btn.action.update').addClass('visible');"
               title="<?= $block->escapeHtml(__('Qty')) ?>"
               class="input-text qty"
               data-validate="{required:true,'validate-greater-than-zero':true}"
               data-role="cart-item-qty"/>
        <div class="plus">+</div>

    </td>

    <td class="col subtotal" data-th="<?= $block->escapeHtml(__('Subtotal')) ?>">
        <?php if ($canApplyMsrp): ?>
            <span class="cart msrp subtotal">--</span>
        <?php else: ?>
            <?= $block->getRowTotalHtml($_item) ?>
        <?php endif; ?>
    </td>
    <td class="col del">
        <div class="actions-toolbar">
            <?= /* @escapeNotVerified */ $block->getActions($_item) ?>
        </div>
    </td>
</tr>
</tbody>
