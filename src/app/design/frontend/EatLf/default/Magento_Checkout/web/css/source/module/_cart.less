// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  //
  //  Shopping cart
  //  ---------------------------------------------

  //  Summary block
  #co-payment-form {
    min-height:200px;
  }
  .order_minimum_amount {
    font-weight: bold;
    color :#9c2727;
    margin-bottom: 10px;
    font-size: 14px;
    text-transform: uppercase;
    &:before {
      content:' \26A0';
      margin-right: 5px;
    }
  }

  .guestCalculator {
    margin-top:20px;
    order:3;
    width: 100%;
    h3 {
      font-size: 16px;
      margin-bottom: 20px;
      margin-top:60px;
      white-space:nowrap;
      b {
        text-transform:initial;
      }
    }
    .guestCalculatorIn  {
      background: #f6f6f6;
      padding: 20px;
      font-weight: bold;
      font-size: 18px;
      color:@lf-blue;
      font-family: 'DinProBold';

      select {
        background:white;
        width: 100px;
        margin: 20px;
        border-radius: 3px;
      }
      div {
        text-align: center;
      }
      .half {
        width:16%;
        display: inline-block;
        vertical-align: top;
      }
      .nbPieces {
        color: @lf-blue;
        margin:5px;
        font-size: 25px;
        display: inline-block;
        margin: 20px;
        text-align: center;
      }
      .total {
        border-right: 1px solid #ccc;
        padding-left: 30px;
        &::after {
          content: "";
          background-repeat: no-repeat;
          background-size: 50px;
          opacity: 0.5;
          top: 110px;
          left: 25px;
          bottom: 0;
          right: 0;
          position: absolute;
          z-index: -1;
        }
        & + .half {
          width:40%;
          line-height:6px;
          text-align:left;
          padding-left:80px;
          b {
            text-transform:initial;
          }
        }
      }
      .nbPiecesConvives {
        background: @lf-blue;
        font-size: 19px;
        border: 1px solid  @lf-blue;
        color:white;
        width: 26px;
        display: inline-block;
        border-radius: 30px;
        padding: 5px;
        margin-top: 14px;
        text-align: center;
        line-height:26px;
      }
      small {
        text-transform: uppercase;
        margin-left: 10px;
        display: inline-block;
        vertical-align: text-top;
        width:320px;
        text-align:left;
        margin-top:9px;
      }
    }
  }

  .cart-summary .block.active > .title:after,
  .paypal-review-discount .block.active > .title:after {
    content: '\e621';
  }
  .checkout-cart-index  {
    font-family: 'DinPro';
    .columns {
      max-width: 1400px;
      margin:auto;
      .column.main {
        margin-left: 25px;
        display: flex;
        flex-direction: column;
      }
    }
    .cart.main.actions {
      display:none;
    }
    .fidelite {
      margin-top: 25px;
    }
    .block.shipping {
      display:none;
    }
  }
  .discount .title strong {
    font-size: 1.4rem!important;
  }
  .discount button {
    color:@lf-blue!important;
  }
  .cart-summary {
    &:extend(.abs-add-box-sizing all);
    .lib-css(background, @sidebar__background-color);
    margin-bottom: @indent__m;
    padding: 1px 30px @indent__m;
    margin-top: 180px;

    > .title {
      display: block;
      color:@lf-blue;
      text-transform: uppercase;
      font-family:'DinProBlack';
      .lib-heading(h3);
      line-height: 1.4;

    }
    .totals {
      color:#444;
      &.sub .amount sup {
        top:-0.5em;
      }
      .amount, .mark {
        color:@lf-blue;
        sup {
          top:-0.9em;
          font-family: 'DinProBold';
        }
      }
      .mark {
        font-family: 'DinProBold';
        text-transform: uppercase;
        letter-spacing: 2px;
        color: @lf-blue;
        padding-top: 12px;
        font-size: 12px;
      }
    }
    .grand.totals .mark {
      padding-top: 30px;
    }
    .grand.totals .price {
      font-size: 24px;
    }
    .block {
      &:extend(.abs-discount-block all);
      margin-bottom: 0;

      .item-options {
        margin-left: 0;
      }

      .fieldset {
        margin: 15px 0 25px 0;

        .field {
          margin: 0 0 @indent__s;

          &.note {
            font-size: @font-size__s;
          }
        }

        .methods {
          .field {
            > .label {
              display: inline;
            }
          }
        }
      }

      .fieldset.estimate {
        > .legend,
        > .legend + br {
          &:extend(.abs-no-display all);
        }
      }
    }
    .actions-toolbar {
      > .primary {
        button {
          &:extend(.abs-revert-secondary-color all);
        }
      }
    }
    &:extend(.abs-adjustment-incl-excl-tax all);
  }

  //  Totals block
  .cart-totals {
    border-top: 1px solid @border-color__base;
    padding-top: @indent__s;
    &:extend(.abs-sidebar-totals all);

    .table-wrapper {
      margin-bottom: 0;
      overflow: inherit;
    }
    .discount .content {
      display: block:important;
    }
    .discount.coupon {
      display: none;
    }
  }

  //  Products table

  .cart.table-wrapper {
    min-height:450px;
    .items {
      thead + .item {
        border-top: @border-width__base solid @lf-blue;
      }

      > .item {
        border-bottom: @border-width__base solid @lf-blue;
        position: relative;
      }
    }

    .col {
      padding-top: @indent__base;

      &.qty {
        .input-text {
          margin-top: -5px;
          &:extend(.abs-input-qty all);
        }

        .label {
          &:extend(.abs-visually-hidden all);
        }
      }
      .price-excluding-tax:after {
        content:'TTC';
        vertical-align: middle;
        display: inline-block;
        margin-top: -7px;
        font-size: 12px;
        font-family: 'DinProBold'
      }

      &.price:not(.barre) {
        .barre {
          display: none;
        }
      }
      &.price.barre {
        padding-left: 15px;
        padding-top:38px!important;
        .barre {
          margin-bottom: 10px;
          text-align: left;
          margin-left: 15px;
          color:@lf-blue;
          &:before {
            background:url(../images/barre.svg);
            background-repeat: no-repeat;
            background-size: 120px;
            background-position: 0px -3px;
            content:"";
            color:@lf-blue;
            display: block;
            position:absolute;
            margin-top: 5px;
            width:70px;
            height:25px;
          }


          .price {
            font-size: 15px;
          }
          .price-excluding-tax:after {
            margin-top: -4px;
            font-size: 10px;
          }
          & + div {
            text-align: left;
            margin-left: 15px;
            .price {
              color:#cc0000;
            }
            .price-excluding-tax:after {
              color:#cc0000;
            }
          }
        }
      }

    }

    .item {
      &-actions td {
        padding-bottom: @indent__s;
        text-align: center;
        white-space: normal;
      }

      .col {
        &.item {
          display: block;
          min-height: 75px;
          padding: @indent__m 0 @indent__s 75px;
          position: relative;
        }
      }
    }

    .actions-toolbar {
      &:extend(.abs-add-clearfix all);

      > .action {
        &:extend(button all);
        .lib-link-as-button();
        margin-bottom: @indent__s;
        margin-right: @indent__s;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .action {
      &.help.map {
        &:extend(.abs-action-button-as-link all);
        font-weight: @font-weight__regular;
      }
    }

    .product {
      &-item-photo {
        display: block;
        left: 0;
        padding: 0;
        position: absolute;
        top: 15px;
        width: 150px;
      }

      &-item-details {
        white-space: normal;
      }

      &-item-name {
        display: inline-block;
        font-weight: @font-weight__regular;
        font-size: 20px;
        font-family: 'DinProBlack';
        text-transform: uppercase;
        color:@lf-blue;
      }
    }

    .gift-registry-name-label {
      &:after {
        content: ':';
      }
    }

    //  Product options
    .item-options {
      font-size: @font-size__s;
      margin-bottom: @indent__s;
      &:extend(.abs-product-options-list all);
      &:extend(.abs-add-clearfix all);
    }

    .product-item-name + .item-options {
      margin-top: @indent__s;
    }

    .product-image-wrapper {
      &:extend(.abs-reset-image-wrapper all);
    }

    .action.configure {
      display: inline-block;
      margin: 0 0 @indent__base;
    }
  }

  .cart-container {
    .form-cart {
      &:extend(.abs-shopping-cart-items all);
    }

    .checkout-methods-items {
      &:extend(.abs-reset-list all);
      margin-top: @indent__base;
      text-align: center;


      .action.primary {
        &:extend(.abs-button-l all);
        width: 100%;
        border: 2px solid @lf-blue;

        &[data-role='proceed-to-checkout'] {
          background-image: url(../images/basket.svg);
          background-repeat: no-repeat;
          padding-left: 40px;
          padding-right: 10px;
          background-position:80px 10px;
        }
      }

      .item {
        & + .item {
          margin-top: @indent__base;
        }
      }
    }
  }

  //  Products pager
  .cart-products-toolbar {
    .toolbar-amount {
      margin: @indent__m 0 15px;
      padding: 0;
      text-align: center;
    }

    .pages {
      margin: 0 0 @indent__m;
      text-align: center;

      .items {
        > .item {
          border-bottom: 0;
        }
      }
    }
  }

  .cart-products-toolbar-top {
    border-bottom: @border-width__base solid @border-color__base;
  }

  //  Cross sell
  //  ---------------------------------------------

  .block.crosssell {
    margin-top:0px;
    margin-bottom: 0px;
    order:2;
    h2 {
      margin-bottom: 0px;
    }
  }

  .cart-tax-info,
  .cart .cart-tax-info {
    + .cart-tax-total {
      display: block;
    }
  }

  .cart.table-wrapper,
  .order-items.table-wrapper {
    .col.price,
    .col.qty,
    .col.subtotal,
    .col.msrp {
      text-align: right;
    }
  }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
  .cart {
    &.table-wrapper {
      overflow: inherit;

      thead {
        .col {
          &:not(.item) {
            display: none;
          }
        }
      }

      .col {
        &.qty,
        &.price,
        &.subtotal,
        &.msrp {
          box-sizing: border-box;
          display: block;
          float: left;
          text-align: center;
          white-space: nowrap;
          width: 33%;

          &:before {
            content: attr(data-th) ':';
            display: block;
            font-weight: @font-weight__bold;
            padding-bottom: @indent__s;
          }
        }

        &.msrp {
          white-space: normal;
        }
      }

      .item {
        .col.item {
          padding-bottom: 0;
        }
      }
    }
  }

  .cart-container {
    .form-cart {
      &:extend(.abs-shopping-cart-items-mobile all);
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .cart-container {
    &:extend(.abs-add-clearfix-desktop all);
    .form-cart {
      &:extend(.abs-shopping-cart-items-desktop all);
    }

    .widget {
      float: left;
    }
  }

  .cart-summary {
    float: right;
    position: relative;
    width: 27%;

    .actions-toolbar {
      margin:0!important;
      .column.main & {
        &:extend(.abs-reset-left-margin-desktop all);
        > .secondary {
          float: none;
        }
      }
    }

    .block {
      .fieldset {
        .field {
          .lib-form-field-type-revert(@_type: block);
          margin: 0 0 @indent__s;
        }
      }
    }
    .discount .content {
      display: block!important;
      .btn {
        color: @lf-blue!important;
        background: none;
        float: right;
        margin-top: -52px;
        height: 42px;
        border:0;
        margin-right: 1px;
        font-size: 18px;
        span.cross:after {
          content:'\e616';
          font-family:'icons-blank-theme';
          display: inline-block;
          font-size:40px;
          height: 30px;
          line-height: 30px;
          color: @lf-blue;
          float: right;
          margin-right:-14px;
          font-weight:normal;
        }
      }
    }
  }

  .cart {
    &.table-wrapper {
      .items { // Google Chrome version 44.0.2403.107 m fix
        min-width: 100%;
        width: auto;
      }

      .item {
        .col {
          &.item {
            padding: 27px 8px @indent__s;
          }
        }

        &-actions {
          td {
            text-align: right;
          }
        }
      }

      .product {
        &-item-photo {
          display: table-cell;
          max-width: 100%;
          padding-right: @indent__base;
          position: static;
          vertical-align: top;
          width: 1%;
        }

        &-item-details {
          display: table-cell;
          vertical-align: top;
          white-space: normal;
          width: 99%;
        }
      }

      .item-actions {
        .actions-toolbar {
          text-align: left;
          &:extend(.abs-reset-left-margin-desktop all);
        }
      }
    }

    .action {
      margin-bottom: @indent__s;
    }
  }

  //  Products pager
  .cart-products-toolbar {
    .toolbar-amount {
      line-height: 30px;
      margin: 0;
    }

    .pages {
      float: right;
      margin: 0 0 1px;

      .item {
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .cart.table-wrapper {
    .cart-products-toolbar {
      + .cart {
        thead {
          tr {
            th.col {
              padding-bottom: 7px;
              padding-top: 8px;
            }
          }
        }
      }
    }

    .cart {
      + .cart-products-toolbar {
        margin-top: @indent__m;
      }
    }
  }
}




@bg-checkbox-svg: "<svg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14'><path fill='@{form-element-input__color}' d='M13.055 4.422q0 0.312-0.219 0.531l-6.719 6.719q-0.219 0.219-0.531 0.219t-0.531-0.219l-3.891-3.891q-0.219-0.219-0.219-0.531t0.219-0.531l1.062-1.062q0.219-0.219 0.531-0.219t0.531 0.219l2.297 2.305 5.125-5.133q0.219-0.219 0.531-0.219t0.531 0.219l1.062 1.062q0.219 0.219 0.219 0.531z'></path></svg>";
@bg-checkbox-svg-escaped: escape(@bg-checkbox-svg);
@bg-checkbox: "data:image/svg+xml,@{bg-checkbox-svg-escaped}";

& when (@media-common = true) {

  .col.qty {
    min-width: 150px;

    .select-qty {
      min-width: 50px;
      max-width: 50px;
      margin-top: -5px;
    }

    .small-save-button {
      padding: 7px 4px;

      .lib-icon-font (
        @icon-checkmark,
        @_icon-font-position: after,
        @_icon-font-color: @primary__color,
        @_icon-font-size: 23px,
        @_icon-font-text-hide: true
      );
    }
  }

  .cart-container .form-cart #empty_cart_button,
  .control.qty .small-save-button {
    display: none;
    vertical-align: top;
    margin-top: -5px;
  }

  .opc-progress-bar {
    text-align: center;
  }

  .checkout-index-index {

    .header.content {

      &:after {
        width: 100%;
        border-bottom: 1px @border-color__base solid;
      }
    }
  }

  // Fix for https://github.com/magento/magento2/issues/8178
  .minicart-wrapper .ui-dialog {
    width: 0;
  }

  .minicart-wrapper {
    z-index: 101;
  }



  .cart.table-wrapper .cart.items {

    .actions-toolbar > .action {
      .lib-button-reset();

      &.action-edit {
        .lib-link(
          @_link-color: @link__color,
          @_link-text-decoration: @link__text-decoration,
          @_link-color-visited: @link__visited__color,
          @_link-text-decoration-visited: @link__visited__text-decoration,
          @_link-color-hover: @link__hover__color,
          @_link-text-decoration-hover: @link__hover__text-decoration,
          @_link-color-active: @link__active__color,
          @_link-text-decoration-active: @link__active__text-decoration,
        );
      }

      &.action-delete {
        .lib-icon-font(
          @_icon-font-content: '\f014',
          @_icon-font: 'FontAwesome',
          @_icon-font-size: 18px,
          @_icon-font-line-height: 32px,
          @_icon-font-color: inherit,
          @_icon-font-color-hover: inherit,
          @_icon-font-color-active: inherit,
          @_icon-font-margin: 0,
          @_icon-font-vertical-align: middle,
          @_icon-font-position: before,
          @_icon-font-text-hide: true,
          @_icon-font-display: inline-block
        );
        display: inline-block;
        position: initial;
        top: 15px;
        right: 0;
      }
    }

    .col.qty {
      min-width: 110px;
      padding-top:33px;  //24
      color:@lf-blue;
    }
  }

  .gift-options-cart-item {
    margin-top: 10px;
  }

  #gift-options-cart {
    // margin-bottom: 4rem;
  }

  .checkout-cart-index {

    .gift-item-block .content {
      padding: 2rem 0 0;
      border-bottom: 0;
    }

    .gift-item-block {
      padding-top: 20px;
    }

    .gift-item-block .title,
    .action-gift {
      .lib-button-reset();
      padding-left: 32px;
      line-height: 24px;
      position: relative;

      &:before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 24px;
        height: 24px;
        background: @form-element-input__background;
        border: 1px @form-element-input__border-color solid;
        border-radius: 2px;
      }

      &:after {
        content: '';
        display: none;
        position: absolute;
        left: 0;
        top: 0;
        width: 24px;
        height: 24px;
        background: url(@bg-checkbox) no-repeat;
        background-size: 20px 20px;
        background-position: center center;

      }

      &._active:after {
        display: block;
      }
    }

    .gift-item-block._active .title:after {
      display: block;
    }

    .gift-options .actions-toolbar .action-update {
      font-size: 1.4rem;
    }
  }

  .billing-address-same-as-shipping-block label,
  .checkout-agreement label {
    padding-left: 32px;
    line-height: 24px;
    position: relative;

    input {
      display: none;
    }

    &:before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      width: 24px;
      height: 24px;
      background: @form-element-input__background;
      border: 1px @form-element-input__border-color solid;
      border-radius: 2px;
    }

    &:after {
      content: '';
      display: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 24px;
      height: 24px;
      background: url(@bg-checkbox) no-repeat;
      background-size: 20px 20px;
      background-position: center center;

    }

    &.active:after {
      display: block;
    }
  }

  .checkout-payment-method .checkout-billing-address .billing-address-details {
    padding-left: 32px;
  }

  .cart-container .form-cart {

    .action.continue {
      .lib-button-as-link();


      &:before {
        vertical-align: middle;
      }
    }
  }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {

  .cart.table-wrapper .cart.items {

    .col.subtotal {
      display: none;
    }
    .col.qty {
      width: 50%;
      text-align: left;
      padding-left: 0;

      &:before {
        display: none;
      }
    }

    .col.price {
      width: 50%;
      text-align: right;
      padding-right: 0;

      &:before {
        display: none;
      }
    }

    .col.item {
      padding-right: 32px;
    }
  }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

  .checkout-index-index .opc-wrapper {
    width: 60%;
  }
  .subtotal {
    color:@lf-blue;
    .price  {
      color:@lf-blue;
      font-family: 'DinProBold';
    }
  }
  .tunnel {
    width: 136%;
    position:absolute;
    margin: auto;
    margin-bottom: 30px;
    height: 40px;
    text-align: center;
    margin-top: -100px;

    div {
      display: inline-block;
      text-align: center;
      width: 220px;
      font-size: 16px;
      line-height: 30px;
      color:@lf-blue;
      font-family: 'DinPro';
      font-weight: Bold;
      letter-spacing: 2px;
      text-transform: uppercase;
      &:last-child:after {
        display:none;
      }
      &:after {
        content:'';
        display: block;
        height: 1px;
        width:200px;
        margin-left:122px;
        margin-top: -48px;
        background: @lf-blue;
      }
      label {
        display: inline-block;
        text-align: center;
        margin: auto;
        width: 100%;
        cursor: default;
      }
    }
    .btn-radio svg  {
      cursor:pointer;
    }
    .btn-radio svg circle {
      stroke:@lf-blue;
    }
    .btn-radio svg path.outer {
      stroke-width:0.5px;
      stroke:@lf-blue;
      fill:white;
    }
    .btn-radio input.checked + svg path.inner,
    .btn-radio input:checked + svg path.inner {
      stroke-width:4.5px;
      outline-offset: -1px;
      fill:@lf-blue;
    }
    .btn-radio input.checked + svg path.outer,
    .btn-radio input:checked + svg path.outer {
      fill:transparent;
    }
  }
  .cart.table {
    margin-top:130px;
  }
  .totals .price  {
    color:@lf-blue;
    font-size:18px;
  }
  .price {
    color:@lf-blue;
    font-size:18px;
    font-family: 'DinProBold';
    font-weight: bold;
    strong {
      font-family: 'DinProBold';
      font-size:22px;
    }
  }
  .primary:not(.btn) {
    width:100%!important;
    font-size: 12px!important;
    font-family: 'DinProBold';
    text-transform: uppercase;
    letter-spacing: 3px;
    color: @lf-blue;
    &.apply {
      color:@lf-blue;
    }
  }
  .checkout-index-index .opc-sidebar {
    width: 40%;
  }

  .checkout-index-index {

    .columns {
      margin-top: 40px;
    }
  }
  .control  input {
    padding-left:10px!important;
    height:42px;
    font-family: Eczar, arial;
    border:0;
    border-bottom: 1px solid @lf-blue;
  }
  #coupon_code {
    border:1px solid @lf-blue!important;
    font-size: 18px;
  }
  .cart-container {
    position: relative;
    padding-top:20px;
    order:1;
    .cart-summary {
      top: 0;
      right: 0;
      min-height: ~"calc(100% - 200px)";

      .message {
        font-size: 12px !important;
      }
    }
    .form-cart .action.update {
      margin-left: 30px!important;
      margin-top: -100px!important;

      @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        margin-left: 1052px!important;
      }

    }
  }

  .action.continue {
    color:@lf-blue!important;
    text-decoration:underline!important;
    font-family: 'DinProBold'!important;
    margin-left: -35px !important;
    margin-top: -90px !important;
    font-size: 16px !important;
    text-transform: unset!important;
    z-index: 3;
    &:hover:before {

      left:0%;
    }
  }

  .action.update {
    padding-right: 30px;
    color:@lf-blue;
    border:1px solid @lf-blue;
    pointer-events:none;
    opacity:0;
    color: @lf-blue!important;
    padding-left: 20px!important;
    height: 55px;
    position:absolute;
    width:100%;
    max-width:318px;
    &:after {
      display:none;
    }
  }
  .action.update.visible {
    opacity:1;
    pointer-events:all;
    z-index:2;
    background:@lf-gold!important;
    border:1px solid @lf-gold!important;
    color:white!important;

    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
      font-size:12px;
    }
  }


  .action[data-role='proceed-to-checkout'] {
    background:@lf-blue;
    color:white;
    text-transform: uppercase;
  }

  .cart.table-wrapper .cart.items {
    th.col {
      color: @lf-blue;
      font-family: 'DinProBlack';
      padding-top: 50px!important;
      padding-bottom: 10px!important;
    }
    .qty {

      input {
        border: 0;
        width: 34px;
        color: #003456;
        text-align: center;
        font-size: 20px;
        font-family: 'DinProBold';
        vertical-align: sub;
      }
      div {
        font-size: 36px;
        color: #003456;
        font-family: 'DinPro';
        width: 30px;
        height: 36px;
        cursor: pointer;
        display: inline-block;
        text-align: center;
        padding-bottom: 20px;
        vertical-align: middle;
        &.mage-error {
          display: none;
        }
      }
    }
    .col   {
      padding-top: 53px; //45
      padding-bottom: 0px;
      padding-left: 0;
      font-size: 18px;
      text-align: center;

      &.del {
        width: 14%;
        .action.action-delete:before {
          content:url("../images/bin.svg");
        }
      }
      .lib-clearfix();

      &.item {
        text-align:left;
        padding-top: 30px;
        padding-bottom: 0px;
        max-width: 420px;
      }
      .product-item-photo {
        display: inline-block;
        float: none;
        width: 150px;
        height: 120px;
        overflow:hidden;
        margin-top:-12px;
        img  {
          margin-top: -60px;
          height: 218px;
          width: 150px;
        }
      }

      .product-item-details {
        display: inline-block;
        width: ~"calc(100% - 180px)";
        margin-left: 0;
        font-family: 'DinProBold';
        .smalldesc {
          font-size:16px;
          color:@lf-blue;
          font-family: Eczar, arial;
          margin-top: -6px;
        }
      }
      .gift-options-cart-item {
        display:none;
      }

      .actions-toolbar {
        margin-top: -6px;
        margin-left: 20px;
        margin-right: -20px;

        .action-edit {
          display: none;
        }

        .action-delete {
          position: static;
          margin-top: 4px;
        }
      }
    }

    .col.subtotal {
      padding-right: 0;
      color:@lf-blue;
    }
  }
}

.action-towishlist {
  display: none!important;
}

@media only screen and (min-width: 300px) and (max-width: 1000px) {

  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_cc_option_long .iwd_opc_cc_preview {
    top: 45px!important;
    right:unset!important;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_cc_option_long {
    height: 70px!important;
  }
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper .iwd_opc_column.iwd_opc_payment_column {
    height:auto;
  }
  .checkout-cart-index {
    #header {
      height: 0px!important;
      min-height: 0!important;
    }
    #header .container {
      height: 50px!important;
      .delivery.fixednoanim .step1,
      .delivery .step1,
      .commande {
        display: none;
      }
      .step3 {
        background:none!important;
      }
    }
    #header .menu {
      display: none;
    }
    .columns {
      .column.main {
        margin-left: 0;
      }
    }
    .block.crosssell {
      margin-top: 0px;
      .product-items {
        margin-left: 0;
      }
    }
    .tunnel div {
      &:first-child {
        background:white;
        color:@lf-blue;
      }
    }

    .cart-container {
      display: flex;
      flex-direction: column;
      .checkout-methods-items .action.primary {
        border: 2px solid @lf-blue;
      }
      .action[data-role='proceed-to-checkout'] {
        background:@lf-blue;
        color:white!important;
        text-transform: uppercase;
      }
      .primary:not(.btn) {
        width:100%!important;
        font-size: 12px!important;
        font-family: 'DinProBold';
        text-transform: uppercase;
        letter-spacing: 3px;
        color: @lf-blue;
        &.apply {
          color:@lf-blue;
        }
      }
      .form-cart {
        order:1;
        .action.continue {
          display: none;
        }
      }

    }
    #shopping-cart-table {
      width:~"calc(100% - 40px)";
      margin:auto;
      thead {
        display: none;
        & + .item {
          border-top:0!important;
        }
      }
      .action.action-edit {
        display: none;
      }
      .item:last-child {
        border-bottom: 0;
      }
    }
    .guestCalculator {
      width: ~"calc(100% - 40px)";
      padding: 0 20px;
      position:relative;
      z-index: 5;
      top:0;
      height: auto;
      margin-bottom: 30px;
      order:3;
      margin-top:20px;
      h3 {
        font-size:16px;
        width:250px;
        white-space:initial;
        margin-top:0;
        &.open +  .guestCalculatorIn {
          height: auto;
          opacity:1;
          transition: all 0.3s ease;
          position: relative;
          overflow:visible;
          &:after {
            display:none;
          }
        }
        &.open:after {
          transform: rotate(225deg);
        }
        &:after {

          position: absolute;
          right:40px;
          margin-top: -55px;
          content: '';
          display: block;
          border-bottom: 2px solid @lf-blue;
          border-right: 2px solid @lf-blue;
          transform: rotate(45deg);
          border-radius: 2px;
          width: 14px;
          height: 14px;

        }
      }

      .guestCalculatorIn {
        transition: all 0.3s ease;
        opacity:0;
        background:none;
        height: 0;
        overflow:hidden;
        z-index: -1;
        position: fixed;
        background: #f6f6f6;
        font-family: 'DinProBlack';
        .nbPiecesConvives {
          margin-top:24px;
        }
        .nbPieces {
          font-family: 'DinProBold';
        }
        .half {
          width: 100%!important;
          padding-left:0!important;
          border-right:0!important;
          text-align:center!important;
          small {
            font-family: 'DinPro';
            font-size: 14px;
            letter-spacing: 1px;
            width:240px;
            line-height:20px;
            margin-top:-7px;
          }
        }
      }
    }
    .cart-summary {
      margin-top:50px;
      display: flex;
      flex-direction: column;
      padding-left: 20px;
      padding-right: 20px;
      order:4;
      .cart-totals {
        order:1;
        border-top:0;
        border-bottom: 1px solid @lf-blue;
        padding-bottom: 10px;
        .totals {
          &.sub sup {
            top:-0.25em;
          }
          sup {
            top:-0.55em;
          }
          .amount {
            font-size: 18px;
            font-family: 'DinProBold';
          }
        }
      }
      .block .fieldset {
        margin:20px 0 0 0;
      }
      .discount {
        order:2;
        .content {
          display: block;
        }
        input {
          height: 50px;
          border:1px solid @lf-blue;
          padding-left: 15px;
          font-size: 18px;
          font-family: Eczar, arial;
        }
        button {
          color:@lf-blue;
          margin-top: -60px;
          float: right;
          height: 50px;
          background: none;
          font-size: 18px;
          border:0;
        }
      }
      .checkout-methods-items {
        order:3;
        margin-bottom:60px;
      }
    }
  }
  .tunnel {
    border-top: 1px solid white;
    div {
      float: left;
      width: ~"calc(100% / 3 - 1px)";
      margin-top: 50px;
      background: @lf-blue;
      height: 50px;
      text-align: center;
      border-right: 1px solid white;
      font-family: 'DinProBold';
      text-transform: uppercase;
      font-size: 12px;
      color:white;
      letter-spacing: 1px;
      &:last-child{
        border-right: 1px solid @lf-blue;
      }
    }
    .btn-radio {
      float: none;
      padding: 0;
      margin-left: 0;
      svg {
        display: none;
      }
      input  {
        width:80px;
        height: 40px;
        -moz-appearance:button;
        -webkit-appearance:button;
        appearance:button;
        position: absolute;
        margin-left: -40px;
        margin-top: -10px;
        opacity:0;
        display: inline-block;
      }
    }
  }

  .cart.table-wrapper {
    margin-bottom: -30px;
    min-height:unset;
    .product-item-photo {
      width:120px;
      overflow:hidden;
      height: 120px;
      position: relative;
      display: inline-block;
      vertical-align: top;
      img {
        width: 110px;
        margin-top: -40px;
      }
    }

    .col.subtotal {
      display: none;
    }
    .col.qty {
      width: 50%;
      text-align: left;
      padding-left: 0;
      padding-top: 2px;
      &:before {
        display: none;
      }
    }

    .col.price {
      left: 135px;
      text-align: right;
      padding-right: 0;
      position: absolute;
      margin-top: -20px;
      width:auto!important;
      padding-top:0!important;
      padding-left: 15px!important;
      color:@lf-blue;
      &.barre {
        padding-top:0!important;
      }
      .barre {
        display: inline-block;
        & + div {
          display: inline-block;
          margin-left: 10px!important;
        }
      }
      &:before {
        display: none;
      }
    }

    .col.item {
      padding-right: 32px;
    }


    .product-item-details {
      width:~"calc(100% - 130px)";
      margin-left: 5px;
      display: inline-block;
      vertical-align: top;
      .smalldesc {
        font-family: Eczar, arial;
        color:@lf-blue;
        height: 20px;
        overflow:hidden;
      }
    }
    .product-item-name {
      font-family: 'DinProBlack';
      font-size: 18px;
      margin-top: 15px;
      height: 42px;
      line-height: 20px;
      overflow:hidden;
    }
    .cart.items {
      .col.item {
        padding:0!important;
        min-height: 110px;
      }
      .col.del {
        float: right;
        margin-top: -26px;
        box-sizing: border-box;
        .action.action-delete:before {
          content:url("../images/bin.svg");
        }
      }
      .col.subtotal {
        width:100%!important;
        text-align: left!important;
        padding-left: 128px;
        padding-top: 5px;
        color:@lf-blue;
        .price {
          color:@lf-blue;
          font-family: 'DinProBold';
          font-size: 20px;
        }
      }
      .col.qty {
        position: absolute;
        padding-left: 128px!important;
        width:245px!important;
        padding-top:0!important;
        div {
          width:25px;
          height:40px;
          font-family: 'DinPro';
          display: inline-block;
          vertical-align: middle;
          font-size: 40px;
          line-height: 30px;
          text-align: center;
        }
        .input-text {
          margin:0;
          vertical-align: middle;
          font-family: 'DinProBold';
          border:0;
          width:37px;
          color:@lf-blue;
          font-size: 20px;
        }
      }
    }

  }

}



.checkout-cart-index .produits .categorie .container .produit  {
  width:23%;
  cursor:default!important;
  margin-left:0;
  margin-right:0.8%;
  height:auto;
  &:hover {
    margin-left:-3px;
    margin-right:~"calc(0.8% - 3px)";

    div.visu {
      background:none!important;
      cursor:default!important;
      img.prod {
        opacity:1!important;
      }
    }
  }
}

.checkout-cart-index .crosssell {
  .product-items {
    margin-left:0!important;
  }
  .addtocart button {
    width:50px!important;
    height:35px!important;
    float:right;
    background:white!important;
    border:0!important;
    img {
      margin-top:-15px!important;
    }
  }
}
@media only screen and (min-width: 300px) and (max-width: 700px) {
  .checkout-cart-index .produits .categorie .container .produit  {
    width:48%;
  }
  .product-item-actions {
    display:block!important;
  }
  #co-payment-form {
    min-height:none;
    height:auto;
  }
}

@media only screen and (min-width: 300px) and (max-width: 374px) {
  .guestCalculator h3 {
    width:230px!important;
    font-size: 14px!important;
  }
  .cart-container {

    .checkout-methods-items {

      .action.primary {


        &[data-role='proceed-to-checkout'] {
          background-position:40px 10px;
        }
      }
    }
  }
}

@media only screen and (min-width: 1000px)  {
  .iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .field .iwd_opc_select_container:not(.selected) .iwd_opc_select_option.iwd_opc_cc_option_long:after {
    width:14px!important;
    height: 14px!important;
  }
}

.onepage-index-index,
.checkout-index-index,
.checkout-cart-index  {
  .minicart-wrapper .ui-dialog {
    display:none!important;
  }
}

.checkout-cart-index .page-bottom  {
  margin-top: 50px;
}


