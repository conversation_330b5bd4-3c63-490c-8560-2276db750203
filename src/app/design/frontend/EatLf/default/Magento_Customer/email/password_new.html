<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Reset your %store_name password" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var this.getUrl(store, 'customer/account/')":"Customer Account URL",
"var customer.name":"Customer Name"
} @-->
{{template config_path="design/email/header_template"}}
<div class="lf_container">
   
    <p class="greeting">{{trans "%name," name=$customer.name}}</p>
    <p>{{trans "There was recently a request to change the password for your account."}}</p>
    <p>{{trans "If you requested this change, set a new password here:"}}</p>

    <table class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td>
                <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
                    <tr>
                        <td align="center">
                            <a href="{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}" target="_blank">{{trans "Set a New Password"}}</a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

<p>{{trans "If you did not make this request, you can ignore this email and your password will remain the same."}}</p>
</div>
{{template config_path="design/email/footer_template"}}
