<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Account\Dashboard\Info $block */
?>
<div class="block block-dashboard-info">

    <div class="block-content">
        <div class="box box-information">
            <h3 class="box-title">
                <span><?= $block->escapeHtml(__('Contact Information')) ?></span>
            </h3>
            <div class="box-content">
                <p>
                    <?= $block->escapeHtml($block->getName()) ?><br>
                    <?= $block->escapeHtml($block->getCustomer()->getEmail()) ?><br>
                    <?= $block->escapeHtml($block->getCustomer()->getCustomAttribute('telephone')->getValue()) ?><br>
                </p>
            </div>
            <div class="box-actions">
                <a class="action edit" href="<?= $block->escapeUrl($block->getUrl('customer/account/edit')) ?>">
                    <span><?= $block->escapeHtml(__('Edit')) ?></span>
                </a>
                <a href="<?= $block->escapeUrl($block->getChangePasswordUrl()) ?>" class="action change-password">
                    <?= $block->escapeHtml(__('Change Password')) ?>
                </a>
            </div>
        </div>
        <?php if ($block->isNewsletterEnabled()): ?>
            <div class="box box-newsletter">
                <h3 class="box-title">
                    <span><?= $block->escapeHtml(__('Newsletters')) ?></span>
                </h3>
                <div class="box-content">
                    <p>
                        <?php if ($block->getIsSubscribed()): ?>
                            <?= $block->escapeHtml(__('You are subscribed to "General Subscription".')) ?>
                        <?php else: ?>
                            <?= $block->escapeHtml(__('You aren\'t subscribed to our newsletter.')) ?>
                        <?php endif; ?>
                    </p>
                    <?php /* Extensions placeholder */ ?>
                    <?= $block->getChildHtml('customer.account.dashboard.info.extra') ?>
                </div>
                <div class="box-actions">
                    <a class="action edit" href="<?= $block->escapeUrl($block->getUrl('newsletter/manage')) ?>"><span><?= $block->escapeHtml(__('Edit')) ?></span></a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
