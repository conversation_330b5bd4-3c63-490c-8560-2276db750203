<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Edit $block */
?>
<form class="form form-edit-account" action="<?= $block->escapeUrl($block->getUrl('customer/account/editPost')) ?>" method="post" id="form-validate" enctype="multipart/form-data" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>" autocomplete="off">
    <fieldset class="fieldset info">
        <?= $block->getBlockHtml('formkey') ?>
        <legend class="legend"><span><?= $block->escapeHtml(__('Account Information')) ?></span></legend><br>
        <?= $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Name')->setObject($block->getCustomer())->toHtml() ?>

        <?php $_dob = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Dob') ?>
        <?php if ($_dob->isEnabled()): ?>
            <?= $_dob->setDate($block->getCustomer()->getDob())->toHtml() ?>
        <?php endif ?>

        <?php $_taxvat = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Taxvat') ?>
        <?php $_gender = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Gender') ?>
        <?= $block->getLayout()->createBlock('EatLf\Customer\Block\Widget\Telephone')->setFormulaireType('edit')->setObject($block->getCustomer())->toHtml() ?>


        <?php if ($_taxvat->isEnabled()): ?>
            <?= $_taxvat->setTaxvat($block->getCustomer()->getTaxvat())->toHtml() ?>
        <?php endif ?>
        <?php if ($_gender->isEnabled()): ?>
            <?= $_gender->setGender($block->getCustomer()->getGender())->toHtml() ?>
        <?php endif ?>
        <div class="field choice">
            <input type="checkbox" name="change_email" id="change-email" data-role="change-email" value="1" title="<?= $block->escapeHtmlAttr(__('Change Email')) ?>" class="checkbox" />
            <label class="label" for="change-email"><span><?= $block->escapeHtml(__('Change Email')) ?></span></label>
        </div>
        <div class="field choice">
            <input type="checkbox" name="change_password" id="change-password" data-role="change-password" value="1" title="<?= $block->escapeHtmlAttr(__('Change Password')) ?>"<?php if ($block->getChangePassword()): ?> checked="checked"<?php endif; ?> class="checkbox" />
            <label class="label" for="change-password"><span><?= $block->escapeHtml(__('Change Password')) ?></span></label>
        </div>
    </fieldset>
    <br><br>
    <fieldset class="fieldset password" data-container="change-email-password">
        <legend class="legend"><span data-title="change-email-password"><?= $block->escapeHtml(__('Change Email and Password')) ?></span></legend><br>
        <div class="field email required" data-container="change-email">
            <label class="label" for="email"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email" name="email" id="email" autocomplete="email" data-input="change-email" value="<?= $block->escapeHtmlAttr($block->getCustomer()->getEmail()) ?>" title="<?= $block->escapeHtmlAttr(__('Email')) ?>" class="input-text" data-validate="{required:true, 'validate-email':true}" />
            </div>
        </div>
        <div class="field password current required">
            <label class="label" for="current-password"><span><?= $block->escapeHtml(__('Current Password')) ?></span></label>
            <div class="control">
                <input type="password" class="input-text" name="current_password" id="current-password" data-input="current-password" autocomplete="off" />
            </div>
        </div>
        <div class="field new password required" data-container="new-password">
            <label class="label" for="password"><span><?= $block->escapeHtml(__('New Password')) ?></span></label>
            <div class="eye"></div>
            <div class="control">
                <input type="password" class="input-text" name="password" id="password"
                       data-password-min-length="<?= $block->escapeHtml($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $block->escapeHtml($block->getRequiredCharacterClassesNumber()) ?>"
                       data-input="new-password"
                       data-validate="{required:true}"
                       autocomplete="off" />

                <div class="password-bubble">
                    <p>Votre mot de passe doit contenir</p>
                    <ul>
                        <li id="has-char-count"><span class="validation-text">Au moins 8 caractères (<span id="currentcount">0</span>/8)</span></li>
                        <li id="has-upper-and-lowercase-char"><span class="validation-text">Des majuscules et minuscules</span></li>
                        <li id="has-number"><span class="validation-text">Au moins 1 chiffre</span></li>
                    </ul>
                </div>

            </div>
        </div>
        <div class="field confirm password required" data-container="confirm-password">
            <label class="label" for="password-confirmation"><span><?= $block->escapeHtml(__('Confirm New Password')) ?></span></label>
            <div class="control">
                <input type="password" class="input-text" name="password_confirmation" id="password-confirmation"
                       data-input="confirm-password"
                       autocomplete="off" />
            </div>
        </div>
    </fieldset>
    <?= $block->getChildHtml('form_additional_info') ?>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="btn btn-8h action save primary" title="<?= $block->escapeHtmlAttr(__('Save')) ?>"><span><?= $block->escapeHtml(__('Save')) ?></span></button>
        </div>
    </div>
</form>

<script>
    require([
        "jquery",
        "mage/mage"
    ], function($){
        var dataForm = $('#form-validate');
        var ignore = <?= /* @noEscape */ $_dob->isEnabled() ? '\'input[id$="full"]\'' : 'null' ?>;

        dataForm.mage('validation', {
            <?php if ($_dob->isEnabled()): ?>
            errorPlacement: function(error, element) {
                if (element.prop('id').search('full') !== -1) {
                    var dobElement = $(element).parents('.customer-dob'),
                        errorClass = error.prop('class');
                    error.insertAfter(element.parent());
                    dobElement.find('.validate-custom').addClass(errorClass)
                        .after('<div class="' + errorClass + '"></div>');
                }
                else {
                    error.insertAfter(element);
                }
            },
            ignore: ':hidden:not(' + ignore + ')'
            <?php else: ?>
            ignore: ignore ? ':hidden:not(' + ignore + ')' : ':hidden'
            <?php endif ?>
        });

    });
</script>
<script type="text/x-magento-init">
    {
        "[data-role=change-email], [data-role=change-password]": {
            "changeEmailPassword": {
                "titleChangeEmail": "<?= $block->escapeJs($block->escapeHtml(__('Change Email'))) ?>",
                "titleChangePassword": "<?= $block->escapeJs($block->escapeHtml(__('Change Password'))) ?>",
                "titleChangeEmailAndPassword": "<?= $block->escapeJs($block->escapeHtml(__('Change Email and Password'))) ?>"
            }
        }
    }
</script>

