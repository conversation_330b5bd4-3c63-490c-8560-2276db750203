<?php

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Register $block
 *  @var \Magento\Framework\Escaper $escaper
 */
?>
<?= $block->getChildHtml('form_fields_before') ?>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.register.extra') ?>
<form class="form create account form-create-account-phone" action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>" method="post" id="form-validate" enctype="multipart/form-data" autocomplete="off" data-mage-init='{"validation":{}}'>
    <?= /* @noEscape */ $block->getBlockHtml('formkey'); ?>
    <fieldset class="fieldset create info">
        <legend class="legend legend-phone"><span><?= $block->escapeHtml(__('Step 1/2')) ?></span></legend><br>
        <input type="hidden" name="success_url" value="<?= $block->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $block->escapeUrl($block->getErrorUrl()) ?>">
        <?= $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Name')->setObject($block->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>

        <div class="field required">
            <label for="email_address" class="label"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email" name="email" autocomplete="email" id="email_address" value="<?= $block->escapeHtmlAttr($block->getFormData()->getEmail()) ?>" title="<?= $block->escapeHtmlAttr(__('Email')) ?>" class="input-text" data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>
        <div class="field password required">
            <label for="password" class="label"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
            <div class="eye"></div>
            <div class="control">

                <input type="password" name="password" id="password"
                       title="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                       class="input-text"
                       data-password-min-length="<?= $block->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $block->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">

                <div class="password-bubble">
                    <p>Votre mot de passe doit contenir</p>
                    <ul>
                        <li id="has-char-count"><span class="validation-text">Au moins 8 caractères (<span id="currentcount">0</span>/8)</span></li>
                        <li id="has-upper-and-lowercase-char"><span class="validation-text">Des majuscules et minuscules</span></li>
                        <li id="has-number"><span class="validation-text">Au moins 1 chiffre</span></li>
                    </ul>
                </div>

            </div>

        </div>
        <div class="field confirmation required">
            <label for="password-confirmation" class="label"><span><?= $block->escapeHtml(__('Confirm Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password_confirmation" title="<?= $block->escapeHtmlAttr(__('Confirm Password')) ?>" id="password-confirmation" class="input-text" data-validate="{required:true, equalTo:'#password'}" autocomplete="off">
            </div>
        </div>



        <?php $_dob = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Dob') ?>
        <?php if ($_dob->isEnabled()): ?>
            <div class="field">
                <label for="dob" class="label"><span><?= __('Date of Birth') ?></span></label>
                <div class="control">
                    <input name="dob" id="dob" title="Date of Birth" value="" type="text">

                    <script type="text/javascript">
                        require(["jquery", "mage/calendar"], function($){
                            $("#dob").calendar({
                                showsTime: false,
                                prevText: '&#x3c;Prec', prevStatus: '',
                                prevJumpText: '&#x3c;&#x3c;', prevJumpStatus: '',
                                nextText: 'Suiv&#x3e;', nextStatus: '',
                                nextJumpText: '&#x3e;&#x3e;', nextJumpStatus: '',
                                monthNames: ['janv.','fevr.','mars.','avr.','mai.','juin.',
                                    'juil.','aôut.','sept.','oct.','nov.','dec.'],
                                monthNamesShort: ['janv.','fevr.','mars.','avr.','mai.','juin.',
                                    'juil.','aôut.','sept.','oct.','nov.','dec.'],
                                dayNames: ['LUN.','MAR.','MER.','JEU.','VEN.','SAM.','DIM.'],
                                dayNamesShort: ['LUN.','MAR.','MER.','JEU.','VEN.','SAM.','DIM.'],
                                dayNamesMin: ['LUN.','MAR.','MER.','JEU.','VEN.','SAM.','DIM.'],
                                dateFormat: "dd/MM/y",
                                yearRange: "-120y:c+nn",
                                buttonImageOnly: false ,
                                buttonText: "Sélectionner une date",  changeMonth: true, changeYear: true, showOn: "both"})
                        });
                    </script>
                </div>
            </div>
        <?php endif ?>

                <?= $block->getLayout()->createBlock('EatLf\Customer\Block\Widget\Telephone')->setFormulaireType('creation')->setObject($block->getFormData())->toHtml() ?>

                <?= $block->getChildHtml('customer.form.register.newsletter') ?>

                <?php if ($block->isNewsletterEnabled() && $block->getChildHtml('customer.form.register.newsletter')==null): ?>
                    <div class="field choice newsletter">
                        <input type="checkbox" name="is_subscribed" title="<?= $block->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1" id="is_subscribed"<?php if ($block->getFormData()->getIsSubscribed()): ?> checked="checked"<?php endif; ?> class="checkbox">
                        <label for="is_subscribed" class="label"><span><?= $block->escapeHtml(__('Sign Up for Newsletter')) ?></span></label>
                    </div>
                <?php endif ?>

        <div class="actions-toolbar"><br>
            <div class="primary">
                <button type="submit" class="btn btn-8h action submit primary" title="<?= $block->escapeHtmlAttr(__('Create an Account')) ?>"><span><?= $block->escapeHtml(__('Create an Account')) ?></span></button>
            </div>
            <!--
            <div class="secondary">
                <a class="action back" href="<?= $block->escapeUrl($block->getBackUrl()) ?>"><span><?= $block->escapeHtml(__('Back')) ?></span></a>
            </div>
            -->
        </div>

        <?php $_taxvat = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Taxvat') ?>
        <?php if ($_taxvat->isEnabled()): ?>
            <?= $_taxvat->setTaxvat($block->getFormData()->getTaxvat())->toHtml() ?>
        <?php endif ?>

        <?php $_gender = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Gender') ?>
        <?php if ($_gender->isEnabled()): ?>
            <?= $_gender->setGender($block->getFormData()->getGender())->toHtml() ?>
        <?php endif ?>
        <?= $block->getChildHtml('form_additional_info') ?>
        <?php if ($block->getShowAddressFields()): ?>
            <fieldset class="fieldset address">
                <legend class="legend"><span><?= $block->escapeHtml(__('Address Information')) ?></span></legend><br>
                <input type="hidden" name="create_address" value="1" />

                <?php $_company = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Company') ?>
                <?php if ($_company->isEnabled()): ?>
                    <?= $_company->setCompany($block->getFormData()->getCompany())->toHtml() ?>
                <?php endif ?>

                <?php $_telephone = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Telephone') ?>
                <?php if ($_telephone->isEnabled()): ?>
                    <?= $_telephone->setTelephone($block->getFormData()->getTelephone())->toHtml() ?>
                <?php endif ?>

                <?php $_fax = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Fax') ?>
                <?php if ($_fax->isEnabled()): ?>
                    <?= $_fax->setFax($block->getFormData()->getFax())->toHtml() ?>
                <?php endif ?>

                <?php $_streetValidationClass = $this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('street'); ?>

                <div class="field street required">
                    <label for="street_1" class="label"><span><?= $block->escapeHtml(__('Street Address')) ?></span></label>
                    <div class="control">
                        <input type="text" name="street[]" value="<?= $block->escapeHtmlAttr($block->getFormData()->getStreet(0)) ?>" title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>" id="street_1" class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>">
                        <div class="nested">
                            <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                            <?php for ($_i = 2, $_n = $this->helper('Magento\Customer\Helper\Address')->getStreetLines(); $_i <= $_n; $_i++): ?>
                                <div class="field additional">
                                    <label class="label" for="street_<?= /* @noEscape */ $_i ?>">
                                        <span><?= $block->escapeHtml(__('Address')) ?></span>
                                    </label>
                                    <div class="control">
                                        <input type="text" name="street[]" value="<?= $block->escapeHtml($block->getFormData()->getStreetLine($_i - 1)) ?>" title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i)) ?>" id="street_<?= /* @noEscape */ $_i ?>" class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>">
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>

                <div class="field required">
                    <label for="city" class="label"><span><?= $block->escapeHtml(__('City')) ?></span></label>
                    <div class="control">
                        <input type="text" name="city" value="<?= $block->escapeHtmlAttr($block->getFormData()->getCity()) ?>" title="<?= $block->escapeHtmlAttr(__('City')) ?>" class="input-text <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('city')) ?>" id="city">
                    </div>
                </div>

                <div class="field region required">
                    <label for="region_id" class="label"><span><?= $block->escapeHtml(__('State/Province')) ?></span></label>
                    <div class="control">
                        <select id="region_id" name="region_id" title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>" class="validate-select" style="display:none;">
                            <option value=""><?= $block->escapeHtml(__('Please select a region, state or province.')) ?></option>
                        </select>
                        <input type="text" id="region" name="region" value="<?= $block->escapeHtml($block->getRegion()) ?>" title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>" class="input-text <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('region')) ?>" style="display:none;">
                    </div>
                </div>

                <div class="field zip required">
                    <label for="zip" class="label"><span><?= $block->escapeHtml(__('Zip/Postal Code')) ?></span></label>
                    <div class="control">
                        <input type="text" name="postcode" value="<?= $block->escapeHtmlAttr($block->getFormData()->getPostcode()) ?>" title="<?= $block->escapeHtmlAttr(__('Zip/Postal Code')) ?>" id="zip" class="input-text validate-zip-international <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('postcode')) ?>">
                    </div>
                </div>

                <div class="field country required">
                    <label for="country" class="label"><span><?= $block->escapeHtml(__('Country')) ?></span></label>
                    <div class="control">
                        <?= $block->getCountryHtmlSelect() ?>
                    </div>
                </div>
                <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes');?>
                <?php if ($addressAttributes): ?>
                    <?php $addressAttributes->setEntityType('customer_address'); ?>
                    <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]');?>
                    <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address');?>
                    <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
                <?php endif;?>
                <input type="hidden" name="default_billing" value="1">
                <input type="hidden" name="default_shipping" value="1">
            </fieldset>

        <?php endif; ?>
    </fieldset>
</form>

<?php if ($block->getShowAddressFields()): ?>
<script type="text/x-magento-init">
    {
        "#country": {
            "regionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */ $block->getConfig('general/region/display_all') ? 'true' : 'false' ?>,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getRegionJson() ?>,
                "defaultRegion": "<?= (int) $block->getFormData()->getRegionId() ?>",
                "countriesWithOptionalZip": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getCountriesWithOptionalZip(true) ?>
            }
        }
    }
</script>
<?php endif; ?>

<script type="text/x-magento-init">
    {
        ".field.password": {
            "passwordStrengthIndicator": {
                "formSelector": "form.form-create-account"
            }
        }
    }
</script>
