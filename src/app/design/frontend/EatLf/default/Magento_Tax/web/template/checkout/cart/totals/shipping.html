<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: isCalculated() && quoteIsVirtual == 0 -->
<!-- ko if: isBothPricesDisplayed() -->
<tr class="totals shipping excl">
    <th class="mark" scope="row">
        <span class="label" data-bind="text: title + ' ' + excludingTaxMessage"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <span class="value" data-bind="text: getShippingMethodTitle()"></span>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getExcludingValue(), attr: {'data-th': excludingTaxMessage}"></span> <sup>HT</sup>
    </td>
</tr>
<tr class="totals shipping incl">
    <th class="mark" scope="row">
        <span class="label" data-bind="text: title + ' ' + includingTaxMessage"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <span class="value" data-bind="text: getShippingMethodTitle()"></span>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getIncludingValue(), attr: {'data-th': title + ' ' + excludingTaxMessage}"></span> <sup>TTC</sup>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: isIncludingDisplayed() -->
<tr class="totals shipping incl">
    <th class="mark" scope="row">
        <span class="label" data-bind="i18n: title"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
    </th>
    <!-- ko if: totals()['shipping_incl_tax'] > 0 -->
    <td class="amount">
            <span class="price"
                  data-bind="text: getIncludingValue(), attr: {'data-th': title}"></span> <sup>TTC</sup>
    </td>
    <!-- /ko -->
    <!-- ko if: totals()['shipping_incl_tax'] == 0 -->
    <td class="amount">
            <span class="free"
                  data-bind="i18n: 'Offered'"></span>
    </td>
    <!-- /ko -->
</tr>
<!-- /ko -->
<!-- ko if: isExcludingDisplayed() -->
<tr class="totals shipping excl">
    <th class="mark" scope="row">
        <span class="label" data-bind="i18n: title"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <span class="value" data-bind="text: getShippingMethodTitle()"></span>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getValue(), attr: {'data-th': title}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- /ko -->
