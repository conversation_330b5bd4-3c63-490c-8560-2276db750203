<?php
/**  @var $block \Magento\Checkout\Block\Cart */
?>
<?php echo $block->getChildHtml('checkout_cart_empty_widget'); ?>
<div class="iwd_empty_cart_wrapper iwd_main_wrapper">
    <div class="iwd_opc_alternative_wrapper">
        <div class="iwd_opc_column iwd_opc_empty_cart_column">
            <div class="iwd_opc_alternative_column">
                <h2 class="text-center">
                    <img style="margin-right: 15px;" src="<?= /* @escapeNotVerified */
                            $block->getViewFileUrl('images/basket2.svg') ?>">  <?php echo __('Empty Cart'); ?>
                </h2>
                <div class="iwd_opc_column_content">
                   
                    <div class="iwd_opc_universal_wrapper iwd_opc_empty_cart_info">
                        <?php echo $block->escapeHtml(
                            __('Looks like there\'s nothing here yet. Add something to your shopping cart to get started.')
                        ); ?>
                    </div>
                    <div class="iwd_opc_universal_wrapper">
                        <button class="btn btn-8h"
                           onclick="window.location='<?php echo $block->getUrl(); ?>';"
                           title="<?php echo $block->escapeHtml(__('Continue Shopping')); ?>">
                            <?php echo $block->escapeHtml(__('Continue Shopping')); ?>
                        </button>
                    
                    <?php if ($block->getQuote()->getCustomer()->getId()) { ?>
                       
                            <button class="btn btn-8h"
                               onclick="window.location='<?php echo $block->getUrl('customer/account'); ?>';"
                               title="<?php echo $block->escapeHtml(__('My Account')); ?>">
                                <?php echo $block->escapeHtml(__('My Account')); ?>
                            </button>
                        
                    <?php } else { ?>
                        
                             <button class="btn btn-8h"
                              onclick="window.location='<?php echo $block->getUrl('customer/account/login'); ?>';"
                               title="<?php echo $block->escapeHtml(__('Log In')); ?>"> 
                                <?php echo $block->escapeHtml(__('Log In')); ?>
                            </button>
                         
                    <?php } ?> 
                    <div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $block->getChildHtml('shopping.cart.table.after'); ?>
<style type="text/css">
    .page-title-wrapper, 
    .guestCalculator {
        display: none;
    } 
</style>