<?php
/**
 * @var $block IWD\Opc\Block\Onepage
 */
?>
<?php //$isVirtual = $block->getQuote()->isVirtual() ?>
<?php
/**
 * @var $checkoutSession \Magento\Checkout\Model\Session\Proxy
 */
$checkoutSession = \Magento\Framework\App\ObjectManager::getInstance()
    ->get(\Magento\Checkout\Model\Session\Proxy::class);
$isVirtual = $checkoutSession->getQuote()->isVirtual() ?>
<div class="iwd_opc_clear"></div>
<div id="checkout-loader" data-role="checkout-loader" class="loading-mask" data-mage-init='{"checkoutLoader": {}}'>
    <div class="loader"></div>
</div>
<div id="iwd_opc_iframe_container" class="iwd_main_wrapper iwd_opc_wrapper " style="display: none;">
    <div class="iwd_opc_alternative_wrapper">
        <div class="iwd_opc_column iwd_opc_iframe_payment_column">
            <div class="iwd_opc_alternative_column">
                <div class="iwd_opc_universal_wrapper iwd_opc_column_name">
                    <?php echo __('Choose a Different Payment Method'); ?>
                </div>
                <div class="iwd_opc_column_content">

                </div>
            </div>
        </div>
    </div>
</div>
 <div class="tunnel">
    <div>
        <label class="btn-radio">
            <input type="radio" name="tunnel1" value="1" id="tunnel1" checked onclick="window.location=jQuery('.action.viewcart').attr('href');">
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
            </label>
            <br>
            Panier
    </div>
    <div>
        <label class="btn-radio">
            <input type="radio" name="tunnel2" value="2" id="tunnel2" checked>
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
            </label>
            <br>
           Livraison
    </div>
    <div>
        <label class="btn-radio">
            <input type="radio" name="tunnel3" value="3" id="tunnel3" disabled>
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
           </label>
           <br>
           Confirmation
    </div>
</div>



<div id="checkout" data-bind="scope:'checkout'"
     class="iwd_opc_wrapper iwd_main_wrapper">
    <div class="iwd_opc_alternative_wrapper">



        <?php if ($isVirtual) { ?>
        <div class="iwd_opc_two_column_wrapper">
            <?php } ?>
            <!-- ko template: getTemplate() --><!-- /ko -->
            <?php if ($isVirtual) { ?>
        </div>
    <?php } ?>
        <script type="text/x-magento-init">
        {
            "#checkout": {
                "Magento_Ui/js/core/app": <?php /* @escapeNotVerified */
                echo $block->getJsLayout(); ?>
            }
        }
        </script>
        <script>
            window.checkoutConfig = <?php /* @escapeNotVerified */
            echo \Laminas\Json\Json::encode($block->getCheckoutConfig()); ?>;
            // Create aliases for customer.js model from customer module
            window.isCustomerLoggedIn = window.checkoutConfig.isCustomerLoggedIn;
            window.customerData = window.checkoutConfig.customerData;
        </script>
        <?php if (!$isVirtual) { ?>
            <?php echo $block->getChildHtml('checkout.gift_options', false) ?>
        <?php } ?>
        <?php echo $block->getChildHtml('checkout.comment.placeholder', false); ?>
        <script>
            require([
                'mage/url',
                'IWD_Opc/js/block-loader'
            ], function (url, iwdBlockLoader) {
                iwdBlockLoader();
                return url.setBaseUrl('<?php /* @escapeNotVerified */ echo $block->getBaseUrl();?>');
            });


        </script>

    </div>

		<?php
		$objectManager =  \Magento\Framework\App\ObjectManager::getInstance();
          $moduleManager = $objectManager->get('\Magento\Framework\Module\Manager');

		if($moduleManager->isEnabled('Amazon_Login')){ ?>
		 <div class="amazon-button-container" id="minicart-amazon-pay-button" style="display:none;">
                   <div class="amazon-button-container centered-button" style="margin-left:45%;">
    <div class="amazon-button-container__cell">
        <div id="PayWithAmazon-<?php echo $block->getJsId() ?>" class="login-with-amazon" data-mage-init='{"amazonButton": {"buttonType": "PwA"}}'></div>
    </div>

    <div class="amazon-button-container__cell">
        <div class="field-tooltip toggle">
            <span class="field-tooltip-action action-help" data-bind="mageInit: {'dropdown':{'activeClass': '_active'}}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></span>
            <div class="field-tooltip-content" data-target="dropdown" aria-hidden="true"><?php /* @noEscape */  echo __('Are you an Amazon customer? Pay now with address and payment details stored in your Amazon account.'); ?></div>
        </div>
    </div>
</div>

                </div>
		<?php  } ?>




</div>
<script>
require([
    "jquery",
    "mage/calendar"
], function($){

    $.extend(true, $, {
        calendarConfig: {
            dayNames: ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],
            dayNamesMin: ["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],
            monthNames: ["January","February","March","April","May","June","July","August","September","October","November","December"],
            monthNamesShort: ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],
            infoTitle: "About the calendar",
            firstDay: 0,
            closeText: "Close",
            currentText: "Go Today",
            prevText: "Previous",
            nextText: "Next",
            weekHeader: "WK",
            timeText: "Time",
            hourText: "Hour",
            minuteText: "Minute",
            dateFormat: $.datepicker.RFC_2822,
            showOn: "button",
            showAnim: "",
            changeMonth: true,
            changeYear: true,
            buttonImageOnly: null,
            buttonImage: null,
            showButtonPanel: true,
            showOtherMonths: true,
            showWeek: false,
            timeFormat: '',
            showTime: false,
            showHour: false,
            showMinute: false,
            serverTimezoneSeconds: 1521082614,
            serverTimezoneOffset: 0,
            yearRange: '1918:2118'
        }
    });

enUS = {"m":{"wide":["January","February","March","April","May","June","July","August","September","October","November","December"],"abbr":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}}; // en_US locale reference

});

</script>
<div class="iwd_opc_clear"></div>
