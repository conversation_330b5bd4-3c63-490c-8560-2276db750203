<?php
/**
 * @var $block \IWD\Opc\Block\Onepage\Success
 */
?>
<?php
$isShowRegistrationForm = $block->isShowRegistrationForm();
?>
<div class="tunnel"> 
    <div>
        <label class="btn-radio">
            <input type="radio" name="tunnel1" value="1" id="tunnel1" checked>
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
            </label>
            <br>
            Panier
    </div>
    <div>        
        <label class="btn-radio">
            <input type="radio" name="tunnel2" value="2" id="tunnel2" checked>
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
            </label>
            <br> 
           Livraison
    </div>
    <div>
        <label class="btn-radio">
            <input type="radio" name="tunnel3" value="3" id="tunnel3" checked>
            <svg width="26px" height="26px" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="9"></circle>
                <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
            </svg>
           </label> 
           <br>
           Confirmation
    </div> 
</div>  
 

<div class="iwd_success_page_wrapper">
    <div class="iwd_opc_alternative_wrapper">
        <div class="iwd_opc_column iwd_opc_success_page_column">
            <div class="iwd_opc_alternative_column"> 
                <div class="iwd_opc_column_content"> 
                    <div class="iwd_opc_universal_wrapper iwd_success_page_info">
 
                        <?php if ($block->getOrderId()) { ?>
                            <h3>
                            <?php if ($block->getCanViewOrder()) { ?>
                                <?php echo sprintf(__(
                                    'Your order (%s) has been placed. <br>You will receive an email with more details of your purchase.'),
                                    sprintf(
                                        '<a href="%s">#%s</a>',
                                        $block->escapeHtml($block->getViewOrderUrl()),
                                        $block->escapeHtml($block->getOrderId())
                                    )
                                ); ?>
                            <?php } else { ?>
                                <?php echo sprintf(__(
                                    'Your order (%s) has been placed. <br>You will receive an email with more details of your purchase.'),
                                    '#' . $block->escapeHtml($block->getOrderId())
                                ); ?>
                            <?php } ?>
                        <?php } ?>
                            </h3> 
                        <?php echo $block->getAdditionalInfoHtml() ?>
                   
                   <br><br><br>

                        <button onclick="window.location='<?php echo $block->getUrl(); ?>'"
                           class="btn btn-8h"
                           title="<?php echo __('Continue Shopping'); ?>">
                            <?php echo __('Continue Shopping'); ?>
                        </button>
                   
                    <?php if ($block->isCustomerLoggedIn()) { ?>
                         
                             <button onclick="window.location='<?php echo $block->getCustomerAccountUrl(); ?>'"
                               class="btn btn-8h"
                               title="<?php echo __('My Account'); ?>">
                                <?php echo __('My Account'); ?>
                            </button>
                        
                    <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style type="text/css">
    .page-title-wrapper {
        display: none;
    }
</style>
