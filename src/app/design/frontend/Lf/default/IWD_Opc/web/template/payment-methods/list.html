<div class="items payment-methods">
    <div class="field">
        <select class="select"
                style="display:none"
                id="iwd_opc_payment_method_select"
                data-validate="{required:true}"
                data-can-unselect="false"
                data-bind="
                        value: selectedPaymentMethod,
                        options: paymentMethods,
                        optionsText: 'title',
                        optionsValue: 'method',
                        optionsCaption: '',
                        event: {change: function(data, event){selectPaymentMethod(data, event, selectedPaymentMethod())}}
                    "></select>

        <div tabindex="0"
             data-element-id="iwd_opc_payment_method_select"
             data-can-unselect="false"
             class="iwd_opc_select_container">

            <div repeat="foreach: paymentMethods, item: '$method'"
                 class="iwd_opc_select_option"
                 data-position-top="0"
                 data-bind='
                            attr: { "data-value": $method().method, "data-first-letter": $method().title.charAt(0).toLowerCase() },
                            css: typeof $method().image !== "undefined" ? "iwd_opc_option_with_image" : ""
                         '>

                <div style="position:relative">
                    <!-- ko if: typeof $method().header !== "undefined" -->
                    <span class="method-title" data-bind="text: $method().header"></span>
                    <!-- /ko -->

                    <!-- ko if: typeof $method().header === "undefined" -->
                    <span class="method-title" data-bind="text: $method().title"></span>
                    <!-- /ko -->

                    <!-- ko if: typeof $method().image !== "undefined" -->
                    <img class="iwd_opc_option_image" data-bind="attr: { src: $method().image }">
                    <!-- /ko -->

                    <!-- ko if: typeof $method().subtitle !== "undefined" -->
                    <div data-bind="html: $method().subtitle"></div>
                    <!-- /ko -->
                </div>


                <each args="data: getRegion($method().method)" render=""></each>
            </div>
        </div>
    </div>
    <div repeat="foreach: paymentGroupsList, item: '$group'">
        <each args="data: getRegion($group().displayArea), as: 'method'" render=""></each>
    </div>

    <div data-bind="afterRender: refreshSelectedValue"></div>
</div>

