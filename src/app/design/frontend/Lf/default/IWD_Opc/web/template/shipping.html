<div class="iwd_opc_column iwd_opc_address_column" data-bind="visible: !quoteIsVirtual">
    <div class="iwd_opc_alternative_column">
        <div class="iwd_opc_universal_wrapper iwd_opc_column_name" data-bind="i18n: 'Shipping Address'"></div>
        <div tabindex="-1" class="iwd_opc_column_content">

            <!-- ko if: (!quoteIsVirtual) -->
            <!-- ko foreach: getRegion('customer-email') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
            <!--/ko-->

            <!-- ko template: 'IWD_Opc/shipping-address/list' --><!-- /ko -->

            <!-- ko foreach: getRegion('address-list-additional-addresses') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->

            <!-- ko foreach: getRegion('before-form') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->

            <div id="opc-new-shipping-address" data-bind="visible: isAddressFormVisible">
                <!-- ko if: customerHasAddresses -->
                <div class="iwd_opc_section_delimiter"></div>
                <!--/ko-->
                <!-- ko template: 'IWD_Opc/shipping-address/form' --><!-- /ko -->
            </div>
        </div>
    </div>
</div>


<div class="iwd_opc_column iwd_opc_shipping_column" data-bind="visible: false">
    <div class="iwd_opc_alternative_column">
        <div class="iwd_opc_universal_wrapper iwd_opc_column_name" data-bind="i18n: 'Shipping Method'"></div>
        <div tabindex="-1" class="iwd_opc_column_content">
            <div id="iwd_opc_shipping_method"
                 data-bind="iwdBlockLoader:isRatesLoading">
                <div class="fieldset">
                    <form class="form methods-shipping" id="co-shipping-method-form">
                        <!-- ko if: rates().length  -->
                        <div id="checkout-shipping-method-load" class="iwd_opc_universal_wrapper">
                            <div class="field">
                                <div class="control">
                                    <select class="select"
                                            id="iwd_opc_shipping_method_group"
                                            name="iwd_opc_shipping_method_group"
                                            data-validate="{required:true}"
                                            data-bind="
                                        options: shippingRateGroups,
                                        value: shippingRateGroup,
                                        optionsAfterRender:
                                            function(option, item) {
                                                decorateSelect('iwd_opc_shipping_method_group');
                                            },
                                        optionsCaption: shippingRateGroupsCaption
                                    "></select>
                                </div>
                            </div>
                            <div class="field" data-bind="visible: isShippingRatesVisible">
                                <div class="control">
                                    <select class="select"
                                            id="iwd_opc_shipping_method_rates"
                                            name="iwd_opc_shipping_method_rates"
                                            data-validate="{required:true}"
                                            data-bind="
                                        options: shippingRates,
                                        optionsAfterRender:
                                            function(option, item) {
                                                decorateSelect('iwd_opc_shipping_method_rates');
                                            },
                                        value: shippingRate,
                                        optionsText: shippingRateTitle,
                                        optionsValue: function(item){
                                            return item.carrier_code+'_'+item.method_code;
                                        },
                                        optionsCaption: shippingRatesCaption,
                                        event: {change: function(){selectShippingMethod(shippingRate(), shippingRates())}}
                                    "></select>
                                </div>
                            </div>
                        </div>
                        <div id="onepage-checkout-shipping-method-additional-load" class="iwd_opc_universal_wrapper">
                            <!-- ko foreach: getRegion('shippingAdditional') -->
                            <!-- ko template: getTemplate() --><!-- /ko -->
                            <!-- /ko -->
                        </div>
                        <!-- /ko -->
                        <!-- ko ifnot: rates().length > 0 -->
                        <div class="field">
                            <div class="control">
                                <input readonly="readonly"
                                       type="text"
                                       tabindex="-1"
                                       data-validate="{required:true}"
                                       class="input-text"
                                       data-bind="attr: {placeholder: $t('Options Unavailable'), title: $t('Options Unavailable')}"/>
                                <div class="iwd_opc_field_tooltip iwd_opc_shipping_method_tooltip" data-icon="&#xf059">
                                    <div class="iwd_opc_field_tooltip_content">
                                        <!-- ko i18n: 'Make sure all required shipping address fields are completed before choosing a shipping method.' -->
                                        <!-- /ko -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /ko -->
                    </form>
                    <!-- ko foreach: getRegion('before-shipping-method-form') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!-- /ko -->
                </div>
            </div>
            <!-- ko if: isShowDelimiterAfterShippingMethods -->
            <div class="iwd_opc_section_delimiter"></div>
            <!-- /ko --> 
            <!-- ko if: isShowComment -->
            <div class="iwd_opc_universal_wrapper iwd_opc_column_name">Commentaires livraison et facturation</div>
            <div class="fieldset">
                <div class="field">
                    <div class="control">
                                <textarea
                                        rows="1"
                                        data-bind="
                                        value: commentValue,
                                        afterRender: textareaAutoSize,
                                        attr: {
                                            title: $t('Comment'),
                                            placeholder: 'Laissez un commentaire pour la livraison [Ex: Sonnez au N° B42]',
                                            id: 'comment',
                                            name: 'comment'
                                        }"/>
                    </div>
                </div>
            </div>
            <!-- /ko -->
            <!-- ko if: isShowGiftMessage -->
            <!-- ko foreach: getRegion('gift-message') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
            <!-- /ko -->
        </div>
    </div>
</div>
