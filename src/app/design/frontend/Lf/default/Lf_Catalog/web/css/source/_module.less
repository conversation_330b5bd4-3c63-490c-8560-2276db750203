@import '_popin.less';

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
@keyframes spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }
  .overlay {
    visibility: hidden;
    background:@lf-blue;
    opacity:0;
    top:0;
    left:0;
    bottom:0;
    right:0;
    width:100%;
    height:100%;
    position:fixed;
    z-index:-1;
    &.shown {
      visibility: visible;
      opacity:0.5;
      z-index:9;
      transition: 0.2s all ease;
    }
  }


  //
  //  Filter bar
  //  ---------------------------------------------

  .produits {
    text-align: center;

    h2{
      color: @lf-blue;
      font-size: 30px;
      font-family: @lf-font-DinProBlack;
      text-transform: uppercase;
      margin-bottom: 80px;
      margin-top:80px;
      display: block;
      letter-spacing: 5px;
    }

    .filtres{
      height: 60px;
      background:#f7f7f7;
      &.fixednoanim {
        position:fixed;
        width:100%;
        background:#f7f7f7;
        top:0px;
        border-top:92px solid #f7f7f7;
        z-index: 6;
        right: 0;
        box-shadow: 2px -1px 2px 3px rgba(0, 0, 0, 0.2);
        left: 0;

        .container {
          width:100%;
          margin:auto;
          height: 55px;
          max-width: none;
          position: sticky;
          .inside {
            display: inline-block;
          }
        }
         & + .recap_filtres {
                 & + .categorie {
                margin-top:100px;
                transition:all 0.3s ease;
              }
          }
      }



      .container{
        display: inline-block;
      }

      .filtre{
        padding: 22px 17.7px;
        border: 0;
        font-size: 13px;
        font-family: 'DinProBold';
        background:transparent;
        float: left;
        cursor: pointer;
        position: relative;
        z-index: 1;
        &:hover {
          color:white!important;
          background:@lf-blue!important;
          & + .last:before {
            border-left:1px solid transparent!important;
          }
        }
        &.selected {
          color:white!important;
          background:@lf-blue!important;
          &:before {
            border-left:1px solid transparent!important;
          }
          + .filtre:before {
            border:0;
          }
          &:after {
            background:@lf-blue;
            width: 100%;
            opacity: 1!important;
          }
          &:hover:after {
            background:@lf-blue;
            width: 100%;
            opacity: 1!important;
          }
        }
        img {
          width:15px;
          height:15px;
          vertical-align: middle;
          margin-right: 10px;
          margin-top: -4px;

           @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
              width:60px;
              height:60px;
              zoom:0.25;
          }


        }
        &.last:before {
          content: '';
          height: 30px;
          margin-top:-6px;
          width: 1px;
          border-left: 1px solid #003456;
          display: inline-block;
          position: absolute;
          margin-left: -17.5px;
        }

        &.last{
          overflow: visible;

          .f1{
            display: inline;
          }

          .f2{
            display: none;
          }

          &.selected {
            color:white;
            &:after {
              background:@lf-blue;
              width: 100%;
              opacity: 1;
            }
            &:hover {
              .tooltip {
                display:block;
                opacity:1;
                transition: 0.2s all ease;
              }
            }
            &:hover:after {
              background:@lf-blue;
              color:white;
            }
            .f1 {display:none;}
            .f2 {display:inline;}
          }
          .tooltip {
            left: -96px;
            display:none;
            opacity:0;
            margin-top: 17px;
            &:before {
              right: 124px;
            }
          }
        }
      }
    }

    .categorie{
      .container{
        display: inline-block;
        width: 100%;
        max-width: 1500px;
        text-align: left;

        &.okdelivery {
          .produit .addtocart {
            display:block;
          }
        }

        h2 {
          text-align: center;
          margin-bottom:50px;
        }
        .produit {
          display:inline-block;
          height:485px;
          width:24%;
          vertical-align:top;
          margin-left:0.8%;
          border:1px solid transparent;
          position:relative;
          margin-bottom: 6px;
          cursor:pointer;
          margin-top: 30px;
          &.unavailable {
            display:none;
          }

          &.cms{
            align-items: center;
            text-align: left;
            padding-left: 50px;
            width:~"calc(24% - 50px)";
            overflow: visible;
            &:before {
              content:url(../images/bonappetit.png);
              position:absolute;
              display: inline-block;
              margin-left: -100px;
              transform: scale(0.8);
              z-index: -1;
            }
            &:hover {
              border:1px solid transparent;
              width:~"calc(24% - 50px)";
              padding:0;
              margin:0;
              padding-left: 50px;
              margin-left: 0.8%;
              margin-top: 30px;
            }
            h3 {
              width:80%;
              min-width:200px;
              margin-top: 70px;
            }
          }
          &.delayed {
            pointer-events:none!important;
            * {
              pointer-events:none!important;
            }
           .visu, .content {
              opacity:0.7!important;
              filter:grayscale(50%);
            }
            form {
              opacity:0.3;
            }
            &:before {
              content:'Délai de préparation trop long, veuillez changer le créneau horaire si vous souhaitez commander';
              font-size:20px;
              color:@lf-blue;
              display:block;
              font-weight:bold;
              background:white;
              position:absolute;
              margin:30px;
              padding:20px;
              text-align:center;
              opacity:0.9;
              z-index:1;
            }
          }
          &.added, &.added:hover {
            div.visu  {
              background:@lf-blue;
              width:100%;
              position: relative;
              display: block;
              text-align: center;
              cursor:pointer;
              div {
                position: absolute;
                display: inline-block;
                width: 50px;
                text-align: center;
                margin-top: 28%;
                font-family: 'DinProBold';
                font-size: 40px;
                background: white;
                border-radius: 200px;
                opacity: 1;
                margin-left:-25px;

                @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                    margin-left:150px;
                }
                @supports (-ms-ime-align:auto) {
                  margin-left:-25px;
                }
                line-height: 45px;
                transition: 0.2s all ease;
                height: 50px;
                letter-spacing: -1px;
                &.cent:before {
                  margin-top:2px;
                  font-size: 30px;
                  display: block;
                }
                &.mille:before {
                  margin-top:2px;
                  font-size: 24px;
                  display: block;
                }
                &:before {
                  content: attr(data-count);
                  color: @lf-blue;
                }
              }

              img.prod {
                opacity:0.3!important;
              }
            }
          }
          img.prod {
            width:100%;
            display: block;
            height: 100%;
            max-height:270px;
            transition:all 0.3s ease;
            &.lazy {
              opacity:0.3;
              transition:all 0.3s ease;
            }
            &.done {
              opacity:1;
              transition:all 0.3s ease;
            }
          }
          .pic {
            float: right;
            &:hover {
              before {
                opacity:1;
              }
            }
            img{
              width:20px;
              margin-top: 17px;
              margin-right: 12px;
              margin-left: -5px;
            }
          }
          h4 {
            padding-left:5px;
            height:46px;
            font-size: 20px;
            overflow:hidden;
          }
          .details {
            display:none;
          }
          .content {
            position:relative;
            background:white;
            padding-top: 1px;
            height: 235px;
            overflow:hidden;
          }
           label {
              background:@lf-gold;
              color:white;
              font-family: 'DinProBold';
              position:absolute;
              text-transform: uppercase;
              top: -20px;
              max-height: 40px;
              img {
                //max-height: 20px;
              }
            }
          .desc,
          .smalldesc {
            font-family: Eczar, arial;
            color:@lf-blue;
            line-height: 15px;
            padding-left:5px;
          }
          .desc {
            padding-top:5px;
            font-size: 13px;
          }
          .smalldesc {
            font-size: 15px;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 20px;
            line-height: 20px;
            white-space: nowrap;
            width: 95%;
          }
          .no-delivery, .addtocart {
            height:40px;
            background:white;

            img {
              opacity:1;
              width:18px;
              height:19px;
              right:0;
              position:absolute;
              background:@lf-blue;
              cursor:pointer;
              border: 6px solid @lf-blue;
              border-bottom: 5px solid @lf-blue;
              border-radius: 25px;
              padding: 1px;
              background: @lf-blue;
              margin: 0 10px;
            }

            h5 {
              transition:all 0.5s ease;
              visibility: hidden;
              opacity:0;
              padding-top:4px;
            }
          }
          .no-delivery img {opacity:0.5;}
          .no-delivery.loading img  {
             background:white;
              width:23px;
              height:25px;
              animation:spin 1.1s linear infinite;
              background-color: hsla(0,0%,100%,.8);
              border-right: 2px solid @lf-blue;
              border-bottom: 2px solid transparent;
              border-left: 2px solid transparent;
              border-top:0;
          }

          .addtocart {
            font-family:'DinProBold';
            display:none;
            font-size:24px;
            color:white;
            color:@lf-blue;
            vertical-align: middle;
            line-height: 26px;
            height: 40px;
            padding-top: 10px;
           br {
            display:none;
           }

              div.ttc {

                  opacity: 1;
                  transition: all 0.5s ease;
                  z-index: 4;
                  font-size: 16px;
                  padding-left: 7px;
                  @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                      max-width: 110px;
                  }
              }

              div.ht {

                  opacity: 1;
                  transition: all 0.5s ease;
                  z-index: 4;

                  padding-left: 7px;
                  @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                      max-width: 110px;
                  }
              }

             &.prixbarre {

                 .unit.regular {
                     color: #CC0000;
                 }

                  br {
                    display:block;
                   }
                   .quantity {
                       margin-top: -44px;
                   }
                 div.barre  {
                  color:@lf-blue!important;
                  top: -20px;
                  display:inline-block;
                  zoom: 0.65;
                  -moz-transform:scale(0.65);
                  position: relative;
                  z-index: 1;
                    &:before {
                    background:url(../images/barre.svg);
                    background-repeat: no-repeat;
                    background-size: 120px;
                    background-position: 0px -3px;
                    content:"";
                    display: block;
                    position:absolute;
                    margin-top: 15px;
                    width:90px;
                    height:25px;
                      @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                        margin-top: 10px;
                      }
                      @supports (-ms-ime-align:auto) {
                        margin-top: 10px;
                      }
                    }
                  & + .barre {
                    clear:both;
                  }
                }
                 _:-moz-tree-row(hover), div.barre:before {
                  margin-top: 10px;
                 }
                div.ttc, div.ht {
                  z-index: 1;
                   color:#CC0000;
                    margin-top: -28px;
                }

                 div.ht.barre {
                     top: -36px;
                 }

                 div.ttc.regular {
                     margin-top: -4px;
                 }


                 _:-moz-tree-row(hover), div.ht.barre {
                        top: -15px;
                        margin-left:-17px;
                      }
                    _:-moz-tree-row(hover), div.ttc.barre {
                        top: -15px;
                        margin-left:-45px;
                      }



              }

            div.barre {
              display:none;
            }


              div.ttc span {
                  font-size: 10px;
            }
            div.ht {
              margin-left: 0px;
              padding-left: 5px;
            }

              div.ht {
                  margin-top: -9px;
              }

            span {
                vertical-align: middle;
                zoom:0.6;
                -moz-transform:scale(0.6);
                padding-right: 10px;
                height: 22px;
                display: inline-block;
                line-height: 11px;

                @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                  padding-right: 0px;
                  width:65px;
                }
                @supports (-ms-ime-align:auto) {
                  padding-right: 15px;
                }

              }
            _:-moz-tree-row(hover), span {
                padding-right: 0px;
                margin-left:-7px;
              }
            img {
              opacity:1;
              width:18px;
              height:19px;
              right:0;
              position:absolute;
              background:@lf-blue;
              cursor:pointer;
              border: 6px solid @lf-blue;
              border-bottom: 5px solid @lf-blue;
              border-radius: 25px;
              padding: 1px;
              background: @lf-blue;
              margin: 0 10px;
            }
            .quantity {
              float:right;
              margin-right: -5px;
                margin-top: -44px;
               user-select: none;

              div {
                font-size:36px;
                color:@lf-blue;
                font-family:'DinPro';
                width: 30px;
                height: 36px;
                cursor:pointer;
                display:inline-block;
                text-align: center;
                vertical-align: middle;
                user-select: none;

                &.plus {
                  margin-right: 13px;
                 }
              }
              input {
                border:0;
                width: 34px;
                color:@lf-blue;
                text-align: center;
                font-size: 20px;
                font-family:'DinProBold';
              }
            }


              @-moz-document url-prefix() {
                  .quantity {
                      margin-right: -16px !important;
                  }
              }
          }
          &:hover {
            border:1px solid transparent;
            padding:3px;
            margin:-3px;
            margin-top: 27px;
            margin-left:~"calc(0.8% - 3px)";
            z-index: 4;

           div.visu  {
              width:100%;
              position: relative;
              display: block;
              text-align: center;
              cursor:pointer;

              div {
                position: absolute;
                display: inline-block;
                width: 50px;
                text-align: center;
                margin-top: 28%;
                font-family: 'DinPro';
                font-size: 40px;
                background: white;
                border-radius: 200px;
                opacity: 1;
                margin-left:-25px;
                @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
                    margin-left:150px;
                }
                @supports (-ms-ime-align:auto) {
                  margin-left:-25px;
                }
                line-height: 40px;
                transition:none!important;
                height: 50px;
                letter-spacing: -1px;
                &:not(.hovered) {
                   transform:none!important;
                }
                &:not(.hovered):before {
                  content: '+'!important;
                  color: white!important;
                  display: inline-block;
                  font-family: 'DinPro'!important;
                  width: 44px;
                  font-size: 46px!important;
                  height: 44px;
                  background: @lf-blue;
                  border-radius: 30px;
                  margin-top: 3px;
                  margin-left: 0;
                  text-align: center;
                  line-height: 35px;

                }
                &:not(.hovered):after {
                  display: inline-block;
                  width: 3px;
                  height: 30px;
                  background: white;
                  content:'';
                  transform: rotate(-50deg);
                  position: absolute;
                  margin-left: 4px;
                  margin-top: 34px;
                }
              }
            }
            label {
              top: -17px;
               max-height: 40px;
            }

            .no-delivery {
              cursor:pointer;
              h5 {
                opacity:1;
                visibility: visible;
                color:@lf-blue;
                line-height: 1;
              }
              img {
                opacity:0;
              }
            }
          }
          hr {
            background-color:@lf-blue;
            height:1px;
            border:0;
          }
        }
      }
    }
  }

  .tooltip, .recap_filtres {
    font-family: 'DinProBold';
    h5 {
      text-align:left!important;
      padding:10px;
      font-size:12px;
      font-family: 'DinProBold';
    }
    ul {
      padding:5px;
      padding-left:15px;
      margin:0;
    }
    &.tooltipsuppr {
      left: 290px;
      width: 440px;
      margin: 2px;
    }
    .item {

      img {
        width: 22px;
        vertical-align: middle;
        margin-right: 7px;
        margin-top: -5px;

         @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
            width:60px;
            height:60px;
            zoom:0.4;
          }

      }
    }
    div {
      &.alert {
        background:@lf-blue;
        position: relative;
        margin: -2px;
        padding: 7px;
        color:white;
      }
      &.part {
        background:white;
        position: relative;
        margin-top: -2px;
        padding: 7px;
        text-transform: uppercase;
        text-transform: uppercase;
        &[value="0"] {
          font-size:13px;
          color:#999;
          margin-top: -2.5px;
          text-transform: initial;
          &:hover {
            background:white;
            cursor:initial;
          }
        }
        span {
          font-size:12px;
          color:#999;
          float:right;
          display:inline-block;
          text-transform: initial;
        }
        &:hover  {
          cursor:pointer;
          background:#eee;
        }
      }
    }
  }

  .recap_filtres {
    padding: 10px;
    text-align: center;
    .item {
      display: inline-block;
      padding: 4px 8px;
      cursor: pointer;
      background: #F4F4F4;
      margin: 0 2px;
      font-size: 15px;
      letter-spacing: 2px;
      &:hover:after {
       content: '';
        position: absolute;
        display: block;
        width: 14px;
        height: 14px;
        background-position: right -1px center;
        background-size: 16px;
        background-repeat: no-repeat;
        background-image: url(../images/cross.svg) !important;
        margin-top: -22px;
        background-color: @lf-gold;
        border-radius: 8px;
        border: 4px solid transparent;
      }
    }
  }

  .pac-container:after {
    background-image: none !important; height: 0px;
  }

  .button{
    width: 100px;
    font-size: 11px;
    font-family: DinProBold, arial;
    color: #003456;
    display: inline-block;
    padding: 8px 20px;
    border: 1px solid #003456;
    text-align: center;
    cursor: pointer;
  }

  .produit {
    .produit-spinner {
      right: 16px;
      position: absolute;
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {

}
