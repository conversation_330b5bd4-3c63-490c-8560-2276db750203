//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .productlayer {
      max-width: 701px;
    &.crosssell {
       max-width: 1104px;
    }


  }
  .productlayer .modal-inner-wrap {
      font-family: 'DinProBlack';
    max-width:701px;
    margin:0;
    width:auto;
    height:100%;
    .modal-header {
      z-index: 3;
      position:relative;
    }
    &.crosssell {
        max-width: 1104px;
      .tocart-form-container form + form {
        display:none;
      }
    }
    .action-close {
      position: absolute;
      background: @lf-blue;
      color:white;
      width: 40px;
      height: 40px;
      left: 640px;
      top: 10px;
      cursor:pointer;
      border-radius: 40px;
      padding:0;
      img {
        width:30px;
        margin:10px;
      }
      &:before {
        color:white;
        font-size: 41px;
        line-height:42px;
      }
    }

      .gzoom {
      width:700px;
          height: 525px;
      img {
        width: 100%;
      }
      .slides {
        width: 700px;
        height: 525px;
      }

          div + div {
              z-index: 4;
      }
    }


    .modal-content {
      padding: 0;
      margin-top: -4rem;
      z-index:2;
    }
    .produit {
        display: inline-block;
      width: 692px;
        vertical-align: top;
        border: 1px solid transparent;
      height: ~"calc(100% - 10px)";
      overflow-y: auto;
      overflow-x: hidden;
      position:absolute;
        padding-right: 3px;

      ::-webkit-scrollbar {
          width: 6px;
      }

      /* Track */

        ::-webkit-scrollbar-track {

      }

      /* Handle */
      ::-webkit-scrollbar-thumb {
          background: @lf-blue;
          border-radius: 10px;
      }


      @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
         .tocart-form-container,
         .no-delivery  {
           max-width:420px;
         }
      }

        .pic.tt {
            &:after {
                top: 30px;
        }

            &:before {
                top: 36px;
            }
      }

      &.cms{
        align-items: center;
        text-align: left;
        padding-left: 50px;
        width:~"calc(24% - 50px)";
        overflow: visible;
        &:before {
          content:url('../images/bonappetit.png');
          position:absolute;
          display: inline-block;
          margin-left: -100px;
          transform: scale(0.8);
          z-index: -1;
        }
        h3 {
          width:80%;
          min-width:200px;
          margin-top: 70px;
        }
      }
      &.added {
        div.visu  {
          background:@lf-blue;
          width:100%;
          position: relative;
          display: block;
          text-align: center;
          cursor:pointer;
          div {
            position: absolute;
            display: inline-block;
            width: 50px;
            text-align: center;
            margin-top: 30%;
            font-family: 'DinProBold';
            font-size: 40px;
            background: white;
            border-radius: 200px;
            opacity: 1;
            margin-left:-25px;
            line-height: 45px;
            transition: 0.2s all ease;
            height: 50px;
            letter-spacing: -1px;
            &:before {
              content: attr(data-count);
              color: @lf-blue;
            }
          }

          img.prod {
            opacity:0.3!important;
          }
        }
      }
      img.prod {
        width:100%;
        display: block;
      }
      .pic {
        float: right;
        img{
          width:30px;
          margin-top:0px;
          margin-right: 12px;
          margin-left: -5px;
        }
      }

        h4 {
        font-size: 28px;
        display: inline-block;
        margin-top: -5px;
        height: 65px;
        max-width:450px;
        overflow:hidden;
      }
      .content {
        position:relative;
          padding-top: 1px;
          clear: both;
        padding-right: 30px;
        margin-left: 30px;
          margin-top: 30px;
        label {
          background:@lf-gold;
          color:white;
          font-family: 'DinProBold';
          position:fixed;
          transform: rotate(-10deg);
          padding: 3px 10px;
          top: 80px;
          z-index:12;
        }
      }
      .desc, .smalldesc {
        font-family: Eczar, arial;
        color:@lf-blue;
        font-size:16px;
          line-height: 20px;
      }
      .desc {
      margin-top:25px;
      }
      .smalldesc {
        font-size: 18px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 20px;
        line-height: 20px;
        white-space: nowrap;
        width: 95%;
        margin-bottom:25px;
      }
      .details {
        font-family: Eczar, arial;
        margin-top:20px;
          color: @lf-blue;
        h5 {
          font-size: 14px;
          text-align:left;
            margin-bottom: 0px;
        }
      }
       .details2 {
        font-family: Eczar, arial;
        margin-top:20px;
           color: @lf-blue;
        display:inline-block;
        vertical-align:bottom;
        font-size: 20px;
        margin-right:20px;
        text-align:center;
        margin-bottom: 20px;
        h6 {
          font-size: 12px;
          display:block;
          margin-bottom:0px;
          font-family: 'DinProBlack';
          text-transform:uppercase;
        }
      }
      .no-delivery, .addtocart {
        height:32px;
          zoom: 1.5;
        img {
          opacity:1;
          width:18px;
          height:19px;
          right:0;
          position:absolute;
            background: @lf-blue;
          cursor:pointer;
          border: 6px solid @lf-blue;
          border-bottom: 5px solid @lf-blue;
          border-radius: 25px;
          padding: 1px;
          background: @lf-blue;
            margin: 0 30px;
        }

        h5 {
          transition:all 0.5s ease;
          visibility: hidden;
          max-width: 300px;
            margin: auto;
            padding-top: 4px;
        }
      }
      .addtocart {
        font-family:'DinProBold';
        font-size:17px;
        color:white;
        color:@lf-blue;
        vertical-align: middle;
        line-height: 26px;
        padding-left:5px;
        margin-top: 5px;
        div.ttc, div.ht {
          float:left;
          margin-right: 10px;
          padding-right: 10px;
          transition:all 0.5s ease;
          height: 21px;
          line-height: 10px;
          margin-top: -20px;
          font-size: 17px;
          &.selected {
            opacity:1;
            transition:all 0.5s ease;
          }
        }

        &.prixbarre {


            br {
              display:block;
             }
           div.barre  {
               color: @lf-blue !important;
            display:inline-block;
               zoom: 0.7;
            -moz-transform:scale(0.55);
            position: relative;
            z-index: 1;
             top: -4px!important;
               &:before {
                    background:url(../images/barre.svg);
                    background-repeat: no-repeat;
                    background-size: 120px;
                    background-position: 0px -3px;
                    content:"";
                    display: block;
                    position:absolute;
                    margin-top: 2px;
                    width:90px;
                    height:25px;
                    }
          }
          .quantity{
            margin-top: -27px!important;
          }
          div.ttc, div.ht {
           top: -12px;
           height: 10px;
           margin-top:0;
            position: relative;
              color: #CC0000;
          }

            @-moz-document url-prefix() {
            div.ttc, div.ht {
               top: 8px;
             }
             div.ht.barre {
                  top: -15px;
                  margin-left:-17px;
                }
                div.ttc.barre {
                  top: -15px;
                  margin-left:-45px;
                }

          }


           div.ht {
         clear:both;
        }

        }

      div.barre {
        display:none;
      }



        div.ht {
          border-right: 1px solid @lf-blue;
        }

          @-moz-document url-prefix() {
            div.ht {
              border-right: 2px solid @lf-blue;
            }
          }


          .unit.regular {
              display: none;
          }

        span {
          vertical-align: super;
          font-size:13px;
          &.tax {
            width: 30px;
            margin-left: 3px;
            width: 30px;
            display: inline-block;
            font-size:11px;
            position: absolute;
            left: 50px;
            color:#999;
            height: 19px;
            z-index: 4;
            top: 0;
            cursor:pointer;
            &:hover {
              text-decoration:underline;
            }
            &.selected {
              text-decoration:none!important;
              color:@lf-blue;
              z-index: 3;
            }
            & + .tax {
              top: 12px;
            }
          }
        }
        img {
          opacity:1;
          width:19px;
          right:0;
            position: absolute;
            cursor: pointer;
        }
        .quantity {
          float:right;
          margin-right: 10px;
          margin-top: -28px;
          user-select: none;
          div {
            font-size:36px;
            color:@lf-blue;
            font-family:'DinPro';
            width: 30px;
            height: 36px;
            cursor:pointer;
            display:inline-block;
            text-align: center;
            vertical-align: middle;
          }
          input {
            border:0;
            width: 40px;
            color:@lf-blue;
            text-align: center;
            font-size: 17px;
            font-family:'DinProBold';
          }
        }
      }
      &:hover {
        .no-delivery {
          cursor:pointer;
          h5 {
            opacity:1;
            visibility: visible;
            color:@lf-blue;
          }
          img {
            opacity:0;
          }
        }
      }
      hr {
        background-color:@lf-blue;
        height:1px;
        border:0;
      }

        @-moz-document url-prefix() {
            div.ttc, div.ht {
               top: 8px;
             }
             div.ht.barre {
                  top: -15px;
                  margin-left:-17px;
                }
                div.ttc.barre {
                  top: -15px;
                  margin-left:-45px;
                }

          }

    }

      ::-webkit-scrollbar {
          width: 6px;
      }

      /* Track */

      ::-webkit-scrollbar-track {

      }

      /* Handle */
      ::-webkit-scrollbar-thumb {
          background: @lf-blue;
          border-radius: 10px;
      }

    .cross {
      background:white;
      width: 380px;
        position: absolute;
      border-left: 1px solid @lf-blue;
      height: ~"calc(100% - 3px)";
      right: 3px;
      overflow-y:auto;
      overflow-x:hidden;
      padding-left:20px;
      z-index:10;
      h3 {
        margin:25px 0;
        font-size: 20px;
      }

      .sell {
        padding: 0px 20px 0 0;
        .produit {
          width: 360px;
          position: initial!important;
            margin: 0 !important;
          padding-top: 0;
          border-right: 0;

           @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
              position: relative!important;
              top: auto!important;
          }

          .no-delivery {
            zoom:1;
            -moz-transform:scale(1);
            height: 45px;
            h5 {
              font-size:11px;
              margin-top: -6px;
            }
          }

          .no-delivery img {
            margin:10px;
          }

            .addtocart {
            zoom: 1;
            -moz-transform:scale(1);
            margin-top: -8px;
            background: white;
            height: 50px;
            margin-bottom: -8px;
            padding-top: 20px;

            img {
              margin:0 10px;
            }
          }
          &:hover {
            .visu img {
              opacity:1!important;
            }

            .addtocart {

              padding-top: 10px;
              opacity:1;
               margin-bottom: -8px;
            }
            .content h5 {
              margin-top: -20px;
              padding-top: 12px;
              line-height: 14px;
            }
            hr {
              border-bottom: 10px solid white;
            }

            .addtocart + hr { border-bottom:0px; }

            & + .produit {
              margin-top: -10px!important;
            }
          }

            .content {
            font-size: 28px;
            padding-right: 5px;
            margin-left: 0px;
            margin-top: 0px;
            h5 {
              margin-top: -10px;
              padding-top: 7px;
              height: 31px;
              transition: opacity 0.5s ease;
            }
            h4 {
              font-size: 16px;
              background:white;
              padding-right: 5px;
              height:35px;
              margin-top:10px;
            }
            .smalldesc  {
              font-size:14px;
              margin-bottom:10px;
              padding-right: 5px;
            }
            label, .desc, .details{
              display:none;
            }
          }
        }
      }
    }

  }
}


.modals-overlay {
    background-color: rgba(0, 53, 87, 0.8);
}

.jssorb051 {display:block;position:absolute;cursor:pointer;margin-top:515px!important;}
.jssorb051 .i {position:relative }
.jssorb051 .i .b {fill:#ccd5dc;}
.jssorb051 .iav .b {fill:@lf-blue;}

.jssorb051 .i.idn {
    opacity: .3;
}


//
//  Mobile
//  _____________________________________________

@supports (-webkit-overflow-scrolling: touch) {
  @media only screen and (max-width: 1000px) {
      .modal-popup.modal-slide.productlayer .produit h4 {
        font-size: 24px!important;
          height: 80px !important;
      }

      .modal-popup.modal-slide.productlayer .produit .smalldesc {
        height:30px!important;
          line-height: 30px !important;
      }
  }
}


@media only screen and (max-width: 1000px) {

   .modal-popup.modal-slide {
    left:0!important;


       &.productlayer {
    .gzoom {
      margin:0;
      width:unset;
      height: unset!important;
        max-height: 300px;
      div {
        width:100%;
      }
      div + div {
        top:0!important;
        z-index: 4;
      }
      img.big, img.vignette {
        left:0!important;
        top:0!important;
        right:0!important;
        height:auto!important;
          width: 100% !important;
        position: relative!important;
      }
      .slides {
          position: absolute;
          left: 0px;
          top: 0px;
          width: 100% !important;
          overflow: hidden;
          width: unset;

      }
      .jssorb051 {display:block;position:relative;cursor:pointer;left:0!important;}
    }
     .addtocart div.ttc,
     .addtocart div.ht {
          margin-top: 12px;
          zoom: 0.9;
          -moz-transform:scale(0.9);
      }
    .modal-inner-wrap {
      background:white!important;
        .action-close {
        right:13px;
        left:unset;
      }
    }
    .cross {
      display:none;
    }
    .produit {
      margin: 0!important;
        width: 100%;
        zoom: 0.7;
        -moz-transform:scale(0.7);
        position: fixed;
        left: 0px;
        bottom: 0;
        overflow: hidden;
        height: 100%;
         top:0px;
         transition:all 0.2s ease;
         .pic {
          float:left;
          margin-top: 130px;
             margin-bottom: 20px;
         }
        .pic img {
          width:50px;
          margin-left:0;
        }
        .no-delivery {
          clear:both;
        }
        h4 {
          font-size: 32px;
          position: absolute;
          left:0;
          margin-top:0;
          height:70px;
            overflow: hidden;

            & + hr  {
              display: none;
            }
        }
        .details {
          font-size:16px;
          h5 {
            font-size:18px;
            margin-bottom:5px;
          }
        }
        hr {
          clear: both;
        }
        .details2 {
          width: 30%;
          margin-right: 0;
          margin-left: 0;
          h6 {
            font-size: 15px;
            white-space:nowrap;
          }
        }
        .smalldesc {
          line-height: 20px;
            font-size: 20px;
          position: absolute;
          margin-top: 90px;
          height:25px;
            margin-bottom: 20px;

          & + hr {
            display:none;
          }
        }
        .desc {
          font-size: 20px;
          margin-bottom: 30px;
          line-height: 26px;
        }
        .addtocart {
          height:42px;
          clear:both;

          .quantity {
            margin-top: 0;

             div  {
                position: relative;
                z-index: 2;
                margin-left: -10px;
                 margin-right: 5px;
                background:@lf-blue;
                border-radius: 20px;
                color:white;
                  width: 40px;
                height: 40px;
                font-size: 40px;
                line-height: 32px;
                text-align: center;
                &.plus {
                  margin-top: -90px;
                  margin-right: -10px;
                }
                &.moins {
                  margin-right: -30px;
                }
            }


          }

          &.prixbarre {
            height: 37px;
           padding-top: 5px;
          }
            img {
              right:-5px;
              width: 25px;
              height: 26px;
            }

          .quantity input {
           display:none;
          }
        }
        .content {
          max-height: none!important;
          overflow-y:auto;
          position:fixed;
          bottom:5px;
          left:0;
          right:0;
          padding-right: 5px;
          margin-left: 15px;
          margin-top: 20px;
        }
    }

  }
 }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

}

 .modal-popup.modal-slide .modal-inner-wrap { background : white; overflow:hidden;}
