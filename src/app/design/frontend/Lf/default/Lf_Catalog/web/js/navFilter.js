define([
    'require',
    'jquery',
    'Lf_Catalog/js/utils',
    'Lf_Catalog/js/isotope'
], function (require, $, utils, Isotope) {
    'use strict';

    var cms = ", .cms";
    var filterValues = [];
    filterValues.filter1 = "";
    filterValues.filter2 = "";
    filterValues.filter3 = ".cms";

   

    return function (config, element) {
        require(['jquery-bridget/jquery-bridget'],
            function (jQueryBridget) {
                jQueryBridget('isotope', Isotope, $);
 
                $(element).find(".filtre.last").on('mouseover', function () {
                    var _this = $(this);
                    $(this).addClass("selected");
                    setTimeout(function() { $(_this).addClass("clickable");}, 200); 
                }).on('mouseout', function () {
                    var _this = $(this);
                    $(this).removeClass("selected"); 
                    setTimeout(function() { $(_this).removeClass("clickable");}, 200); 
                }).on('click', function () {
                    var _this = $(this);
                    $(this).addClass("selected");
                    setTimeout(function() { $(_this).addClass("clickable");}, 200); 
                });

                   
                if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0,4))) {
                    var isMobile = true;
                 } else {
                     var isMobile = false;
                 } 
               
                var theclick = 'click';
               
                if(navigator.userAgent.match(/iPad/i) != null) {
                    var theclick = 'touchstart';
                }

                //clic sur les filtres
                $(element).on(theclick, '.filtre:not(.last)', function () {

                    if($(this).hasClass("selected")) {
                         $(this).removeClass("selected");
                         $(element).find(".filtres").removeClass("hasSelected");
                         var thisfilter = $(this).attr('data-filter');
                         filterValues.filter1 = filterValues.filter1.replace(thisfilter,'');
                         filterValues.filter3 = cms;
                         var stringFilters = filterValues.filter1 + filterValues.filter2 + filterValues.filter3 ;
                         utils.filterCategories(stringFilters);
                         
                        if(isMobile == true) {
                            window.scrollTo(0,0);
                        } else {
                            window.scrollTo(0,$('.produits').offset().top-50);
                        }

                    } else {

                        $(element).find(".filtre:not(.last)").removeClass("selected");
                        $(this).addClass("selected");
                        $(element).find(".filtres").addClass("hasSelected");
                        var thisfilter = $(this).attr('data-filter');
                        filterValues.filter1 = thisfilter+':not(.unavailable)';
                        filterValues.filter3 = cms;
                        var stringFilters = filterValues.filter1 + filterValues.filter2 + filterValues.filter3 ;
                        utils.filterCategories(stringFilters);
                         if(isMobile == true) {
                            window.scrollTo(0,0);
                        } else {
                            window.scrollTo(0,$('.produits').offset().top-50);
                        }
                    }

                    if(jQuery('.menu').hasClass("open"))
                    {
                        setTimeout( "jQuery('.menu').trigger('click')" , 1000);
                    }

                });

                // clic sur un filtre non selectionné
                $("body").on(theclick, '.last .item:not(.selectedfilter)', function () { 
                    var thisfilter = $(this).attr('data-filter');
                    filterValues.filter2 = filterValues.filter2+':not(.unavailable)' + thisfilter;
                    if(filterValues.hasUnavailableFilter==undefined)
                    {
                        filterValues.filter2 +=':not(.unavailable)';
                        filterValues.hasUnavailableFilter=true;
                    }
                    filterValues.filter3 = cms;
                    var stringFilters = filterValues.filter1 + filterValues.filter2 + filterValues.filter3 ;
                    utils.filterCategories(stringFilters);
                    $(this).addClass("selectedfilter").clone().appendTo(".recap_filtres"); 
                     if(isMobile == true) {
                        window.scrollTo(0,0);
                    } else {
                        window.scrollTo(0,$('.produits').offset().top-50);
                    }
                });

                // clic sur un filtre déja selectionné
                $("body").on(theclick, '.item.selectedfilter', function() {
                    var thisfilter = $(this).attr('data-filter');
                    $(this).removeClass("selectedfilter");
                    filterValues.filter2 = filterValues.filter2.replace(':not(.unavailable)'+thisfilter,'');
                    if(filterValues.filter1 == "" && filterValues.filter2 == ""){
                        filterValues.filter3 = ".cms";
                    } else {
                        filterValues.filter3 = cms;
                    }
                    var stringFilters = filterValues.filter1 + filterValues.filter2 + filterValues.filter3 ;
                    utils.filterCategories(stringFilters);
                    $(".recap_filtres").find(".item[data-filter='"+  thisfilter +"']").remove();
                    $(".filtres").find(".item[data-filter='"+  thisfilter +"']").removeClass("selectedfilter");
                     if(isMobile == true) {
                        window.scrollTo(0,0);
                    } else {
                        window.scrollTo(0,$('.produits').offset().top-50);
                    }
                });

                 $("body").on('touchstart', '.filtre.last.selected', function() {
                    $(".filtres .container").removeClass("selectedfilter");
                 });
                 
            });
    }
});