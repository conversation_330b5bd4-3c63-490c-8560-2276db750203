define([
    'require',
    'jquery',
    'Lf_Catalog/js/isotope'
], function (require, $, Isotope) {
    'use strict';
    return function (config, element) {
        require(['jquery-bridget/jquery-bridget'],
            function (jQueryBridget) {
                jQueryBridget('isotope', Isotope, $);

                $('.categorie-container').isotope({
                    itemSelector: '.produit',
                    layoutMode: 'fitRows',
                    transitionDuration: 0
                });

                // Get all of the images that are marked up to lazy load
                const lazyImages = document.querySelectorAll('img.lazy');
                const lazyConfig = { 
                  rootMargin: '50px 0px',
                  threshold: 0.01
                };

                let lazyImageCount = lazyImages.length; 
                let observer; 
                // If we don't have support for intersection observer, loads the images immediately
                if (!('IntersectionObserver' in window)) {
                  loadImagesImmediately(lazyImages); 
                } else {
                  // It is supported, load the images
                  observer = new IntersectionObserver(onIntersection, lazyConfig); 
                  for (let i = 0; i < lazyImages.length; i++) { 
                    let image = lazyImages[i];
                    if (image.classList.contains('done')) {
                      continue;
                    } 
                    observer.observe(image);
                  }
                }
 
   
                function loadImagesImmediately(lazyImages) {
                  // foreach() is not supported in IE
                  for (let i = 0; i < lazyImages.length; i++) { 
                    let image = lazyImages[i];
                    applyImage(image, image.dataset.src); 
                  }
                }

                /**
                 * Disconnect the observer
                 */
                function disconnect() {
                  if (!observer) {
                    return;
                  } 
                  observer.disconnect();
                }
 
                function onIntersection(entries) {
                  // Disconnect if we've already loaded all of the images


                  // Loop through the entries
                  for (let i = 0; i < entries.length; i++) { 
                    let entry = entries[i];
                    // Are we in viewport?
                    if (entry.intersectionRatio > 0) {
                      lazyImageCount--;

                      // Stop watching and load the image
                      observer.unobserve(entry.target); 
                      applyImage(entry.target, entry.target.dataset.src); 
                    }
                  }
 
                  if (lazyImageCount === 0) {
                    observer.disconnect(); 
                  }
 
                }
 
                function applyImage(img, src) {
                  // Prevent this from being lazy loaded a second time.
                  img.classList.add('done');
                  img.src = src;
                }
                  

            }
        );
    }

});
