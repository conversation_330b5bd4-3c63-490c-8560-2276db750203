//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  .changeAdress {
    max-width:700px;
    overflow:visible!important;
    .action-close {
      padding: 1.1rem 2rem;
      right: -20px;
      top: -18px;
      background: @lf-blue!important;
      color: white;
      width: 40px;
      height: 40px;
      cursor: pointer;
      border-radius: 40px;
      padding: 0;
      z-index: 1;
      &:before {
        color:white!important;
      }
    }
  }

  .shipping-popin {
    text-align: center;
    h3 {
      text-transform:initial;
    }
    p {
      margin:40px auto;
      display:inline-block;
      max-width:380px;
      font-size:16px;
      color:@lf-blue;
      font-family:Eczar;
    }

    button {
      padding: 15px;
      font-size: 13px;
      font-family: 'DinPro';
      width:70px;
      background:#959595;
      color:white;
      margin:3px;
      & + button {
        background:@lf-blue;
      }
    }

  }

    .minicart-wrapper .block-minicart:before {
        right: 26px;
        top: -10px;
    }

    .overlaysmallcart {
        position: absolute;
        width: 100%;
        height: 90px;
        margin-top: -29px;
    }

    .minicart-link {
        color: #ffffff;
        text-transform: uppercase;
        border-top: 1px solid white;
        margin-top: 8px;
        font-size: 16px;
        font-weight: bold;
        padding-top: 4px;
    }

    .minicart-wrapper .action.showcart {
        display: block;
    }

    .smallcart {
        box-sizing: border-box;
        width: 152px;
        padding: 15px 0 0 0;
        display: block;
        height: 91px;
        background: @lf-blue;
        float: right;
        cursor: pointer;
        margin: -1px 0 0 0;
        text-align: center;

        &.visible {
            visibility: visible;
        }

        img {
            width: 20px;
            vertical-align: middle;
        }

        .count {
            height: 20px;
      width:24px;
      border-radius:12px;
      background:white;
      display: inline-block;
      color:@lf-blue;
      text-align: center;
      padding-top:4px;
      font-size: 11px;
      margin-left: 5px;
      margin-right: 5px;
      vertical-align: middle;
      border-right: 1px solid white;
    }
    .total {
      border-left: 1px solid white;
      margin-left: 5px;
      padding-left: 10px;
      font-size:20px;
      color:white;
      display: inline-block;
      vertical-align: middle;

      span {
        vertical-align: super;
        font-size: 10px;
      }
    }
  }

  .minicart-wrapper .action.showcart::before {
    content: '' !important;
  }

  .checkout-cart-index {
    // Block couverts

    .couverts-table {
      border-collapse: separate;
      border-spacing: 0 15px;
    }

      .cart-couverts th, .cart-couverts tbody td, .cart-couverts tbody th :last-child, .cart-couverts tbody td :last-child {
          padding: 5px 0;
          vertical-align: middle;
      }

      .cart-couverts th, .cart-couverts tbody td {
          border-top: 1px solid #d1d1d1 !important;
      }

      .cart-couverts table {
          border-bottom: 1px solid #d1d1d1 !important;
      }

    .cart-couverts-label {
        text-transform: lowercase;
      color: #003456;
      font-size: 12px;
      font-family: 'DinProBold';
      padding-left: 0px;
      padding-right: 0;
      padding-top: 6px;
      max-width: 60px;
    }

      .cart-couverts-label::first-letter {
          text-transform: uppercase;
      }
    .cart-couverts-price {
      font-weight: @font-weight__regular;
      font-size: 12px !important;
      padding-left: 5px;
      padding-right: 0;
      padding-top: 6px !important;
    }

    .moinscouverts, .pluscouverts {
        font-size: 14px;
      color: #003456;
      font-family: 'DinPro';
      cursor: pointer;
      line-height: 1rem;
      padding: 0 10px;
    }

    .quantitycouverts {
      padding: 0;
      text-align: center;
    }

    .cart-couverts-ttc {
      color: #003456;
        font-size: 12px !important;
      text-align: right;
      padding-right: 0;
      padding-left: 0 !important;

      sup {
        font-family: 'DinProBold';
        font-size: 12px !important;
      }
    }

    .qtycouverts {
      border: 0;
      color: #003456;
      text-align: center;
        font-size: 12px;
      font-family: 'DinProBold';
      background: transparent;
      line-height: 1rem;
      width: 30px;
    }



    .couvertsfooter {
      display: flex;
      align-items: center;

      justify-content: space-between;
      padding-left: 0px;
      padding-top: 15px;
      padding-bottom: 15px;

      &.couvertsfooter {
          font-size: 12px !important;
          font-family: DinProLight;
      }
    }

    .imgCover {
      padding-left: 10px;
      width: 35px;
    }
  }

}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .checkout-cart-index {
    .column.main {
      min-height: 900px;
    }

    .cart-couverts-ttc {
      sup {
        font-size: 10px !important;
      }
    }
  }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {

  header .container .delivery .block-minicart {
    margin: -1px 0px !important;
    position: absolute;
      width: 100vw;
  }
    .table-wrapper .table:not(.cart):not(.totals):not(.table-comparison) > tbody > tr td {
        display: flex !important;
    }
    .checkout-cart-index .cart-couverts-price {
        font-family: 'DinProBold';
    }

}

@media only screen and (min-width: 300px) and (max-width: 1000px) {
    .minicart-wrapper .counter-number {
        display: none;
    }

    .minicart-wrapper {
        min-width: 220px;
        height: 52px;
        padding: 0px 0px 0px 0px !important;
    }

    .smallcart .total {
        border-left: 0px;
        border-right: 1px solid white;
        float: left;
        text-align: center;
        padding: 0 10px 0 10px;
        line-height: 50px;
    }

    .minicart-link {
        color: white;
        float: left;
        border: 0px;
        vertical-align: middle;
        text-align: center;
        margin: 0 0 0 10px !important;
        line-height: 45px;
    }
}



