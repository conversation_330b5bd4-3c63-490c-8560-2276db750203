//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .account {
    table.datatable {
      color: @lf-blue;
      margin-bottom: 40px;
      width: 100% !important;

      & > thead > tr > th {
        border: 0 !important;
        font-family: @font-family-name__header;
        font-size: 10px;
        line-height: 16px;
        padding: 20px 10px;
      }

      tbody {
        tr {
          padding: 20px;

          &:nth-child(odd) {
            background-color: #F6F6F6;
          }
        }

        td {
          align-items: center;
          font-family: @lf-font-DinPro;
          font-size: 12px;
          vertical-align: middle;

          &.important {
            font-family: @font-family-name__header;
          }

          &.actions {
            text-align: right;
          }

          a {
            &:not(:first-child) {
              margin-left: 24px;
            }

            span {
              font-family: @font-family-name__header;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

// Desktop
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .account {
    table.datatable {
      tbody {
        tr {
          height: 56px;
        }
      }
    }
  }
}
