//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  /**
   * Devis page panier
   */

  .quotation-button {
    align-items: center;
    background-color: @lf-gold;
    color: @color-white;
    display: flex;
    height: 56px;
    font-size: 1.6rem;
    justify-content: center;
    text-transform: none;
    line-height: 2.2rem;
    padding: 14px 17px;
    width: 100%;

    &:before {
      background: url('@{baseDir}images/mail.svg') no-repeat;
      background-size: 24px;
      content: ' ';
      display: block;
      height: 21px;
      margin-right: 4px;
      width: 24px;
    }

    &:hover, &:active, &:focus {
      background-color: @lf-gold;
      color: @color-white;
    }
  }

  .cart-clear-button {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cart-clear-text {
    color: @lf-blue;
    font-family: @font-family-name__header;
    margin-top: 16px;
    text-align: center;
  }

  .quotation-clear-block {
    padding-top: 32px;
    margin-top: 32px;
    border-top: 1px solid #d1d1d1;
  }

  .quotation-header {
    color: @lf-gold;
    font-family: @font-family-name__header;
    font-size: 1.8rem;
    font-weight: @font-weight__bold;
    margin-top: 32px;
    text-transform: uppercase;
    text-align: left;
  }

  .checkout-quotation-header {
    color: @lf-blue;
    font-family: @font-family-name__header;
    letter-spacing: 3.84px;
    margin-top: 100px;
    text-align: center;
    text-transform: uppercase;
    font-size: 3.2rem;
    padding: 10px;

    span {
      color: @lf-gold;
    }
  }

  /**
   * Table devis compte client
   */

  table.table-devis {
    tr.canceled,
    tr.expired {
      color: #969696;
    }

    .download {
      &:before {
        background: url('@{baseDir}images/download_pdf.svg') no-repeat;
        background-size: 12px;
        content: ' ';
        display: inline-block;
        height: 12px;
        margin-right: 5px;
        width: 12px;
      }

      &.disabled {
        color: #969696;
      }
    }

    .validate {
      &:before {
        background: url('@{baseDir}images/check.svg') no-repeat;
        background-size: 12px;
        content: ' ';
        display: inline-block;
        height: 12px;
        margin-right: 5px;
        width: 12px;
      }
    }

    .modify {
      &:before {
        background: url('@{baseDir}images/small_edit.svg') no-repeat;
        background-size: 12px;
        content: ' ';
        display: inline-block;
        height: 12px;
        margin-right: 5px;
        width: 12px;
      }
    }

    .more-info {
      margin-left: 8px;
      position: relative;

      .more-info-msg {
        border-radius: 10px;
        background-color: @lf-blue;
        color: @color-white;
        display: none;
        font-size: 12px;
        font-weight: @font-weight__bold;
        left: 20px;
        padding: 5px;
        position: absolute;
        text-align: left;
        top: -15px;
        width: 180px;
      }

      &:hover {
        .more-info-msg {
          display: block;
        }
      }
    }

    .quotation-status {
      &:before {
        border-radius: 50%;
        content: ' ';
        display: inline-block;
        height: 8px;
        margin-right: 8px;
        width: 8px;
      }

      &.validated:before {
        background-color: #22C373;
      }

      &.canceled:before,
      &.expired:before {
        background-color: #DD493C;
      }

      &.sent_to_customer:before,
      &.customer_reminder:before,
      &.in_progress:before {
        background-color: #F99F36;
      }

      &:not(.expired) {
        .more-info {
          display: none;
        }
      }
    }
  }

  /**
   * Modal devis
   */

  .popup-devis {
    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 10px 10px 20px;

      .action-close {
        padding: 0;
        position: relative;

        &:before {
          color: @lf-blue;
          font-weight: @font-weight__bold;
        }
      }

      .modal-title {
        font-family: @lf-font-DinProBlack;
        color: @lf-blue;
        font-size: 1.2rem;
        font-weight: @font-weight__bold;
        padding: 0;
        border: 0;
        text-transform: uppercase;
      }
    }

    .modal-content {
      padding: 0;
    }

    .modal-inner-wrap {
      max-width: 500px;
    }

    .devispopin {
      position: relative;
      overflow-x: hidden;

      &:before {
        background: url('@{baseDir}images/lf_gold.png') no-repeat;
        background-size: 80px;
        content: ' ';
        display: block;
        position: absolute;
        top: -10px;
        left: -9px;
        height: 80px;
        width: 80px;
      }

      .password-bubble {
        position: absolute;
        margin-top: 0;
        margin-left: 0;
        top: 48px;

        li {
          white-space: normal;
        }
      }

      .block.devis {
        align-items: center;
        border-top: 1px solid #d1d1d1;
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
        padding: 32px 40px 40px 40px;

        &:before {
          background: url('@{baseDir}images/edit_pencil.png') no-repeat;
          background-size: 56px;
          content: ' ';
          display: block;
          height: 56px;
          width: 56px;
        }
      }

      .devis-popin-desc {
        color: @lf-blue;
        font-family: @lf-font-DinPro;
        margin-bottom: 16px;
        margin-top: 24px;
        width: 100%;
      }

      .form-devis {
        width: 100%;

        .field {
          margin-bottom: 16px;
        }

        .control {
          position: relative;
          width: 100% !important;

          input {
            border: 1px solid #D1D1D1;
            font-family: @lf-font-DinPro;
            height: 48px;
            padding-left: 10px;
            width: 100%;

            .lib-input-placeholder(
              @_input-placeholder-color: #969696,
              @_input-placeholder-font-weight: @font-weight__bold
            );
          }
        }
      }
    }

    .validate-message {
      border-top: 1px solid #d1d1d1;
      color: @lf-blue;
      font-family: @lf-font-DinPro;
      padding: 80px 40px 40px 40px;
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .popup-devis {
    .modal-inner-wrap {
      border-radius: 8px;
    }
  }

  table.table-devis {
    .actions {
      display: flex;
      line-height: 20px;
      align-items: center;
      justify-content: flex-start;
      margin-top: 9px;
    }
  }
}

//
//  Desktop large
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
  .checkout-quotation-header {
    margin-bottom: -45px;
    margin-top: -5px;
    position: absolute;
    width: 135%;
  }

  .devispopin {
    .password-bubble {
      width: ~"calc(100% - 30px)";
    }
  }
}
