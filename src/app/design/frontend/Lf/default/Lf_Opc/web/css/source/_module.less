//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  .onepage-index-index {
    .loading-mask .loader p {
      display: none;
    }
  }

  #checkout textarea#comment {
    margin-bottom: 9px;
  }

  .kr-payment-button {
    display: none !important;
  }

  .billing-same-adress.field {
    font-family: @lf-font-DinPro;
    font-size: 16px;
    margin: 30px 0;
    text-align: left;

    button {
      text-transform: uppercase;
    }
  }
   
  .shipping-address-button-container {
    text-align: left;
    button {
      padding: 15px 40px;
      font-family: 'DinPro';
      font-size: 12px;
      font-family: 'DinProBlack';
      border:2px solid @lf-blue;
    }
  }

  .street {
    .field {
      margin: 0 !important;
    }
  }

  .billing-form-container {
    select.selectized {
      margin: 0;
      padding: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      height: 1px;
      opacity: 0;
      z-index: -1;
    }
  }

  .edenred-limit {
    text-align: left;
  }

  .iwd_opc_option_with_image {
    &[data-value="edenred"] {
      overflow: visible !important;

      .iwd_opc_option_image {
        display: block !important;
        width: 128px !important;
        height: 45px !important;
        position: relative !important;
        float: none !important;
        margin: 10px 0 0 0 !important;
      }
    }

    &[data-value="systempay_standard"] {
      height: 57px !important;

      .iwd_opc_option_image {
        width: auto !important;
        height: 27px !important;
        display: block !important;
        position: relative !important;
        float: none !important;
        margin: 10px 0 0 0 !important;
      }

      &:after {
        left: 4px !important;
        top: -51px !important;
      }
    }
  }

  .iwd_opc_select_container {
    overflow: visible !important;
  }

  .billing-email-container {
    border: 1px solid @lf-blue;
    margin-bottom: 25px;
    padding: 25px 15px 20px 15px;

    &.closed {
      .field {
        display: none;
      }

      .action:after {
        content: @icon-expand;
      }
    }

    &.missing.missing.missing {
      input {
        border: 3px solid @lf-gold!important;
      }
    }

    .action {
      .lib-button-reset();
    }

    .action:after {
      font-family: 'icons-blank-theme';
      font-size: 29px;
      font-weight: bold;
      content: @icon-remove;
    }

    .billing-email-header {
      align-items: center;
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }

    p {
      text-align: left;
      margin-bottom: 0;
    }

    .title {
      color: @lf-blue;
      font-family: @lf-font-DinPro;
      font-weight: @font-weight__bold;
      font-size: 16px;
    }

    .subtitle {
      color: @lf-blue;
      font-size: 12px;
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .iwd_main_wrapper {
    .fieldset {
      .field {
        &[name='billingAddressshared.postcode'],
        &[name='billingAddressshared.firstname'],
        &[name='shippingAddress.postcode'],
        &[name='shippingAddress.firstname'] {
          float: left;
          margin-right: 5px;
          width: 35%;
        }

        &[name='billingAddressshared.lastname'],
        &[name='billingAddressshared.city'],
        &[name='shippingAddress.lastname'],
        &[name='shippingAddress.city']{
          float: left;
          width: ~"calc(100% - 35% - 5px)";
        }
      }
    }
  }
}



@media only screen and (min-width: 300px) and (max-width: 1000px) {

.iwd_opc_option_with_image {
    
    &[data-value="systempay_standard"] {
      
      &:after { 
        top: -70px !important;
      }
    }
  }

}  
