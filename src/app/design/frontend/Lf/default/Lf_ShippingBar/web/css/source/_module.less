@import '_datepicker.less';

//
//  Common
//  _____________________________________________

& when (@media-common = true) {


.pac-container.fixednoanim {
  position:fixed!important;
  top:62px!important;
}

.commande {
  font-size:16px;
  display: inline-block;
  line-height: 26px;
  height:90px;
   width: ~"calc(100% - 170px)";

  .picto {
    width:100px;
    float: left;
    margin-right: 30px;
    margin-top: -14px;
  }
  b {
    display: inline-block;
    min-width: 120px;
  }
  #_place:after {
   right: -7px;
  }
  .grey.blue {
    cursor: pointer;
    top: 0px;
    display: inline-block;
    position: relative;
    font-size: 15px;
    font-weight: normal;
    font-family: Eczar, arial;
    height: 28px;
    border-left: 1px solid #aaa;
    padding-top: 5px;
    padding-left: 19px;
    width:66%;
    max-width: 330px;
    vertical-align: middle;
    height: auto;
    &:after {
      background:url(../images/edit.svg);
      content:'';
      width: 15px;
      background-size: 15px;
      height: 15px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: ~"calc(50% - 7px)";
    }

    & + .question {
      border-left: 1px solid #aaa;
      height: 60px;
      vertical-align: middle;
      margin-top: 0;
      padding-top: 31PX;
      padding-bottom: 0px;
      margin-left: 20px;
      & + .grey.blue {
         width:66%;
         max-width: 420px;
      }
    }
  }
}

  header {
    .container {

      &.okdelivery {
         .step1 {
          display:none;
         }
         .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
          margin-top: -87px;
          .commande {
              display: none;
            }
          }
      }

      .delivery {
        width:1200px;
        background:white;
        color:@lf-blue;
        font-family:'DinProBold';
        font-size:14px;
        margin:auto;
        height:90px;
        position:relative;
        margin-top:220px;
        margin-bottom:70px;
        //transition:all 0.5s ease;
        z-index:10;

        .loader {
          height: 60px;
          text-align: center;
          margin-bottom: -90px;
          padding-top: 30px;
          opacity:1;
          background: white;
          transition: all 1s ease;
          position: relative;
          z-index: 1;

            div {
              display: inline-block;
              position: relative;
              width: 64px;
              height: 64px;
               div {
                  box-sizing: border-box;
                  display: block;
                  position: absolute;
                  width: 51px;
                  height: 51px;
                  margin: -10px 0;
                  border: 6px solid @lf-gold;
                  border-radius: 50%;
                  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
                  border-color: @lf-gold transparent transparent transparent;
                }
                div:nth-child(1) {
                animation-delay: -0.45s;
              }
               div:nth-child(2) {
                  animation-delay: -0.3s;
                }
               div:nth-child(3) {
                  animation-delay: -0.15s;
                }
            }

            @keyframes lds-ring {
              0% {
                transform: rotate(0deg);
              }
              100% {
                transform: rotate(360deg);
              }
            }

          &.hidden {
            opacity:0;
            z-index: 0;
            pointer-events:none;
          }
        }
        .txt {
          margin-top: 11px;
          display: block;
          margin-left: 652px;
          position: absolute;
        }
        .tooltip {
        }
        .moment {
          background: white;
          position: relative;
          margin-top: -2px;
          padding: 5px;
          text-transform: uppercase;
        }
        .resetPopin {
          position:absolute;
          z-index: 400;
          right: 263px;
          top: 91px;
          background: #003456;
          color: white;
          width: 470px;
          font-size: 12px;
          text-align: center;
          line-height: 14px;
          &:before {
            border: 6px solid;
            border-color: transparent transparent @lf-blue transparent;
            z-index: 99;
            border-bottom-style: solid;
            content: '';
            display: block;
            height: 0;
            position: absolute;
            width: 0;
            margin-top: -12px;
            right: 20px;
        }
          span {
            display: inline-block;
            width: 90%;
            margin-top: 30px;
          }
          .btn {
            margin:25px 40px 25px 10px;
            font-size:11px;
            padding:10px 25px;
            vertical-align: super;
          }
          .close {
            position: absolute;
            right: 10px;
            top: 10px;
            color: white;
            background: none;
            padding: 0;
            margin: -5px;
            border: 0;
            &:before {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-size: 38px;
                line-height: 32px;
                color: inherit;
                content: '\e616';
                font-family: 'icons-blank-theme';
                margin: 0;
                vertical-align: top;
                display: inline-block;
                font-weight: normal;
                overflow: hidden;
                speak: none;
                text-align: center;
            }
          }
          &._place {
            left: 0;
            right:unset;
            width: 465px;
            &:after {

            }
          }
            &._when {
                right: 153px;
            }
        }

        &.fixed {
          transition:all 0.5s ease;
          position:fixed;
          margin-top:0px;
          top:0;
        }
        &.bordered:after {
          border-bottom: 1px solid #85a0b2!important;
        }
        &.fixednoanim {
          position:fixed;
          margin-top:0px;
          top:0;
          right:181px;
          transition:none!important;
          height: 91px;
          background:#f7f7f7;

           .loader {
             background:#f7f7f7;
           }


          .resetPopin {
            top:93px;
            width: 600px;
             &._place {
            left: unset;
            right:586px;
            width: 295px;
            }
              &._when {
                  width: 434px;
              }
           }
          &:after {
            content:'';
            width: 100%;
            position:fixed;
            height: 1px;
            border-bottom: 1px solid #ccc;
            left: 0;
            top: 89px;
          }

          .step3 {
              border-left: 1px solid #aaa;
          }

          .block-minicart {
            margin: -1px 20px;
          }

          .step1 .ok {
            height: 62px;
            padding-top: 28px;
           }

        }
        &.okdelivery {
          margin-top: 113px;

         .commande {
          opacity:1;
         }

          &.fixednoanim, &.fixed  {
            margin-top: 0px;
            .OK {
              height: 68px!important;
              padding-top: 0!important;
            }
          }
          .OK {
            height: 68px!important;
            padding-top: 0!important;
          }
        }
        .button {
          float:right;
          margin-top: -32px;
          margin-right: 7px;
        }
        .tooltip .button {
          margin-top: 6px;
          float:none;
          width:auto;
          &:after {
            line-height: 43px;
          }
        }
        .ok {
          color:white;
          font-family:'DinProBlack';
          letter-spacing:4px;
          text-align: center;
        }
        .check {
            left:700px;
            top:0px;
            background:#FB7576;
            color:white;
            display:none;
            width: 430px;
            z-index: -1;
            pointer-events:none;
            position: absolute;
            font-size: 16px;
            font-family:'Eczar';
            padding:34px 17px 35px;
            padding-left:50px;
            cursor:pointer;
            &.hidden {
              z-index: -1!important;
              pointer-events:all;
              display:none!important;
            }
            &.visible {
              z-index: 1;
              pointer-events:all;
            }
            &:before {
              content:' ';
              position:absolute;
              height:30px;
              background:#FB7576;
              width:30px;
              transform:rotate(45deg);
              display:block;
              left:-14px;
              cursor:pointer;
              top:30px;
            }
        }
        .nok,  .empty {
          color: @lf-gold;
          margin: 23px 15px;
          float: right;
          width: 360px;
          z-index: 1;
          position: relative;
          font-size: 12px;
          span {
            margin-left: -49px;
            float: left;
            margin-top: 7px;
            display: inline-block;
            img {
              width: 25px;
            }
            &:before {
              background: @lf-gold;
              content:"";
              height:32px;
              width:3px;
              display: inline-block;
              transform: rotate(35deg);
              margin-top: -6px;
              margin-left: 11px;
              position: absolute;
            }
          }
        }
        .nok:before {
            border:3px solid @lf-gold;
            content:"";
            height:28px;
            width:28px;
            border-radius:20px;
            display: inline-block;
            float: left;
            margin-right: 20px;
          }
        .empty {
          font-size: 16px;
          line-height: 32px;
        }
        .block-minicart {
          padding: 0px 20px;
          border:1px solid @lf-blue;
          box-shadow: none;
          margin: -2px 15px;
          position: absolute;

          .subtitle {
              width:320px;
              margin:0;
          }
          &:after {
             display:none;
          }

          ::-webkit-scrollbar {
              width: 6px;
          }

          /* Track */
          ::-webkit-scrollbar-track {

          }

          /* Handle */
          ::-webkit-scrollbar-thumb {
              background: @lf-blue;
              border-radius: 10px;
          }

          .minicart-items-wrapper {
            padding:10px;
            margin: 0 -17px 0px -20px;
            min-height: 78px;
            max-height: 350px;
            height: auto!important;
          }
          .minicart-widgets {
            display:none;
          }
          .actions {
            margin:6px -10px;
          }
          .viewcart {
            background:@lf-blue;
            display: block;
            color:white;
            font-family: DinProBold, arial;
            text-transform: uppercase;
            font-size: 15px;
          }
          .product {
            text-align: left;
          }
          .product-item {
            padding:0;
            cursor:default;
          }
          .product-item-details {
            padding:0;
            display: inline-block;
            max-width:235px;
          }
          .product-item-photo {
            display: inline-block;
            float: none;
          }
          .product-item-name {
            text-align: left;
            display: inline-block;
             font-family: DinProBold, arial;
              width: 240px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: middle;
             a {
                color:@lf-blue;
                text-decoration: none;
                &.delete {
                  position:relative;
                  right:-2px;
                  margin-top:7px;
                  transform:scale(0.8);
                  &:before {
                    content: url(../images/bin.svg);
                  }
                }
             }
          }
          .qty   {
            display: inline-block;
            width:32px;
            label:after {
              display:none;
            }
          }
           .qty div {
            display: inline;
          }
         }
        .step1 {
          .ok {
            display:block;
            margin-top:4px;
            width:220px;
            font-size:12px;
             text-transform: uppercase;
            height: 64px;
            margin-top: 0px;
            background:@lf-blue;
            display: inline-block;
            vertical-align: top;
            padding-top:26px;
            float:left;
            &:before {
              background:url(../images/location2.svg);
              background-repeat:no-repeat;
              width:26px;
              height:34px;
              margin-right: 10px;
              background-size: 25px;
              display: inline-block;
              content:'';
              vertical-align: middle;
            }
          }
          &:not(.OK) {
            & + .step2.OK + .step3 {
              margin-top: -87px;
              .commande {
                display: none;
              }
            }
          }
        }
        .step1.OK  {
          width:120px;
          height: 80px;
          background:@lf-blue;
          display: inline-block;
          cursor:pointer;
          vertical-align: top;
          &:after {
            display:block;
            content:'';
            position:absolute;
            width:15px;
            height:15px;
            background:@lf-blue;
            transform:rotate(45deg);
            left: 110px;
            top: ~"calc(50% - 10px)";
          }

          & + .step2 {
            display: inline-block;
            position: relative;
            width:1200px;
            height:auto;
            overflow:visible;

            .choix-livraison {display:inline-block;}
            .ok {
              display:block;
              margin-top:4px;
              width:220px;
              height:63px;
              margin-top: -85px;
              background:@lf-blue;
              display: inline-block;
              vertical-align: top;
              text-transform: uppercase;
              padding-top:28px;
              float:left;
              &:before {
                background:url(../images/clock.svg);
                width:32.5px;
                height:32.5px;
                display: inline-block;
                content:'';
                background-size:33px;
                margin-right: 10px;
                 vertical-align: middle;
                 background-repeat: no-repeat;
              }
            }
            &.OK {
              width:120px;
              height: 69px;
              margin-left:20px;
              background:@lf-blue;
              display: inline-block;
              cursor:pointer;
              &:after {
                display:block;
                content:'';
                position:absolute;
                width:15px;
                height:15px;
                background:@lf-blue;
                transform:rotate(45deg);
                left: 110px;
                top: ~"calc(50% - 10px)";
              }

              & + .step3 {
                display: inline-block;
                margin: -1px 0px;
                position: absolute;
                width:100%;
                height:92px;
                overflow:visible;
                .smallcart {
                  margin-top: 0;
                  opacity:1;
                }
                .commande {
                  opacity:1;
                }
              }
            }
          }
        }

        .step2 {
          width:0px;
          height:0px;
          overflow:hidden;
          transition:all 0.5s ease;

          &.noToday {
            .creneau2 {
              margin-left:423px;
            }
            .creneau3 {
              margin-left:602px;
            }
          }
          &.noTomorrow {
            &.noToday {
              .creneau3 {
                margin-left:408px;
              }
            }
            .creneau3 {
              margin-left:616px;
            }
          }
          .choix-livraison {
            top: 27px;
            position: absolute;
            border-left:1px solid #e5e5e5;
            .tooltip span {
              color:#999;
              cursor:pointer;
              display: inline-block;
              margin-left:5px;
            }
            label {
              cursor:pointer;
             font-family: Eczar, arial;
             font-size:15px;
             &:after {
             float:none;
              content: '';
              display: block;
              border-bottom: 2px solid #003456;
              border-right: 2px solid #003456;
              transform: rotate(45deg);
              border-radius: 2px;
              width: 8px;
              height: 8px;
              margin-left: 15px;
              float: right;
              margin-top: 3px;
             }
            }
            input[type='radio'] {
              margin-left: 100px;
              vertical-align:middle;
              height: 21px;
              width: 21px;
              margin-top: -3px;
              cursor:pointer;
            }
          }
          .question {
            margin-top:12px;
            margin-left:230px;
            margin-right: 30px;
            padding-top: 16px;
          }
          .creneau {
            background:@lf-blue;
            border-radius:3px;
            padding:2px;
            width:240px;
            margin-top:4px;

            &:before {
              display: block;
              content: '';
              position: absolute;
              width: 10px;
              height: 10px;
              background:@lf-blue;
              transform: rotate(45deg);
              margin-left: 14px;
              margin-top:-5px;
            }
          }
          .closed{
              color: @lf-grey;
              text-decoration: line-through;
          }
          .tooltip div.part.closed:hover
          {
                cursor: auto;
                background-color: white;
          }
          .creneau1 {
            margin-left:426px;
          }
          .creneau2 {
            margin-left:633px;
          }
          .creneau3 {
            margin-left:812px;
          }
          #creneaux {
            height:32px;
            z-index: 2;
            position: relative;
            width:240px;
            background:#eee;
            color:@lf-blue;
            font-family: DinPro, arial;
            option:first-child {
              font-family: DinPro, arial;
            }
            option {
              font-family: DinProBold, arial;
            }
          }

          .txt2 {
            font-size:12px;
            color:#999;
            float:right;
            display:inline-block;
            margin-top:-10px;
            & + .txt {
              visibility: hidden;
            }
          }
        }

          .backbutton {
              display:inline-block;
              color:@lf-blue;
              background:url(../images/edit.svg);
              background-repeat: no-repeat;
              background-position-y: bottom;
              text-align: right;
              width: 70px;
              height: 19px;
              padding-top: 5px;
              padding-left: 8px;
              margin-left: 50px;
              font-family: Eczar, arial;
              font-size: 17px;
              font-weight: bold;
              cursor: pointer;
          }
        .question {
          font-size:18px;
          text-transform: uppercase;
          padding:13px 15px 0 33px;
          display:inline-block;
          margin-top:10px;
          vertical-align: bottom;
          height: 57px;
          vertical-align: middle;
          margin-top: 0;
          padding-top: 30px;
          padding-bottom: 0px;
        }
        .adresse {
          border: 0px;
          border-left:1px solid #e5e5e5;
          width: 450px;
          height: 32px;
          padding-left: 20px;
          font-family: Eczar, arial;
          color: @lf-blue;
          font-size: 16px;
          margin-top: 29px;
          vertical-align: top;
           &::focus {
            outline:none!important;
           }
        }
      }
    }
  }
}


.pac-item-query {
  color:#aaa;
  & + span {
    font-size: 13px;
    padding-right: 3px;
    color: #aaa;
    display:inline-block;
    &:before {
      content:' - ';
    }
  }
}
.pac-matched {
  color:@lf-blue;
}
.pac-item-selected {
   background:#EEE!important;
    .pac-icon-marker {
    background-position: -1px -161px!important;
  }
}



.oldAddresses {
    width:  384px;
    background: white!important;
    margin: 0px;
    margin-top: -25px;
    margin-left: 308px;
    position:absolute;
    z-index:1;

    div {
    border:1px solid @lf-blue;
    padding:  5px 7px;
     div {
      padding:5px 3px;
      border:0;
      border-bottom:1px solid #CCC;
      cursor: pointer;
      &:hover {
        background:#F2F2F2;
      }
      &:last-child {
         border:0;
      }
     }
 }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {

 header .container .delivery .loader {
    opacity:0.8;
  }

}

@media only screen and (min-width: @screen__l) and (max-width: (@screen__xl - 1)){
    header .container .delivery .fixednoanim .step1 .adresse {
        border: 0px;
        border-left: 1px solid #e5e5e5;
        width: ~"calc(50% - 355px)";
        height: 32px;
        padding-left: 20px;
        font-family: Eczar, arial;
        color: #003456;
        font-size: 16px;
        margin-top: 29px;
        vertical-align: top;
    }

    header .container .delivery .step1 .adresse {
        border: 0px;
        border-left: 1px solid #e5e5e5;
        width: 284px;
        height: 32px;
        padding-left: 20px;
        font-family: Eczar, arial;
        color: #003456;
        font-size: 16px;
        margin-top: 29px;
        vertical-align: top;
    }

    header .container .delivery .nok {
        color: #bda25a;
        margin: 23px 15px;
        float: right;
        width: 338px;
        z-index: 1;
        position: relative;
        font-size: 12px;
    }
}

@media screen and (min-width: 1451px) and (max-width: 1550px) {
    header {
        .container {
            .delivery.fixednoanim {
                .resetPopin._place {
                    width: 434px;
                    right: 636px;
                }
                .resetPopin._when {
                    width: 484px;
                }
            }
        }
    }
}
@media screen and (min-width: 1551px) and (max-width: 1600px) {
    header {
        .container {
            .delivery.fixednoanim {
                .resetPopin._place {
                    width: 464px;
                    right: 706px;
                }
                .resetPopin._when {
                    width: 553px;
                }
            }
        }
    }
}
