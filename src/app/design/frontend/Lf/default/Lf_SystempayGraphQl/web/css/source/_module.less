//
//  Common
//  _____________________________________________

@_input-color: #033456;
@_page-background: @color-white;
@_button-background: #F7CBDD;

& when (@media-common = true) {
  .lf_systempay_graphql-payment-form {
    background: @_page-background;
    display: flex;
    justify-content: center;
    padding-top: 50px;

    #krtoolbar {
      margin-bottom: 80px;
    }

    .alias-title {
      color: @_input-color;
      text-transform: uppercase;
      letter-spacing: 1.2px;
      font-size: 12px;
      margin-bottom: 20px;
      font-weight: bold;
    }

    @-webkit-keyframes kr-bouncedelay {
      0%, 80%, 100% {
        -webkit-transform: scale(0);
      }
      40% {
        -webkit-transform: scale(1);
      }
    }
    @keyframes kr-bouncedelay {
      0%, 80%, 100% {
        transform: scale(0);
      }
      40% {
        transform: scale(1);
      }
    }

    .kr-embedded .kr-spinner {
      box-sizing: border-box;
      display: none;
      margin: 0 auto;
      text-align: center;
      padding-top: 1px;
      height: 22px;
      width: 70px;
    }

    .kr-embedded .kr-spinner > div {
      width: 18px;
      height: 18px;
      background-color: @_input-color;
      border-radius: 100%;
      display: inline-block;
      -webkit-animation: kr-bouncedelay 1.4s infinite ease-in-out both;
      animation: kr-bouncedelay 1.4s infinite ease-in-out both;
    }

    .kr-embedded .kr-spinner .kr-bounce1 {
      -webkit-animation-delay: -0.32s;
      animation-delay: -0.32s;
    }

    .kr-embedded .kr-spinner .kr-bounce2 {
      -webkit-animation-delay: -0.16s;
      animation-delay: -0.16s;
    }

    .kr-button-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: @color-white;
      padding: 10px 20px;
    }

    .kr-payment-button {
      display: block !important;
    }

    .kr-checkbox-input-wrapper {
      width: 100%;
      margin-top: 25px;
    }

    .kr-checkbox {
      align-items: center;
      display: flex;
      justify-content: space-between;
    }

    .kr-embedded .kr-field.kr-checkbox .kr-field-control input:checked ~ .kr-checkbox-control-indicator {
      background: @_page-background;

      &:before {
        background: url('@{baseDir}images/icon_check_box-full.svg') no-repeat;
      }
    }

    .kr-embedded .kr-field.kr-checkbox .kr-checkbox-control-indicator {
      background: @_page-background;
      border: 0;
      height: 16px;
      order: 1;
      width: 16px;

      &:before {
        display: block;
        content: '';
        background: url('@{baseDir}images/icon_check_box-blank.svg') no-repeat;
        height: 24px;
        width: 24px;
      }
    }

    .kr-embedded .kr-field.kr-checkbox .kr-field-control:hover input ~ .kr-checkbox-control-indicator {
      background: @_page-background;
    }

    .kr-embedded .kr-field.kr-checkbox .kr-field-control:hover input:not([disabled]):checked ~ .kr-checkbox-control-indicator {
      background: @_page-background;
    }

    .kr-embedded .kr-field.kr-checkbox .kr-label {
      padding-left: 0;
    }

    .kr-checkbox-label {
      color: @_input-color;
      font-size: 1.6rem;
    }

    .kr-input-field {
      color: @_input-color;
      font-size: 16px !important;
    }

    .cardName {
      background-color: rgba(0, 0, 0, 0);
      border: 0;
      border-bottom: 1px solid @_input-color;
      color: @_input-color;
      font-size: 16px;
      margin-top: 40px;
      padding-bottom: 10px;
      width: 100%;

      .lib-input-placeholder(
        @_input-placeholder-color: #969696
      )
    }

    .kr-embedded[krvue] .kr-pan .kr-field-component,
    .kr-embedded .kr-pan .kr-field-wrapper .kr-icon-wrapper-root,
    .kr-embedded.kr-help-button-inner-field .kr-help-button-wrapper.kr-inner,
    .kr-field-component
    {
      height: 35px;
      border-bottom: 1px solid @_input-color !important;
    }

    .kr-embedded.kr-help-button-inner-field .kr-form-error {
      margin-top: 20px;
      text-align: center;
    }

    .kr-embedded.kr-help-button-inner-field {
      width: 90%;

      .kr-pan {
        width: 100%;
      }

      .kr-row {
        display: flex;
        align-items: center;
        margin: 30px 0 4px;

        .kr-expiry {
          margin-right: 20px;
        }
      }

      .kr-payment-button {
        background-color: @_button-background !important;
        border-radius: 4px;
        border-color:  @_button-background;
        color: @_input-color;
        font-size: 1.6rem;
        font-weight: bold;
        height: 64px;
        opacity: 1;
        width: 100%;
      }
    }
  }

  .lf_systempay_graphql-payment-result {
    background: @_page-background;
  }
}

//
//  Large screens
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .lf_systempay_graphql-payment-form {
    .kr-embedded.kr-help-button-inner-field .kr-form-error {
      width: 400px;
    }

    .kr-embedded.kr-help-button-inner-field {
      width: 400px;
    }
  }

}
