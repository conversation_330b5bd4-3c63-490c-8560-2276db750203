//
//  Theme variables
//  _____________________________________________
@font-family-name__header: 'DinProBold';

//
//  Common
//  _____________________________________________


// blue screen of death

body.blue:before {
    content: '';
    background-color: @lf-blue;
    bottom: 0;
    left: 0;
    z-index: 10;
    position: fixed;
    right: 0;
    top: 0;
    opacity: 0.8;
    pointer-events: none;
}


//
//  Pop-in fullscreen
//  _____________________________________________

body.hidden-pagehub{
  visibility: hidden;
}

#fullpopin {
  text-align: center;
  position:fixed;
  z-index: 100;
  top:0;
  left:0;
  right: 0;
  bottom: 0;
  background:white;

  .logopop {
    width: 270px;
    margin:10px;
    margin-left: 30px!important;
  }

  .left {
    float: left;
    margin-top: 22px;
    font-family: 'DinProBold';
    font-size: 15px;
    color:@lf-blue;
      li {
        display: inline-block;
        margin-bottom: 0;
      }
   }
  .right {
    float: right;
    margin-top: 22px;
    margin-right: 20px;
    font-family: 'DinProBold';
    font-size: 15px;
    color:@lf-blue;
    img {
      height:15px;
      vertical-align: middle;
      margin-top: -3px;
      margin-left: 3px;
    }
  }

  .columns {
    display: flex;
    flex: 1;
    height: ~"calc(100% - 100px)";
    font-family: 'DinProBlack';
    text-transform: uppercase;
    max-width: none;
    .col {
      width: ~"calc(100% / 3)";
      height: ~"calc(100% - 150px)";
      font-size: 30px;
      text-align: center;
      display: flex;
      align-items: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      img {
        opacity: 0;
        transition:all 0.3s ease;
        width: 100%;
        position: relative;
        top: 0;
        left: 0;
        object-fit: cover;
        min-width: 100%;
        min-height: 100%;
      }
      &:hover {
        img { opacity: 1; }
        .texte {
          color:white;
        }
      }
    }
    .texte {
      position: relative;
      z-index: 1;
      width: 100%;
      text-align: center;
      pointer-events: none;
      margin-left:-100%;
    }
    .col1 {
      background:#f5f1ee;
      color:@lf-blue;
    }
    .col2 {
      background:@lf-blue;
      color:white;
    }
    .col3 {
      background:#f7cbdd;
      color:@lf-blue;
    }
    .sub {

      background: white;
      width: 400px;
      height: 150px;
      position: fixed;
      bottom: 0px;
      color: @lf-blue;
      font-family: Eczar,arial;
      font-size: 22px;
      text-align: center;
      background-repeat: no-repeat;
      background-size: 100%;
      padding-top:170px;
      text-transform: none;
      margin-left: ~"calc((33.33% - 400px) / 2)";
      line-height: 25px;
      z-index: 1;
      &.sub1 {
        background-image: url(../images/cms/sub1.jpg);
      }
      &.sub2 {
        background-image: url(../images/cms/sub2.jpg);
      }
      &.sub3 {
        background-image: url(../images/cms/sub3.jpg);
      }
    }
  }
}

.iwd_main_wrapper .field:not(.choice) .kr-label label {
  display: inline-block;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
     #fullpopin .columns .col {
       width: ~"calc(100% / 3 - 0.1px)";
    }
    #fullpopin .columns .sub {
      margin-left: ~"calc((-33.33% - 400px) / 2)!important";
    }
}

 @media only screen and (min-width: 300px) and (max-width: @screen__ipad) {

.handheld {
  #fullpopin {
    .columns {
      display: flex;
      flex: 1;
      height: unset;
        .col {
          width: 100%;
          height: ~"calc((100vh - 220px)/3)";
          font-size: 15px;
          overflow: hidden;
          position: relative;
        }
        .sub {
          width: ~"calc((100vh - 220px)/3)";
          height: 55%;
          position: absolute;
          bottom: unset;
          margin-left: -6vh;
          transform: rotate(-90deg);
          color: transparent;
          font-size: 10px;
          padding-top: 0;
          line-height:32px;
        }
        .texte {
        padding-left: 30px;
      }
    }


    .logopop {
      width: 190px;
    }
    .left {
      position: absolute;
      bottom: 10px;
      width: 100%;
      margin-left: -40px;
      margin-bottom: 0;
      font-size: 12px;
    }
    .right {
      position: absolute;
      bottom: 50px;
      font-size: 12px;
      width: 100%;
    }
  }
}
}


// iphone X
 @media only screen and (device-width: 375px) and (device-height: 812px) {

  .handheld {
    #fullpopin {
      .columns {
          .col {
            height: ~"calc((100vh - 270px)/3)";
          }
          .sub {
            width: ~"calc((100vh - 270px)/3)";
          }
        }
      }
    }
}


// iphone 11 pro
 @media only screen and (device-width: 414px) and (device-height:896px)  {

  .handheld {
    #fullpopin {
      .columns {
          .col {
            height: ~"calc((100vh - 270px)/3)";
          }
          .sub {
            width: ~"calc((100vh - 270px)/3)";
          }
        }
      }
    }
}



//
//  fin Pop-in fullscreen
//  _____________________________________________


  //
  //  Header
  //  ---------------------------------------------

input[type=text]::-ms-clear{
  display: none;
}

#goog_conv_iframe {
  opacity:0;
  visibility:hidden;
}

.customer-account-logoutsuccess .columns p,
.sales-order-history .message.empty.info {
  background:none;
  color:@lf-blue;
  font-size: 24px;
  font-family: 'DinProBlack';
  text-transform: uppercase;
  text-align: center;
  margin:0;
  width: 1200px;
  max-width: 100%;
  padding:0;
  letter-spacing: 3px;
  span:before {
    display:none;
  }
}

.hub .modal-inner-wrap {
  margin: 0;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.timeslot-expired-modal,
.creneau-warning-modal {
  .modal-inner-wrap {
    max-width: 900px;
    top:20%;
  }
  .modal-footer {
    padding-bottom: 0;
    text-align: center;
    button {
      margin-top: 120px;
      margin-bottom: 45px;
      padding: 16px 27px;
      text-transform: uppercase;
      color:@lf-blue;
      margin-left: 5px;
      margin-right: 5px;
      vertical-align: unset;
      font-family: 'DinProBold';
      letter-spacing: 2px;
      font-size: 12px;
      &.hidden {
        display: none;
      }
      &.first {
        background:#959595;
        border:1px solid #959595;
        color:white;
      }
    }
  }
  .modal-content {
     font-family: 'DinProBlack';
     font-size: 18px;
     color:@lf-blue;
     text-align: center;
  }
  .timer1:before {
    content:url(../images/timer1.png);
    display: inline-block;
    width: 42px;
    height: 60px;
    margin-bottom: 15px;
   }
   .timer2:before {
    content:url(../images/timer2.png);
    display: inline-block;
    width: 42px;
    height: 60px;
    margin-bottom: 15px;
   }

    .macaron {
        content: url(../images/macaron.png);
        position: absolute;
        top: 0;
        left: 0;
        width: 100px;
    }
  .shipping-warning-body,
  .shipping-expired-body {
    margin-left: -32px;
    width:900px;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 40px;
    margin-bottom: -130px;
    position: relative;
    z-index: 1;
    line-height: 30px;
    color:white;
    height:80px;
    .footnote {
      font-size: 12px;
      font-family: 'DinPro';
    }
    .highlight {
      display: inline-block;
      padding:0 4px;
      background:#bda25b;
    }
  }
  .action-close {
      position: absolute;
      background: @lf-blue!important;
      color:white!important;
      width: 40px;
      height: 40px;
      left: 880px;
      top: -20px;
      cursor:pointer;
      border-radius: 40px;
      padding:0;
      &:hover {
        background:white!important;
        color:@lf-blue!important;
        border:1px solid @lf-blue!important;
      }
      img {
        width:30px;
        margin:10px;
      }
      &:before {
        color:white;
        font-size: 41px;
        line-height:42px;
      }
    }
}
.customer-account-logoutsuccess .columns p {
  width: 100%;
}
.customer-account-login .field.password .control {
  margin-top: 0;
}
.cart-container {
    .checkout-methods-items + .message {
       margin-top: 10px!important;
       background:@lf-gold!important;
       font-size: 12px!important;
       order:5;
    }
   .message {
       padding:10px 40px 10px 20px!important;
       font-size: 10px!important;
       margin-top: -15px!important;
       line-height: 22px!important;
       order:3;
       @media only screen and (min-width: 300px) and (max-width: @screen__ipad) {
       margin-top: 0px!important;
       }
       div:after {
        position:absolute;
        right: 0px;
        top:7px;
       }
   }
}

.modal-popup.shipping-warning-modal,
.modal-popup.checkout-validation-popin,
.modal-popup.cart-stock-popin {
  .modal-content {
    padding-top: 20px;
    text-align: center;
  }
  header{
    background-color: #f7f7f7!important;
  }
  .modal-title {
    border:0;
    color: @lf-blue;
    font-size: 30px;
    font-family: DinProBlack!important;
    text-transform: uppercase;
    line-height: 35px;
  }
  .modal-inner-wrap {
    min-width: 550px!important;
    width: 550px!important;
    overflow: visible;
  }
  .modal-footer {
    min-height: unset;
    padding: 20px!important;
    text-align: center;
  }
  .modal-header {
    padding-bottom: 0.5rem;
    padding-top: 1.2rem!important;
    color: @lf-blue;
    font-size: 30px;
    font-family: 'Futura'!important;
  }

}

  .cart-container,
  .messages {

      .message {
        cursor:pointer;
        &.error {
          background:#fb7576;
          text-align: center;
          font-family: 'DinProBold';
          text-transform: uppercase;
          font-size: 18px;
          color: white;
          padding:30px;
          padding-right: 0;
          letter-spacing: 2px;
        }
        &.warning {
          text-align: center;
          font-family: 'DinProBold';
          text-transform: uppercase;
          font-size: 18px;
          color: white;
          padding:30px;
          background:#fb7576;
          letter-spacing: 2px;
          padding-right: 0;
        }
        &.success {
          background:#87ca9e;
          text-align: center;
          font-family: 'DinProBold';
          text-transform: uppercase;
          font-size: 18px;
          color: white;
          padding:30px;
          letter-spacing: 2px;
          padding-right: 0;
        }
          div:before {
            display:none!important;
          }
          div:after {
            content:'\e616';
            font-family:'icons-blank-theme';
            display: inline-block;
            font-size:40px;
            height: 30px;
            line-height: 30px;
            color: white;
            float: right;
          }
      }
  }






  .adresse,
  .adresse:focus,
  select:focus,
  textarea:focus,
  input:focus {
    box-shadow: none!important;
  }

  input.adresse::placeholder {
  color:#777!important;
}
#password-error {
  display:none!important;
}
.password-bubble {
  display:none;
  background:#f7f7F7;
  margin-top:-10px;
  position:absolute;
  z-index:3;
  margin-left:150px;
  color:@lf-blue;
  letter-spacing: 2px;
  width: 350px;
  padding-top: 10px;
  text-align: left;
  padding-left: 30px;
  p {
    position:relative;
    z-index:4;
    padding:0 15px;
    font-weight:bold;
    font-size: 15px;
  }
  li {
    text-align:left;
    padding:0 20px 0 10px;
    list-style:none;
    font-size: 14px;
    line-height: 15px;
    &:before {
      content:'\e616';
      font-family:'icons-blank-theme';
      display: inline-block;
      font-size:26px;
      height: 20px;
      line-height: 30px;
      position: absolute;
      left: 45px;
      margin-top: -6px;
      color:#fb7576;
    }
  }
  li.okay {
    &:before {
      content:'L';
      display:block;
      width:11px;
      background:#87ca9e;
      color:white;
      height:13px;
      position:absolute;
      margin-left:5px;
      margin-top:-1px;
      z-index:1;
      border-radius: 15px;
      font-size:12px;
      line-height: 13px;
      text-align: center;
      font-weight: bold;
      font-family: arial, monospace;
      transform: rotate(45deg) scaleX(-1);
      padding-top: 2px;
      padding-right: 1px;
      padding-left: 4px;
      padding-bottom:1px;
    }

  }

  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
   .okay:before {
      margin-top:-11px;
      font-size:24px;
    }


  }
}
.smallcart {
    opacity:0;
}
 .form.contact fieldset {
 max-width: 700px;
 margin:auto;
 padding-right: 140px;
 textarea {
  border:0;
  border-bottom:1px solid @lf-blue;
 }
     select {
         border: none;
         border-bottom:1px solid @lf-blue;
         font-family: Eczar,arial;
         background-color: white;
     }
    .control.center {
      text-align:center;
    }
    div.txt {
        font-family: Eczar,arial;
        font-size: 14px;
        color: @lf-blue;
    }
 }

.field.password .control {
  margin-top:-20px;
}

.eye {
  background: url(../images/eye-open.svg) no-repeat;
   width:20px;
   height:20px;
   cursor:pointer;
   transform:scale(1.5);
   float:right;
    top:17px;
   z-index:3;
   position:relative;
   margin-right:10px;
   display:block;
}
.eye.close {
    background: url(../images/eye-close.svg) no-repeat;
}

.commande {
 opacity:0;
}


  #header {
    height: 470px;
    width: 100%;



    .burger, .menu {
      display: none;
    }

     .identified {
      color:transparent!important;
      img {
        display: none;
      }
       &:after    {
              top:0!important;
           color:white!important;
           content:attr(data-content)!important;
           transform:none!important;
           background: @lf-blue!important;
        }
        &.f-20,
        &.f-20:after {
          background: #a3a198!important;
        }
        &.f-30,
        &.f-30:after {
            background: @lf-gold!important;
          }
    }

    .bigtitre {
      position: absolute;
      top:200px;
      pointer-events: none;
      text-align: center;
      margin:auto;
      line-height: 30px;
      left:~"calc(50% - 300px)";
      width:600px;

      &:before {
        font-size:40px;
        color:white;
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 7px;
      }
      &:after {
        display: block;
        margin-top: 10px;
        font-size:20px;
        color:white;
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 2px;
        white-space: pre;
        line-height: 25px;
      }
    }
    .container {
      padding: 30px 10px 0px 10px;
      min-height:60px;
      .links {
        text-align: center;
        position: relative;
        width: 870px;
        margin: auto;
        margin-top: -60px;
        padding-left:70px;
        span a.account {

          &:after {
            line-height:18px;
            height: 65px;
            white-space:pre-line;
            text-align: left;
            padding-top: 25px;
            padding-left:20px;
            padding-right: 120px;
            width:auto;
          }
        }
        .aspan {
          position: fixed;
          right: 0;
          z-index: 9;
          height: 87px;
          top:55px;
          width: 185px;
          height: 90px;
          cursor:pointer;
          &:hover a.account:after {
              text-decoration:underline;
            }
          a.close {
            float:right;
            z-index:33;
            position:relative;
            margin:0;
            padding:0;
            border:0;
            top:30px;
            right:-5px;
            cursor:pointer;
            text-decoration: none;
            &:after {
              content:'\e616';
              font-family:'icons-blank-theme';
              display: inline-block;
              font-size:40px;
              height: 30px;
              line-height: 30px;
            }

            &:hover {
              text-decoration: none;
            }
          }
          &.fixednoanim {
            position: fixed;
            top: 0px!important;
            right: 0px;
            display: block;
            margin:0;
            border-left:1px solid white;
            line-height: 90px;
            &.logged {
              height: 14px!important;
            }
            &:after {
              line-height: 88px;
            }
          }
        }
        a {
          font-family: 'DinProBold';
          color: white;
          font-size: 11px;
          text-transform: uppercase;
          display: inline-block;
          margin-right: 20px;
          padding-right: 20px;
          border-right: 2px solid white;
          letter-spacing:4px;
          line-height: 9px;
          &.devis {
             border-right: 0px;
          }
          &:last-child {
            border: 0;
            background: @lf-blue;
            width: 155px;
            padding:0 15px;
            text-align: center;
            top: 0;
            transition: 0.2s all ease;
            white-space: nowrap;
            right: 0;
            margin: 0;
            z-index: 12;
            font-size: 12px;
            letter-spacing: 2px;
            line-height: 91px;
            height:90px;
            display: block;


            img {
              height: 30px;
              padding:0!important;
              margin-right: 10px;
            }
          }
           &.logged {
              padding:38px 15px;
              height: 15px!important;
            }
           .account {
            height: 90px;
            margin-right: 0px;
            vertical-align: middle;
            padding: 0 15px;
            line-height: 88px;
            &:after {
              color: white;
              content: attr(data-content);
               line-height: 88px;
            }
          }

        }

      }
    }
    .logo {
      width: 300px;
      height:66px;
      transition: none!important;
      cursor: pointer;
      background-image: url(../images/logoa.svg);
      background-size: 100%;
      background-repeat: no-repeat;
      background-color:transparent;
      &.fixednoanim {
        position: fixed;
        top: 0px;
        left: 0px;
        z-index: 9;
        transition: none!important;
        max-width: ~"calc(50% - 480px)";
        width:13%;
        border-left: 5px solid #f7f7f7;
        margin-top: 0;
        height: 90px;
        background-image: url(../images/logob.svg);
        background-position: center;
        background-color: #f7f7f7;
        border-right: 2720px solid #f7f7f7;
      }
    }
  }

  .container {
    max-width: 1900px;
    margin: auto;
  }





 ////// CMS
  .atwork {
    background-image: url(../images/cms/atwork.jpg);
    width:1100px;
    margin:auto;
    background-repeat: no-repeat;
    padding-top: 600px;
    background-size: 100%;
    margin-top:100px;
      h3 {
        margin-bottom: 25px;
      }
      div {
        color:@lf-blue;
        font-family: Eczar, arial;
        display: inline-block;
        width:480px;
        vertical-align: top;
        margin-bottom: 25px;
        & + div {
          margin-left: 133px;
          width: 460px;
        }
      }
      hr {
        visibility: hidden;
      }
      button {
        padding:10px 30px!important;
        font-size:10px;
        font-family:'DinProBold';
        letter-spacing:4px;
        color:@lf-blue!important;
        border:1px solid @lf-blue;
      }
  }
  .delivered {
    margin-top:50px;
    width:1400px;
    text-align: center;
    height:220px;
    margin:auto;
      h3 {
        margin-bottom: 50px;
      }
      .logos1, .logos2 {
        background-image: url(../images/cms/logos1.jpg);
        width:1300px;
        height: 85px;
        background-repeat: no-repeat;
        background-size: 100%;
        opacity:0;
        position:absolute;
        transition: all 0.5s ease;
        margin-left:50px;
        &.active {
          opacity:1;
          transition: all 0.5s ease;
        }
        &:before,  &:after {
        content: '';
        border-bottom: 2px solid #003456;
        border-right: 2px solid #003456;
        transform: rotate(135deg);
        width: 15px;
        height: 15px;
        display: block;
        float: right;
        margin-top: 5px;
        position: absolute;
        top: 30px;
        left: -30px;
        }
        &:after {
          transform: rotate(-45deg);
          right:-30px;
          left:unset;
        }
      }

  }

  .closest {
     width:340px;
     height:370px;
      background-image: url(../images/cms/map.jpg);
       background-repeat: no-repeat;
        background-size: 944px;
        background-color:#eaeaea;
        padding-left: 1000px;
        padding-right: 60px;
        margin-bottom: 50px;
        margin:auto;
        h4 {
          width: 210px;
          margin: auto;
          text-align: center;
          padding: 60px;
          font-size: 20px;
          font-family: 'DinProBlack';
          letter-spacing: 4px;
        }
        input[type='text'] {
         float: left;
          width: 290px;
          height: 50px;
          padding-left: 20px;
        }
        input[type='submit'] {
          background:@lf-blue;
          height: 50px;
          width: 50px;
           font-family:'DinProBold';
           color:white;
           font-size: 14px;
           border:0px;
           text-align: center;
           float: right;
        }
  }

.bluewave {
  height:200px;
  background:@lf-blue;
  margin-top: -150px;
  width: 100%;
  position: absolute;
  left: 0;
  z-index: -1;
}


.breadcrumbs {
  display: none;
}

.cms-nos-restaurants {
    .page-title-wrapper {
      opacity:0;
    }
    .content {
      width:1450px;
      height:800px;
      margin:auto;
    }
 ::-webkit-scrollbar {
          width: 6px;
      }

      /* Track */
      ::-webkit-scrollbar-track {

      }

      /* Handle */
      ::-webkit-scrollbar-thumb {
          background: white;
          border-radius: 10px;
      }
    .restos {
      width:440px;
      height:765px;
      padding:20px;
      padding-top:8px;
      padding-right:7px;
      border-top:7px solid #eaeaea;
      border-right:7px solid #eaeaea;
      background:#eaeaea;
      overflow-y:scroll;
      overflow-x:hidden;
      float:left;
      ::-webkit-scrollbar {
          width: 6px;
      }

      /* Track */
      ::-webkit-scrollbar-track {

      }

      /* Handle */
      ::-webkit-scrollbar-thumb {
          background: white;
          border-radius: 10px;
      }



    }
   #header {
     background: white url(../images/cms/bgresto.jpg) no-repeat top center / cover!important;
      .bigtitre {
      top:216px;

      &:before {
        font-size:35px;
        color:white;
        content:'Retrouvez la famille';
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 7px;
      }
      &:after {
        display: none;
      }
    }
   }
    .mapr {
        width:960px;
        height:800px;
        float:left;
      }
   .page-bottom {
    display:none
   }
      .gm-style .gm-style-iw,
      .item {
        padding:20px;
        padding-top:12px;
        background:white;
        margin-bottom:8px;
        font-size:15px;
        font-family:Eczar;
        color:@lf-blue;
        max-width:395px;
      }
      .gm-style .gm-style-iw {
      padding:0px!important;
    }
}


.bloc_bleu {
   clear: both;
    background: @lf-blue;
    color:white;
    padding: 40px;
    width:auto;
    max-width: 800px;
    margin: auto;
    font-family:Eczar;
    h3 {
      color:white;
      font-size: 24px;
    }
   }

.cms-le-concept {

   #header {
     background: white url(../images/cms/bgconcept.jpg) no-repeat center center / cover!important;
      .bigtitre {
      top:216px;

      &:before {
        font-size:35px;
        color:white;
        content:'La famille cuisine chaque jour pour vous';
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 7px;
      }
      &:after {
        display: none;
      }
    }
   }

   .columns {
    max-width: 1680px;
    margin:auto;
    text-align: center;
   }
   h3 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 30px;
    letter-spacing:2px;
   }
   h2 {
    visibility: hidden!important;
   }
   .delivered, .atwork {
    display:none;
   }
}


.cms-comment-ca-marche {

   #header {
     background: white url(../images/cms/bgcomment.jpg) no-repeat center center / cover!important;
      .bigtitre {
      top:216px;
      &:before {
        font-size:35px;
        color:white;
        content:'Comment ça marche ?';
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 7px;
      }
      &:after {
        display: none;
      }
    }
   }
h2 {
    visibility: hidden!important;
   }
   .columns {
    max-width: 1680px;
    margin:auto;
    text-align: center;

   h3 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 36px;
    letter-spacing:2px;
   }

   .delivered, .atwork {
    display:none;
   }
   .bloc {
      font-family: Eczar;
      font-size: 16px;
      color:@lf-blue;
      h3 {
          font-size: 20px;
      }
   }
   .b1 {

   }
   .b2 {
      margin-top:190px;
   }
   .b3 {
      margin-top:90px;
   }
   .b4 {
      margin-top:120px;
      margin-bottom: 30px;
      &:before {
        top:55px!important;
      }
   }
    .bloc:before {
        content:attr(data-text);
        width: 60px;
        height: 60px;
        display: block;
        border:1px solid @lf-blue;
        border-radius: 30px;
        position:relative;
        background: white;
        text-align: center;
        line-height: 56px;
        font-size: 24px;
        font-family: 'DinProBlack';
        top:65px;
      }
   .demi1 {
      width: 500px;
      border-right: 1px solid @lf-blue;
      display: inline-block;
      vertical-align: top;
      padding-right: 70px;
      text-align: right;
      margin-bottom: 50px;
      h3 { text-align: right;}
      .bloc:before {
       left:539px;
      }
   }
   .demi2 {
      width: 500px;
      display: inline-block;
      vertical-align: top;
       padding-left: 70px;
      text-align: left;
       h3 { text-align: left;}
      .bloc:before {
       left:-106px;
      }
   }
   .bloc_bleu {
    margin-bottom: 40px;
   }
   .tiers {
      width: auto;

      max-width: 33%;
      display: inline-block;
      vertical-align: top;
      font-family: Eczar;
      font-size: 16px;
      color:@lf-blue;
      text-align: center;
      margin-bottom: 70px;
      margin-left: 5px;
       margin-top: 50px;
      img  {
        margin-bottom: 30px;
      }
      h3 {
          font-size: 20px;
      }
   }

  .fid {
      width: 320px;
      border-right: 1px solid @lf-blue;
      height: 480px;
      display: inline-block;
      vertical-align: top;
      font-family: Eczar;
      font-size: 16px;
      color:@lf-blue;
      text-align: center;
      margin-bottom: 70px;
       margin-top: 40px;
       &:last-child {
        border:0;
       }
      img  {
        margin-bottom: 30px;
      }
      ul {
        text-align: left;
         margin-left: 40px;
      }
      h3.bronze {
          font-size: 24px;
          color:#62440c;
      }
       h3.argent {
          font-size: 24px;
          color:#88939a;
      }
       h3.or {
          font-size: 24px;
          color:#bda259;
      }
      h3:not(.fidel) {
          font-size: 16px;
      }
   }

 } .delivered, .atwork {
    display:none;
   }
}


.cms-la-carte {

   #header {
     background: white url(../images/cms/bgcarte.jpg) no-repeat center center / cover!important;
      .bigtitre {
      top:216px;

      &:before {
        font-size:35px;
        color:white;
        content:'La carte';
        font-family: 'DinProBlack';
        text-transform: uppercase;
        letter-spacing: 7px;
      }
      &:after {
        display: none;
      }
    }
   }
   .cms-right {
      height: auto;
   }
   .column.main {
     text-align: center;
     margin-top: 50px;
   }
   .columns {
    max-width: 1500px;
    margin:auto;
    text-align: center;
    ul {
      margin: 40px 0px;
      text-align: left;
      width: 50%;
        li {
            width:500px;
            text-align: left;
            height: 70px;
            button {
              float:right;
              margin-top: -12px;
              padding-left: 20px;
              img  {
                  margin-right: 10px;
                  vertical-align: inherit;
              }
            }
            &:first-child {
               h3:before {
                right:-103px;
                width: 205px;
               }
            }
            &:last-child {
               h3:before {
                 width: 180px;
                right: -128px;
               }
            }
            h3 {
              background: white;
              text-align: left;
              margin: 0;
              display: inline-block;
              padding: 0;
              width: 500px;

            }
            h3:before {
              content:'';
              border-bottom: 1px solid @lf-blue;
              display: block;
              position: relative;
              width: 200px;
              top: 16px;
              right: -108px;
            }
        }
    }


   }
   h3 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 30px;
    letter-spacing:2px;
     & + div {
        font-family:Eczar;
        font-size: 16px;
        color:@lf-blue;
      }
   }
   h2 {
    display: none!important;
   }
   .carte {
      float: left;
     width: 45%;
     margin-bottom:100px;
   }
   .delivered, .atwork {
    display:none;
   }
}


.centertext {
   font-family: Eczar, arial;
   margin-top: 20px;
   margin-bottom:40px;
   text-align: center!important;
   font-size: 18px;
   color:@lf-blue;
   margin:auto!important;

   .bleu {
    text-transform: uppercase;
   }
   .or {
    text-transform: uppercase;
    color:@lf-gold;
   }
     &:before {
      display:block;
      width:100px;
      height: 1px;
      background:@lf-blue;
      content:'';
      margin:auto;
      margin-top: 20px;
      margin-bottom:20px;
     }
   &.first:before  {
    display:none;
   }
}
.cms-left, .cms-right {
  width:50%;
  float:left;
  height: 870px;

  .btn {
     font-family:'DinPro';
     font-size: 11px;
     padding:15px 40px;
     text-transform: uppercase;
     border:1px solid @lf-gold;
     color: @lf-gold;
  }
  &.w40 {
    width:40%;
    float:none;
    display: inline-block;
    vertical-align: top;
    height: 435px;
    font-family: Eczar, arial;
   font-size: 16px;
    color:@lf-blue;
  }
  div {
    width:70%;
    text-align:left;
    margin-left:10%;
    h3, h5 {
      text-align: left;
    }
    h5 {
      font-size: 14px;
      margin-top: -20px;
      margin-bottom:20px;
      letter-spacing: 2px;
    }
  }
}
.cms-right {
  float:right;
}
.concept1 {
   background: white url(../images/cms/concept1.jpg) no-repeat center top;
   background-size: 100%;
}
.concept2 {
   background: white url(../images/cms/concept2.jpg) no-repeat center top;
   background-size: 100%;
}
.concept3 {
   background: white url(../images/cms/concept3.jpg) no-repeat center top;
   background-size: 100%;
   margin-top: 100px;
}
.concept4 {
   background: white url(../images/cms/concept4.jpg) no-repeat center top;
   background-size: 100%;
   margin-top: 100px;
}
.concept5 {
  margin-top: 150px;
   background: white url(../images/cms/concept5.jpg) no-repeat center top;
   background-size: 100%;
   width: 28.55%;
   height: 520px;
}
.concept6 {
  margin-top: 150px;
   background: white url(../images/cms/concept6.jpg) no-repeat center top;
   background-size: 100%;
    width:71.4%;
   height: 520px;
  margin-bottom: 100px;
}
.concept7 {
   background: white url(../images/cms/concept7.jpg) no-repeat center top;
   background-size: 100%;
   margin-top: 100px;
   margin-bottom: 100px;
}

 .formulaire {
   margin-top: 100px;
   h3 {
    margin-top:40px;
   }
   span {
     font-family: Eczar, arial;
   font-size: 14px;
    color:@lf-blue;
   }
   div {
    margin-left: 20%;
    margin-bottom: 15px;
    border:0;
      border-top: 1px solid @lf-blue;
      border-bottom: 1px solid @lf-blue;
      width: 60%;
       font-family:'DinProBold';
     font-size: 14px;
     text-transform: uppercase;
     height: 43px;
    line-height: 43px;
    letter-spacing: 2px;
    &.textarea {
      height: 250px;
    }
    textarea {
      border:0;
      height: 200px;
    }
    label {
      color:@lf-blue;
      cursor:pointer;
    }
    input {
      float: right;
      border:0;
      width: ~"calc(100% - 120px)";
      margin-top: 6px;
      padding-left: 15px;
       font-family:'DinProBold';
       font-size: 14px;
        color:@lf-blue;
    }
   }
}


.hand {
   background: white url(../images/cms/main.jpg) no-repeat center 70px;
   h3 {
    margin-top: 200px!important;
   }
}

.customer-account-logoutsuccess .columns p {
  text-align:center;
}

.checkout-cart-index .cart.main.actions {
    position: absolute;
    width: 100%;
    top: 640px;
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {

}

//
//  Mobile
//  _____________________________________________




@media only screen and (min-width: 300px) and (max-width: @screen__ipad) {






// blue screen of death

body.blue:before {
    z-index: 16;
    top:192px;
}

body.blue:after {
    content: '';
    background-color: @lf-blue;
    height: 117px;
    left: 0;
    z-index: 16;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 20;
    opacity: 0.8;
    pointer-events: none;
}

body.blue .page-wrapper:before,
body.blue .page-wrapper:after {
    content: '';
    background-color: @lf-blue;
    height:75px;
    left: 0;
    z-index: 20;
    position: fixed;
    width: 12px;
    top: 117px;
    z-index: 20;
    opacity: 0.8;
    pointer-events: none;
}

body.blue .page-wrapper:after {
    left: unset;
    width: 13px;
    right: 0;
}

.timeslot-expired-modal,
.creneau-warning-modal {
  overflow-y: unset;
  .modal-inner-wrap {
    width: 290px;
    max-width: 290px;
    top:-19px;
  }
  .margined {
    margin-top: -20px;
    line-height: 25px;
  }
  .footnote {
    line-height: 20px;
    padding:0 30px;
  }
   .modal-footer {
    min-height: unset;
    button {
      width: 100%;
      margin-bottom: 20px;
      margin-left: 0;
      margin-right: 0;
      &.first {
        background:#959595;
        border:1px solid #959595;
        color:white;
        margin-bottom: 20px;
        & + button {
          margin-top:0;
        }
      }
    }
  }
  .modal-content {
     font-size: 16px;
  }

  .shipping-warning-body,
  .shipping-expired-body {
    width:290px;
  }
  .action-close {
      left:unset;
      top: 5px;
      right: 5px;
      background: @lf-blue!important;
      &:before {
        color:white!important;
      }
    }
}



  .modal-popup.shipping-warning-modal,
  .modal-popup.checkout-validation-popin,
  .modal-popup.cart-stock-popin {
    .modal-content {
      padding-top: 120px;
      text-align: center;
    }
    .modal-title {
      border:0;
      color: @lf-blue;
      font-size: 25px;
      font-family: 'Futura'!important;
      line-height: 50px;
    }
    .modal-inner-wrap {
      min-width: 100%!important;
      width: 100%!important;
      overflow: visible;
    }
    .modal-footer {
      min-height: unset;
      padding: 20px!important;
      text-align: center;
    }
    .modal-header {
      padding-bottom: 0.5rem;
      padding-top: 1.2rem!important;
      color: @lf-blue;
      font-size: 25px;
      font-family: 'Futura'!important;
      left: 0;
      text-align: center;
      width: auto;
      padding-right: 100px;
      white-space: nowrap;
    }
  }






#remove-coupon + .field {
   float: left;
    width: 100%;
    & + button {
    }
}

.checkout-cart-index .cart.main.actions {
    margin-left: 20px;
    margin-top: 490px;
    position: absolute;
    width: 100%;
    top: unset;
}

.customer-account-logoutsuccess .columns p,
.sales-order-history .message.empty.info {
  font-size: 18px;
}
.page.messages {
    .messages {
        .message {
            padding-top: 28px;

            div {
                text-align: center;
            }
        }
    }
}


     .modal-popup.modal-slide._inner-scroll .modal-inner-wrap {
      height:100%!important;
     }

     .password-bubble {

        margin-top:0px;
        margin-left:0px;
        width:100%;
        position:relative;
        padding-left: 0;
        padding-right: 0;
        ul {
          padding-left: 30px;
        }
        p {
        margin-top:-15px;
        padding-top:15px;
        font-size: 15px;
        letter-spacing: 1px;
        }
        li {
          white-space: nowrap;
          &:before {
            left:8px;
          }
        }

    }

    .field.password .control {
      margin-top:0px!important;
    }
    .eye { top:37px; }
     .form.contact fieldset {
      padding:20px;
     }
    .message.global.cookie {
        z-index: 23!important;
    }
    body {
      overflow-x:hidden;
    }
    .d-none {
      display:none!important;
    }
    .d-nope {
      height:1px!important;
    }
    .bigtitre {
      display: none;
    }

   .onepage-index-index .lf-billing-address-form {
    width: calc(100% + 24px);
    }

  .customer-account-logoutsuccess,
  .checkout-onepage-success,
  .onepage-index-index,
  .customer-account-login,
  .customer-account-forgot-password,
  .account {
  #header {
   min-height: 50px;
   .container {
    height: 50px;
   }
   .delivery.fixednoanim .step1,
    .delivery .step1,
    .commande {
      display:none!important;
    }
    .step3 {
      background:none;
    }
    .filtres {
      box-shadow:none;
      &.open {
        top:92px!important;
      }
    }
      .loader {
        display: none;
      }

  }
  .block-order-details-view,
      .order-details-items {
        width: 90%;
        margin-left: 5%;
        .grand_total .price {
          font-size: 24px;
          font-weight: bold;
        }
      }

  }

  .account #header {
      min-height: 80px;
      .container {
        height: 90px;
       }
      .menu {
        &:before {
          content:'';
          display:block;
          position:absolute;
          box-shadow:inset 0px 10px 10px -11px #656565;
          margin-top:32px;
          width:~"calc(100% + 20px)";
          height:5px;
          margin-left:-20px;
        }
      }

  }
  .account .sidebar-main {
    display: none;
  }


    //CMS


    .atwork {
        background-image: url(../images/cms/atworkmobile.jpg);
        width: 100%;
        padding-top: 200vw;

        div {
          width: ~"calc(100% - 60px)";
          padding: 30px;
          & + div {
            margin-left:0;
             width: ~"calc(100% - 60px)";
             background-image: url(../images/cms/atworkmobile2.jpg);
             background-size: 100%;
             background-repeat: no-repeat;
             padding-top: 90vw;
             & + hr + div {
                padding-top:30px;
                margin-top: -30px;
                background:none;
                text-align: center;
                & + div {
                    display:none;
                }
             }
          }
        }
    }
    .delivered {
      width: 100%;
      overflow: hidden;
      margin-top: 0;
      max-width: 100%;
      .logos {
          width: 3900px;
          background-size: 1300px;
          position:static;
          background-repeat: repeat-x;
          animation: slide 20s linear infinite;
          &:before, &:after  { display:none; }
      }
      @keyframes slide{
        0%{
          transform: translate3d(0, 0, 0);
        }
        100%{
          transform: translate3d(-1300px, 0, 0);
        }
      }

    }
    .closest {
      width:~"calc(100% - 40px)";
      background-image: none;
      padding: 20px;
      height: 230px;
      input[type='text'] {
        width:~"calc(100% - 50px)";
      }
      h4 {
        padding:30px;
      }

    }

.cms-la-carte {
   #header {
     background: white url(../images/cms/bgcartemobile.jpg) no-repeat top 90px center / cover!important;
   }

   .carte {
      float:none;
      width: 100%;
      margin-bottom: 50px;
   }
  .columns {
    max-width: auto;
    margin:auto;
    text-align: center;
    ul {
         float:none;
         margin:0;
         width: 100%;
         margin-top:30px;
        li {
            width:100%;
            height: 120px;
            button {
                float:none;
                margin-top: 15px;
                display: block;
                width:~"calc(100% - 80px)";
            }
            h3 {
              background: white;
              margin-top: -5px;
              vertical-align: top;
              margin-bottom: 40px;
              width: 100%;
            }
            h3:before {
              display: none;
            }
        }
    }
  }
}


.cms-comment-ca-marche {

   #header {
     background: white url(../images/cms/bgcommentmobile.jpg) no-repeat top 90px center / cover!important;
   }

   .columns {
    max-width: auto;
    margin:auto;
    text-align: center;

    h3 {
        font-size: 32px;
    }
   .bloc {
      font-family: Eczar;
      font-size: 16px;
      color:@lf-blue;
      position: absolute;
      width: 100%;
      h3 {
          font-size: 20px;
          text-align: center!important;
          &:before {
            content:'';
            border-right: 1px solid @lf-blue;
            display: inline-block;
            width: 1px;
            height: 30px;
            position:absolute;
            left:50%;
            margin-top: -111px;
          }
      }
   }
   .b1 {
      margin-top:-550px;
   }
   .b2 {
      margin-top:260px;
   }
   .b3 {
      margin-top:20px;
   }
   .b4 {
      margin-top:820px;
      margin-bottom: 30px;
      &:before {
        top:-5px!important;
      }
   }
    .bloc:before {
        content:attr(data-text);
        top:-5px;
      }
   .demi1 {
      width:100%;
      height: 500px;
      border-right: 0px;
      padding-right: 0px;
      text-align: center;
      margin-top: 30px;
      margin-bottom: 0;
      .bloc:before {
       left:~"calc(50% - 30px)";
      }
   }
   .demi2 {
      width:100%;
      height: 550px;
       padding-left: 0px;
      text-align: center;
      .bloc:before {
       left:~"calc(50% - 30px)";
      }
   }
   .tiers {
      max-width: 100%;
      margin-left: 0px;
      margin-bottom: 0px;
   }
   .bloc_bleu {
    margin-top: 50px;
   }
  .fid {
      width: auto;
      border-bottom: 1px solid @lf-blue;
      border-right: 0px;
      height:auto;
      margin-bottom: 70px;
      padding-bottom: 30px;
      margin-top: 0;
   }

  }

}

.cms-nos-restaurants {
   #header {
     background: white url(../images/cms/bgrestomobile.jpg) no-repeat top 90px left / cover!important;
   }
   .columns .comlumn.main {
    padding-bottom:0;
   }
   .content {
    width:auto;
    height:auto;
    display:flex;
    flex-direction:column;
   }
   .page-title-wrapper {
   opacity:1;
   }
   .restos {
    height:auto;
    float:none;
    order:2;
      width:~"calc(100% - 40px)!important";
      padding-right:13px;
    .item {
      width:100%!important;
    }
   }
   .mapr {
    width:100%;
    height:400px;
    order:1;
    float:none;
   }
   .gm-style .gm-style-iw {
    width:unset!important;
   }
}

.cms-le-concept {
   #header {
     background: white url(../images/cms/bgconceptmobile.jpg) no-repeat center center / cover!important;
   }
   .columns .comlumn.main {
    padding-bottom:0;
   }
   .cms-left,
   .cms-right {
    width: 100%;
    height: auto;
    float: none;
    div {
      margin:30px;
      width:~"calc(100% - 60px)";
    }
    button {
      margin-top: 30px;
      font-size: 16px;
    }
    &.w40 {
      width:100%;
      height: auto;
      padding-bottom: 440px;
      br {
        display: none;
      }
    }
    .centertext br {
      display: none;
    }
    .centertext br + br {
      display: block;
      height: 25px;
    }
    &.concept1 {
      height: 100vw;
      background-position: center left;
      margin-bottom: 1px;
     }
&.concept3 {
      display: none;
    }
   &.concept4 {
    display: none;
    & + .w40 {
       background: white url(../images/cms/concept3.jpg) no-repeat bottom center;
        & + .w40 {
         background: white url(../images/cms/concept4.jpg) no-repeat bottom center;
      }
    }
   }
    &.concept5 {
      width:100%;
      height: 105vw;
      margin-top: 0;
    }
    &.concept6 {
      width:100%;
      height: 82vw;
       margin-top: 0;
       background: white url(../images/cms/concept6mobile.jpg) no-repeat bottom center;
        background-size: 100%;
    }
    &.concept7 {
      display: none;
    }
&.concept2 {
    height: 100vw;
   }


   }
   h3 {
    font-size: 36px;
      br {
        display: none;
      }
   }

}



.pac-container.fixednoanim {
  position:fixed!important;
  top:190px!important;
}

    #header {

         height:auto;
         background:@lf-blue;
         backface-visibility: hidden;
         top:0;
         z-index: 16;
         min-height: 420px;
        .container {
            padding: 0;
            background:@lf-blue;
            position:fixed;
            width: 100%;
            z-index: 16;




            &.okdelivery {
              height: 93px;

              .delivery {
                transition: unset;
              }
              .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
                margin-top: -219px;
                margin-right: -13px;
                  .smallcart {
                    height:9px;
                    background: none;
                  }
                  .overlaysmallcart {
                    height: 70px;
                  }
                }

              .delivery.fixednoanim {
                .step1:not(.OK) + .step2[style='display: none;'] + .step3 {
                //margin-top: -211px;

                }
               .commande{
                display:none!important;
              }
            }
              .step1.OK{
                display:none!important;
              }


            }
            .links {
                position:absolute;
                background:@lf-blue;
                left: -300px;
                width: 300px;
                transition: .28s ease-in-out;
                text-align: left;
                padding-left:0;
                &.open {
                    left:0;
                    z-index: 15;
                    border-right: 1000px solid rgba(0,52,86,0.8);
                    border-left: 0;
                    background-clip: padding-box;
                    background-color: white;
                    color:@lf-blue;
                    bottom: 0;
                    position: fixed;
                    top: 0;
                    padding-top: 140px;
                    margin: 0;
                    padding-left:0;
                    a.identified {
                      padding-top:10px!important;
                      border-top: 12px solid @lf-blue!important;
                      color:transparent!important;
                    }
                     a.identified:after  {
                      background-color:@lf-blue!important;
                      color:white!important;
                     }

                     a.identified {
                        &.f-20 {border-top: 10px solid #a3a198!important;   }
                        &.f-20,
                        &.f-20:after {
                          background-color: #a3a198!important;
                        }
                        &.f-30 { border-top: 10px solid @lf-gold!important; }
                        &.f-30,
                        &.f-30:after {
                            background-color: @lf-gold!important;
                          }
                    }
                     a:last-child {
                        padding:0;
                        white-space: nowrap;
                        position: fixed;
                        top: 0;
                        left: 0;
                        color:@lf-blue;
                        width: 249px;
                        font-size: 17px;
                        width:274px;
                        height: 80px;
                        padding-left:26px;
                        background-color: #f6f6f6;
                        text-align: left;
                        border-top: 20px solid #f6f6f6;
                        border-bottom: 10px solid #f6f6f6;
                        display: block;
                        padding-top: 30px;

                        img {
                            display:none;
                        }
                        &:after {
                          line-height:20px;
                          top: 0;
                          padding-top: 50px;
                          background: none;
                          color: @lf-blue;
                          height: 70px;
                          padding-left: 25px;
                          font-size: 18px;
                          width: 155px;
                        }
                    }
                    .aspan {
                      left:0;
                      a.close {
                        color:white;
                        &:before {
                          content:'Déconnexion';
                          display: inline-block;
                          font-size:8px;
                          padding-top: 11px;
                          display: inline-block;
                          vertical-align: top;
                          letter-spacing: 1px;
                        }
                      }
                    }
                }
                .aspan {
                  width: 300px;
                   &.fixednoanim {
                     right:unset;
                        height:inherit;
                        padding:inherit;
                        height: auto;
                        padding-top: 55px;
                        margin-left: -1px;
                        &:after {
                            line-height:inherit;
                        }
                    }

                    left: -300px;

                }
                a {
                    display:block;
                    border:0;
                    margin:25px 20px;
                    color:@lf-blue;
                    padding-left: 6px;
                    font-size: 14px;
                   &.account:not(.identified) {
                    color:@lf-blue!important;
                   }
                }
            }
        }
        .logo {
            margin-left: 60px;
            width:200px;
            margin-top:10px;
            height: auto;
            background-size: 170px;
            margin-bottom: -3px;
            border-left:0!important;
            &.fixednoanim {
                width:200px;
                max-width:none;
                top:unset;
                left:unset;
                border:0;
                background: transparent url(../images/logoa.svg)!important;
                position: initial;
                margin-top: 10px;
                background-size: 170px!important;
                background-repeat: no-repeat!important;
            }
        }
        .menu  {
          display:block;
          background:white;
          color:@lf-blue;
          text-transform: uppercase;
          font-family:'DinProBold';
          padding: 12px 20px;
          cursor:pointer;
          box-shadow: inset 0px 10px 10px -8px #656565;

          &:after {
            content: '';
            border-bottom: 2px solid #003456;
            border-right: 2px solid #003456;
            transform: rotate(45deg);
            width: 8px;
            height: 8px;
            display: block;
            float: right;
            margin-top: 5px;
          }
        }
        .burger {
            position: fixed;
            display: block;
             -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            width: 60px;
            height: 50px;
            top: 0;
            left: 0;
            background-color: @lf-blue;
            -ms-transform: rotate(0);
            -webkit-transform: rotate(0);
            transform: rotate(0);
            -webkit-transition: .28s ease-in-out;
            transition: .28s ease-in-out;
            display: table-cell;
            vertical-align: middle;
            margin: 0;
            z-index: 16;
            cursor:pointer;
            &.open {
                height: 51px;
                background: none;
                span:first-child {
                    top: 24px;
                    -ms-transform: rotate(135deg);
                    -webkit-transform: rotate(135deg);
                    transform: rotate(135deg);
                }
                span:nth-child(2) {
                   opacity: 0;
                    left: -60px;
                    top: 24px;
                }
                span:nth-child(3) {
                    top: 24px;
                    -ms-transform: rotate(-135deg);
                    -webkit-transform: rotate(-135deg);
                    transform: rotate(-135deg);
                }
                span {
                   background:@lf-blue;
                }
                &.identified span {
                  background:white;
                }
            }
            div {
                position: relative;
                width: 20px;
                margin: 0 auto;
            }
            span {
                position: absolute;
                height: 3px;
                width: 20px;
                background-color: white;
                opacity: 1;
                left: 0;
                -ms-transform: rotate(0);
                -webkit-transform: rotate(0);
                transform: rotate(0);
                -webkit-transition: .28s ease-in-out;
                transition: .28s ease-in-out;
                &:first-child {
                    top: 16px;
                }
                &:nth-child(2) {
                    top: 24px;
                }
                &:nth-child(3) {
                    top: 32px;
                }

            }
        }
        .delivery, .delivery.fixednoanim {
            position: fixed;
            top:92px;
             -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            margin:25px 13px;
            height: auto;
            width: ~"calc(100% - 26px)";
            background:none;
            left:auto;
            right: auto;


            &:after {
              border-bottom:0;
            }

            .block-minicart .product-item-details {
                max-width: ~"calc(100% - 90px)";
            }
            .block-minicart .product-item-name {
                max-width: 100%;
            }
            .txt {
                margin:0;
                padding: 10px;
                position: relative;
            }
            &.okdelivery {
                margin-top:-80px!important;
                position:unset;
                background: none;
                border: 0px;
                transition:0s;
                .step1 {
                  display:none;
                }
                .step1.OK {
                  display:block!important;
                }
                .step3 {
                    position: absolute!important;
                }
            }
            .nok {
              z-index:2;
            }
            .nok:not([style='display: none;']) + .oldAddresses {
              display:none;
            }


            .nok, .check {
                width: ~"calc(100% - 60px)";
                background: white;
                padding: 15px 0px 15px 15px;
                margin: 0px;
                margin-top: 4px;
            }
          .check {
            left:44px;
            width: ~"calc(100% - 74px)";
            top:71px;
            background: #fb7576;
            padding: 15px;
            border-radius:0;
            &:before {
              top:-6px;
              left:15px;
              z-index: -1;
            }


          }

            .oldAddresses {
                width: ~"calc(100% - 79px)";
                background: white;
                margin: 0px;
                margin-top: -8px;
                margin-left: 60px;
                position:absolute;
                cursor:pointer;
                > div {
                  max-height:360px;
                overflow-x:hidden;
                  overflow-y:auto;
                }
            }
            .step1.OK {
              display:none!important;
            }
            .step1:not(.OK) + .step2 {
              display:none;
            }
            .question {
                border: 0;
                padding: 3px 10px;
                margin-left: 50px;
                height: 10px;
                position: absolute;
                font-size: 16px;
                margin-top: 5px!important;
            }
            .adresse {
                width: ~"calc(100% - 65px)";
                padding-left: 5px;
                font-size: 14px;
                margin-left: 60px;
                margin-top: 33px;
                border-left: 0;
                margin-bottom: 10px;
                background: white;
            }
            .step1 {
                display: block!important;
            }

            .step1, .step2 {
              background:white!important;
              width: 100%;
              height: auto;

            }
            .step1 .ok, .step2 .ok {
              position: absolute;
              left: -1px;
              zoom: .75;
              -moz-transform:scale(0.75);
              width: 60px;
              letter-spacing: -1000px;
              color: @lf-blue;
              padding-top: 33px;
              height: 69px;
              display: inline-block!important;
              margin-top:-1px;
              &:before {
                margin-right:0;
                width: 20px;
                height: 27px;
                background-size: 20px;
              }
            }
            .btn-radio {
                margin-left:48px;
                display: block;
                width: ~"calc(100% - 90px)";
                display: inline-block;
                float: left;
                clear: both;
                margin-top: 3px;
                margin-bottom: 10px;
                border-bottom: 1px solid #d9d9d9;
                border-radius: 0;
                padding-bottom: 14px;
                padding-left: 12px;
                &:last-of-type {

                }
            }
            .backbutton {
                display: block;
                margin-left: 62px;
                width: 79px;
                display: inline-block;
                float: left;
                clear: both;
                position: relative;
                margin-bottom: 10px;
            }
            .step2 {
                background: white;
                .ok {
                  margin-top: -215px;
                  height: ~"calc(100% - 30px)";
                }

                &.OK {

                   &[style="display: none;"] ~ .step1.OK {
                     display:none!important;
                   }
                }

                .ok:before {
                  width: 32px;
                  height: 32px;
                  background-size: 31px;
                  background-position: center;
                  height: ~"calc(100% - 40px)";
                }
                .question{
                    margin:0;
                    vertical-align: top;
                    float: left;
                    margin-left: 50px;
                }
                .choix-livraison {
                    height: 120px;
                    position: relative;
                    top:unset;
                    display:block!important;
                    clear:both;
                    margin-top: 40px;
                    .tooltip span {
                        position: fixed;
                        right: 20px;
                        margin-top: -20px;
                    }
                }
                .creneau1, .creneau2, .creneau3 {
                    position: fixed;
                    top: 209px;
                    margin-left: 45px;
                    width: ~"calc(100% - 93px)";
                    .tooltip {
                        width: 100%;
                        margin-top:-5px;
                        &:before {
                          margin-left: 9px;
                        }
                    }
                }
                .creneau2 {
                    top: 262px;
                }
                .creneau3 {
                    top: 318px;
                }
                &.noToday {
                  .creneau2 {
                     top: 209px;
                  }
                  .creneau3 {
                    top: 263px;
                  }
              }
              &.noTomorrow {
                  &.noToday {
                    .creneau3 {
                      top: 192px !important;
                    }
                  }
                .creneau3 {
                   top: 244px !important;
                   }
              }



            }



            .step1.OK + .step2.OK + .step3 {
                padding: 10px;
                width: ~"calc(100% - 20px)";
                left:0;
                height: auto;
                margin: 0;
                top:0;
                background: white;
                padding-bottom: 0;
                .step {
                    display:none;
                }
                 .smallcart{
                  opacity:1;
                  position: fixed;
                  top: 0;
                  right: 0;
                  margin: 0;
                  padding: 10px 0 9px;
                  border-radius: 0;
                 }
                .overlaysmallcart {
                  height:70px;

                }
                .question {
                  float: left;
                  position: relative;
                  margin-left: 0;
                  display: block;
                  width: 100%;
                }
                .resetPopin {
                 top: 167px;
                  width: 100%;
                  right: 0px;
                  span {
                    width: auto;
                  }
                  &._place {
                    top: 87px;
                  }
                }

                .commande {
                    margin:0;
                    line-height: 14px;
                    font-size: 13px;
                    margin-top: 6px;
                    height: auto;
                    padding-bottom: 4px;
                    width: 100%;
                    transition: 0.5s all ease;
                    top:0;
                    opacity:1;
                    .question {
                        margin-top:10px!important;
                    }
                    &.hidden {
                        top:-100px;
                        position:absolute;
                        opacity:0;
                    }
                    .picto {
                        display:none;
                    }
                    b {
                        width: 90px;
                        min-width: 90px;
                        float: left;
                    }
                    #_when {
                      border-bottom:0;
                      border-bottom: 0;
                      padding-bottom: 10px;
                    }
                    .grey.blue {
                        display: block;
                        float: left;
                        width:100%;
                        margin-bottom: 6px;
                        border-left:0px;
                        padding-left: 10px;
                        padding-top: 15px;
                        max-width: ~"calc(100% - 35px)";
                        border-bottom: 1px solid #d9d9d9;
                        padding-right: 25px;
                        padding-bottom: 15px;
                        top: unset;
                        &:after {
                          right:8px;
                        }
                    }
                }
            }
        }
         .delivery.fixednoanim {
            position: relative;
            top:-10px;

            & + .menu {
              top: 52px;
              position: fixed;
              right: 0;
              left: 0;
            }
             .creneau1, .creneau2, .creneau3 {
                  .tooltip {
                      margin-top:15px!important;
                  }
              }
            .step3 {
              border:0px;
              top: -500px!important;
            }

            .creneau1 {
                top: 259px!important;
            }
            .creneau2 {
                top: 312px!important;
            }
            .creneau3 {
                top: 368px!important;
            }
            .noTomorrow {
                  .creneau3 {
                  top: 312px!important;
                }
              }
            .noToday {
              .creneau2 {
                 top: 259px!important;
              }
              .creneau3 {
                top: 312px!important;
              }
              &.noTomorrow {
                .creneau3 {
                  top: 259px!important;
                }
              }
            }
        }
    }
    .filtres.fixednoanim + .recap_filtres + .categorie {
      position: relative;
      z-index: 15;
      & ~ .categorie {
        position: relative;
        z-index: 15;
      }
    }
    .filtres .filtre:not(.selected):hover:after {
      background: none;
    }
    .filtres.fixednoanim .filtre.last  {


          width: ~"calc(100% - 60px)!important";

          &.selected  {
             width: ~"calc(100% - 70px)!important";
          }
           &.clickable {
             width: ~"calc(100% - 70px)!important";
          }
          .tooltip .item:after {
            right:-10px!important;
          }

      }
      .filtres .filtre.last  {

          &.selected  {
             width: ~"calc(100% - 10px)!important";
          }
           &.clickable {
             width: ~"calc(100% - 10px)!important";
          }
          .tooltip .item:after {
            right:0px!important;
          }

      }
    .filtres.fixednoanim .container {
      box-shadow: none!important;

    }
    .filtres, .filtres.fixednoanim {
        position: fixed;
         -webkit-backface-visibility: hidden;
         backface-visibility: hidden;
        width: 100%;
        background: white;
        transition: .28s ease-in-out;
        height: auto!important;
        top: -1100px!important;
        padding-top:0;
        z-index: -1;
        box-shadow: 0px 4px 15px -2px #656565;
        padding-bottom: 20px;

        .container {
            background:none;
            padding: 15px 30px;
            display: block!important;
            height: auto!important;
            box-shadow: inset 0px 10px 10px -8px #656565;
            height:280ps!important;
            .inside {
                display:block!important;
            }
        }
        &.open,  &.fixed {
            opacity:1;
            top:92px!important;
            z-index: 16;
            border-top: 0;
            bottom: 0px;
            overflow-y: auto;
            overflow-x: hidden;
        }
        &.hasSelected .filtre {
            padding-left: 30px;
        }
       .filtre {
            font-size: 16px!important;
            padding: 8px 0!important;
            display: block;
            clear: both;
            text-align: left;
            &.last:before {
                border-left:0!important;
            }
            &.last {
              border-top: 1px solid @lf-blue;
              margin-top: 15px;
              padding-top:15px!important;
               width: 100%;
               transition:unset!important;
               &.selected:hover .tooltip {
                opacity:1;
               }
               &:not(.selected):hover {
                 background: none!important;
                  color:@lf-blue!important;
                   transition:unset!important;
                  &:after {
                    background: none!important;
                  }
                   .f1 {
                    display:inline;
                  }
                  .f2 {
                    display:none;
                  }
               }
               &.clickable  {
                  background: none!important;
                  color:@lf-blue!important;
                   transition:unset!important;
                  .f1 {
                    display:inline;
                  }
                  .f2 {
                    display:none;
                  }
                }
            }
            &.last .tooltip {
                position: unset;
                border: 0;
                background: 0;
                margin: -22px!important;
                margin-top: 0!important;
                width: 100%;
                display: block!important;
                opacity:1!important;
                ul {
                  padding:0;
                }
                h5 {
                  font-size: 16px;
                  padding:20px 10px;
                }
                &:before {
                    display:none;
                }
                .item {
                  padding:6px 18px 10px;
                  width: 100%;
                  font-size: 16px;
                  height: 22px;
                  &:after {
                    right:0;
                    height: 24px;
                    width: 24px;

                  }
                  &.selectedfilter::after {
                      outline:6px solid @lf-blue;
                      outline-offset: -13px;
                  }
                  &[data-content='veggie'] {
                      &.selectedfilter:after  {
                      outline: 6px solid #2a8e50;
                    }
                  }
                  &:hover {
                    background:none;
                  }
                }
            }

            &.selected {
                transition:unset!important;
                padding-left: 10px !important;
               padding-right: 10px !important;
               margin-left: -10px;
                &.last {
                  width: 100%;

                  &.clickable  {
                  background: none!important;
                   transition:unset!important;
                  .f1 {
                    display:inline-block;
                  }
                  .f2 {
                    display:none;
                  }
                  }


                }

                &.last:after {
                  background: none!important;
                  color:@lf-blue!important;
                   transition:unset!important;
                }

                &:after {
                    width: 100%;
                    opacity: 1!important;
                }
                &:hover:after {
                    width: 100%;
                    opacity: 1!important;
                }
            }
            &.selected.clickable .tooltip {
              display:block!important;
            }
            &.clickable {

                padding-left: 10px !important;
               padding-right: 10px !important;
               margin-left: -10px;
                transition:unset!important;
                .tooltip {
                  display:block;
                  opacity:1;
                  transition:0.2s all ease;
                  padding-bottom:60px;
                 }


                &.last:after {
                  background: none!important;
                  color:@lf-blue!important;
                }

                &:after {
                    background:@lf-gold!important;
                    width: 100%;
                    opacity: 1!important;
                }
                &:hover:after {
                    background:@lf-gold;
                    width: 100%;
                    opacity: 1!important;
                }
            }
        }
    }

    .fixednoanim .filtre.last .tooltip .item:after {
      right:0px!important;
      position:relative;
      float:right;
       transition:unset!important;
    }
    .recap_filtres {
        display:none;
     }

     .produits .categorie .container .produit.added:hover div.visu div:before {
        content: attr(data-count)!important;
        font-family:inherit!important;
        font-size:inherit!important;
        background:none;
        color:@lf-blue!important;
     }
      .produits .categorie .container .produit.added:hover div.visu div:after {
        display:none;
     }

     .produits {
      h2 {
        margin-bottom: 0!important;
        margin-top: 30px;
      }
      .categorie .container .produit.added:hover div.visu div:before {

                content: attr(data-count);
            }
        .categorie .container .produit {
            width: 48%;
            height: ~"calc(50vw + 115px)";
            margin-top:20px;
            .smalldesc + hr {
              display: none;
            }
            &:hover {
              margin-top:17px;
            }
            .smalldesc {
              overflow: hidden;
              white-space: normal;
              line-height: 20px;
              margin-top: 5px;
            }
            label {

              top: -10px;
              img {

              }
            }
            &.added div.visu div {
                margin-top: -65%!important;
                content: attr(data-count);
            }
            h4 {
              height: 30px;
              margin-top: 10px;
              font-size:14px;
              overflow: hidden;
            }
            .desc {
              display:none;
            }
            .content {
              height:120px;
            }
            div.visu {
              padding-top: 100%;
              overflow: hidden;
              height: auto;
              position: relative;
              img.prod {
                min-width: 133%!important;
                height: 100%!important;
                position: absolute;
                top: 0;
                left:-17%;
              }
            }
            &.cms {
                width: ~"calc(48% - 15px)";
                padding-left:15px;
                display:none;
                &:before {
                    transform: scale(0.6);
                    margin-left: -67px;
                }
                h3 {
                    font-size: 16px;
                    min-width: unset;
                }
                &:hover {
                    width: ~"calc(48% - 15px)";
                    padding-left:15px;
                }
            }
            .pic {
               display: none;
            }
            .addtocart {
              font-size:20px;
              white-space:nowrap;
              img {
                margin:0;
              }
              div.ttc, div.ht {
                  transition:all 0.5s ease;
                  z-index: 4;
                  zoom: 0.75;
                  -moz-transform:scale(0.75);
              }

              &.prixbarre {
                &:before {
                  margin-top:0px;
                }
                div.ttc, div.ht {
                     top:-26px;
                }
                div.barre {
                  top: -10px;
                  zoom: 0.6;
                  -moz-transform:scale(0.6);
                }
              }
              div.ht {
                   margin-left:5px;
              }
              span {
                  vertical-align: super;

                  &.tax {
                    width: 30px;
                    margin-left: 3px;
                    width: 30px;
                    display: inline-block;
                    font-size:11px;
                    top: 0px;
                    position: absolute;
                    left: 50px;
                    color:#999;
                    height: 19px;
                    cursor:pointer;
                    font-family:'DinPro';
                    opacity:1!important;
                    &:hover {
                        text-decoration:underline;
                    }
                    &.selected {
                        text-decoration:none!important;
                        font-family:'DinProBold';
                        color:@lf-blue;
                    }
                    & + .tax {
                        top: 12px;
                        left:50px;
                    }
                  }
                }
                .quantity {
                  position: absolute;
                  right: 0;
                  bottom: 10px;
                    div  {
                        position: relative;
                        z-index: 2;
                        margin-left: -10px;
                        margin-right: 5px;
                        width: 40px;
                        background:@lf-blue;
                        border-radius: 20px;
                        color:white;
                        width: 36px;
                        &.plus {
                          margin-top: -90px;
                        }
                        &.moins {
                          margin-right: -26px;
                        }
                    }
                    input {
                        display: none;
                    }
                }
            }
        }
    }

    .productlayer.shown, .productlayer._show {
        z-index: 20;
        top:0;

        .close {
            right: -5px;
            top: -5px;
            transform: scale(0.6);
            z-index: 12;
        }
        .cross {
            display:none;
        }
        hr {
            height: 1.2px;
        }
        .jssorb051 {display:block;position:relative;cursor:pointer; top: 88%; }
        .jssorb051 .i {position:relative }
        .jssorb051 .i .b {fill:#fff;fill-opacity:0.5;stroke:#000;stroke-width:400;stroke-miterlimit:10;stroke-opacity:0.5;}
        .jssorb051 .i:hover .b {fill-opacity:.7;}
        .jssorb051 .iav .b {fill-opacity: 1;}
        .jssorb051 .i.idn {opacity:.3;}

        .gzoom {
            margin:0;
            width:unset;
            height: unset;
            .added:first-child {
              background:@lf-blue;
              &:before {
                position: absolute;
              display: inline-block;
              width: 70px;
              text-align: center;
              margin-top: 25vw;
              font-family: 'DinProBold';
              font-size: 50px;
              background: white;
              border-radius: 200px;
              opacity: 1;
              margin-left:~"calc(50% - 35px)";
              line-height: 64px;
              transition: 0.2s all ease;
              height: 70px;
              letter-spacing: -1px;

              content: attr(data-count);
              color: @lf-blue;

              }
              .slides img {
                opacity:0.3;
              }
            }
            img.big, img.vignette {
                left:0!important;
                top:0!important;
                right:0!important;
                height:auto!important;
                width:100%!important;
                position: relative!important;
            }
            .slides {
                position: absolute; left: 0px; top: 0px; width:100%; overflow: hidden;
            }
        }
        .produit {
            margin: 0;
            margin-top: 10px;
            width: 100%;
            zoom: 0.7;
            -moz-transform:scale(0.7);
            position: fixed;
            left: 0;
            top: 45%;
            bottom: 0;
            overflow-y: auto;
            padding-bottom: 10px;

            .addtocart {
                height:32px;
                  img {
                    right:5px;
                    }
                .quantity input {
                    height:26px;
                }
            }
            .content {
                max-height: none!important;
            }
        }
    }
    .pac-container {
      margin-left: -15px;
      width: ~"calc(100% - 71px)!important";
     }
    footer {
        background:@lf-blue;
        padding:0px;
        padding-bottom:20px;
        min-height: 560px;
        img  {
            width:80%;
            float:right;
        }
        .bloc {
            width:100%;

            float:left;
            font-size:11px;
            font-family:'DinProBold';
            color:white;
            text-transform: uppercase;
            line-height:20px;
            border-right: 0;
            text-align: center;
            letter-spacing: 2px;
            padding-bottom: 15px;
            padding-top: 5px;

            &:after {
              content:'';
              height: 1px;
              width: 160px;
              background: white;
              display: block;
              margin:auto;
              margin-top: 23px;
            }
            &.last:after {
              display: none;
            }
            &:first-child {
                width: 90%;
                clear: both;
                margin: 6% 0 4%;
                height: 60px;
                border:0;
                padding: 0;
                &:after {
                  display:none;
                }
            }
            &:nth-of-type(2) {
              display: none;
            }
            a {
                font-size:11px;
                font-family:'DinProBold';
                color:white;
                text-transform: uppercase;
                display: inline-block;
                white-space: nowrap;
                letter-spacing: 2px;
                &:hover {
                    text-decoration:underline;
                }
            }
        }
    }


    .form.password.forget {
      padding:20px;
    }
    .customer-account-logoutsuccess,
    .customer-account-login,
    .customer-account-forgotpassword {
      #header .menu {
        display:none!important;
      }
    }


    .changeAdress .action-close {
      top:10px;
      right:10px;
    }

    .cart-container .action.update {
      position:absolute;
      width:~"calc(100% - 40px)";
      height:55px;
      color:@lf-blue!important;
      z-index:-1;
      opacity:0;
      pointer-events:none;
      &.visible {
        z-index:2;
        opacity:1;
        pointer-events:all;
      }

    }
}



.fidelite {
    span {
      display:inline-block;
      font-weight: bold;
      &:after {
         font-size:18px;
          display: inline-block;
          vertical-align: top;
          line-height: 18px;
          margin-left:5px;
      }
    }
    &.f-10 span {
      color: #71662b!important;
       &:after {
        content:' \2605';
      }
    }
    &.f-20 span {
      color: #a3a198!important;
       &:after {
        content:' \2605\2605';
      }
    }
    &.f-30 span {
        color: @lf-gold!important;
         &:after {
        content:' \2605\2605\2605';
      }
    }
}


.iwd_empty_cart_powered_by {
  display:none;
}







// petits telephones (iphone 5)

@media only screen and (min-width: 300px) and (max-width: 374px) {
    #header .delivery {
        &.fixednoanim {
            top: -18px;

            & + .menu {
                top: 50px;
            }
        }

        .adresse {
            font-size: 11px !important;
        }
    }
  .produits .categorie .container .produit .addtocart div.ttc,
   .produits .categorie .container .produit .addtocart div.ht {
    zoom: 0.62;
    -moz-transform:scale(0.62);
  }

}
@media only screen and (min-width: 300px) and (max-width: 765px) {

 .account .block-addresses-list .items.addresses > .item {
  width: auto!important;
 }
}
//ipad

@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
  body, html {
    zoom:0.75;
  }
   #header .container .links a {
    font-size:9px!important;
   }
   .commande .grey.blue + .question + .grey.blue {
    max-width: 376px;
   }
   .fixednoanim.commande .grey.blue + .question + .grey.blue {
    max-width: 208px;
   }
   .account .filtres {
    display: none;
   }


}


// grands ecrans

@media screen and (min-width: @screen__ipad) {
   .block.block-customer-login,
   .block.block-new-customer {
    height: 400px;

   }
   .account .filtres {
    display: none;
   }
    .block-authentication .login .actions-toolbar > .secondary  {
      margin-top: -50px!important;
    }


     body:not(.cms-home):not(.cms-page-view):not(.contact-index-index) {
        #header {
          height: 90px!important;
          background:none!important;
        }
        .bigtitre {
          display:none;
        }
        .aspan, header .logo {
          position:fixed!important;
          top:0!important;
        }
        .delivery {

          position:fixed!important;
          margin-top:0px!important;
          top:0!important;
          transition:none!important;
          height: 91px!important;
          background:#f7f7f7!important;

           .loader {
             background:#f7f7f7!important;
           }


        }
   }
}


@media screen and (min-width: @screen__ipad) and (max-width: 1100px) {
  body, html {zoom:0.848;}
   .produits .filtres .filtre {
    font-size:12px;
  }
}

@media screen and (min-width: 1100px) and (max-width: 1200px) {
  body, html {zoom:0.9;}
}


@media screen and (min-width: 1200px) and (max-width: 1330px) {
  body, html {zoom:0.949;}
}

@media screen and (min-width: @screen__ipad) and (max-width: 1450px) {

   header .container .delivery.fixednoanim {
    max-width: 970px;

       .choix-livraison .btn-radio {
           margin-left: 0px;
       }
       .creneau1 .tooltip2 {
           margin-left: -51px;
       }
       .creneau2 .tooltip2 {
           margin-left: -112px;
       }
       .tooltip3 {
           margin-left: -172px;
       }
  }
  .fixednoanim .commande {
      max-width: 900px;
      margin-top: 2px;
  }
   .fixednoanim .commande .grey.blue {
      max-width: 250px;
      line-height: 40px;
  }
   .fixednoanim .commande .grey.blue + .question + .grey.blue {
      width: 66%;
      max-width: 250px;
    height: auto;
    line-height: 20px;
    padding-right: 20px;
  }
}

@media screen and (min-width: 1451px) and (max-width: 1550px) {

   header .container .delivery.fixednoanim {
    max-width: 1070px;
  }
  .fixednoanim .commande {
      max-width: 1000px;
      margin-top: 2px;
  }

   .fixednoanim .commande .grey.blue {
      max-width: 300px;
  }
  .commande .grey.blue + .question + .grey.blue {

     max-width: 420px!important;
  }

  .fixednoanim .commande .grey.blue + .question + .grey.blue {
     max-width: 320px!important;
    line-height: 20px;
  }

  .resetPopin {
    width: 615px!important;
     &._place {
      width: 434px!important;
    }
  }

    header .container .delivery.fixednoanim {
        .choix-livraison .btn-radio {
            margin-left: 40px;
        }
    }
}


@media screen and (min-width: 1551px) and (max-width: 1600px) {

   header .container .delivery.fixednoanim {
    max-width: 1170px;
    right:187px;
    &:after {

            top: 90px;
          }
  }
   .fixednoanim .commande .grey.blue + .question + .grey.blue {

      max-width: 380px;
      line-height: 20px;
  }

}


@media  screen and (min-width: 1330px) {
  body, html {zoom:1;}
  .tooltip .item.btn-success3d + span:hover:after { right:7.6%; }
  .produits .filtres .filtre {  padding: 22px 17.7px; }
}

@media screen and (min-width: 1001px){
  .productlayer .modal-inner-wrap .produit .content {top: 0px!important;}
}

@media screen and (min-width: 1601px) {

  header#header .logo.fixednoanim {
    max-width:none!important;
    width:15%!important;
    max-height:60px!important;
    border-top:14px solid #f7f7f7;
    max-width: 250px!important;
    border-bottom: 18px solid #f7f7f7;
    border-left:2em solid #f7f7f7!important;
    margin-left: 0px;
    }
     header#header .logo  {
      margin-left: 30px;
    }

   #header .container .delivery.fixednoanim {
   width: ~"calc(100% - 508px)";
   right:231px;
   max-width:none;
        .commande .grey.blue {
          max-width:310px!important;
          & +.question + .grey.blue {
             max-width: ~"calc(100% - 595px)!important";
          }
        }

  }
  .tooltip .item.btn-success3d + span:hover:after {
    right:7.6%;
  }

  #header .container .delivery {
        width:1440px;
        .commande .grey.blue {
          max-width:490px!important;
          & +.question + .grey.blue {
             max-width:500px!important;
          }
        }
          .resetPopin {
            width:665px;
              right: 185px;
          }
        .resetPopin._place {
          width:624px;
            right: 649px;
        }
      .resetPopin._when {
          right: 153px;
      }
   }
    #header .container .links .aspan {
        width: 232px!important;
    }
    #header .container .links .aspan.fixednoanim {
        width: 229px!important;
    }
    #header .container .links a:last-child,
    #header .container .links.fixednoanim {
        width: 205px!important;
    }

    #header {
        .container {
            .delivery.fixednoanim {
                .resetPopin._place {
                    right: unset;
                    left: 0px;
                    width: 444px;
                }
                .resetPopin._when {
                    width: ~"calc(100% - 597px)";
                }
            }
        }
    }

}

@media screen and (min-width: 1800px) {


    h2 {
      font-size: 33px!important;
      letter-spacing: 5px;
    }
     .commande {
    width: ~"calc(100% - 260px)!important";

    .grey.blue {
      font-size: 17px;
    }
   }
   .smallcart {
    width:242px;
    margin-top: -1px;
    .total {
      font-size:30px;
      span {
        font-size:15px;
      }
    }
    img {
      width:30px;
    }
    .count {
      font-size: 20px;
      height: 32px;
      width:34px;
      border-radius: 22px;
    }

   }
 .minicart-wrapper .action.showcart {
  margin-top: -4px;
 }
 .produits{
    h2 {
      font-size: 33px;
    }
  .categorie .container{

    .produit {
     height:515px;
     .desc {
       height: 62px;
       overflow:hidden;
     }
      &.cms h3 {
        font-size:32px;
      }
      .pic img {
      width: 30px;
     }
     h4 {
      padding-right: 10px;
     }
     .no-delivery h5 {
      font-size:13px;
     }
     .no-delivery img  {
      margin-top:3px;
     }
     .addtocart div.plus {
      margin-right: 13px;
     }
     }
  }
  .filtres .filtre {
    font-size:14px;
    padding: 20px 17.7px 21px;
    &.last {
      margin-left:22px;
    }
  }

}
 #header .container .links a:last-child img {
   height:40px;
 }
 .oldadresses  {
    margin-left:312px;
 }

 #header .container .delivery {
  .question {
    font-size: 20px;
  }
  .step2 {
    .question {
      padding-right: 8px;
    }
    .choix-livraison label {
      font-size: 17px;
    }
    .creneau2 {
      padding-left:10px;
    }
    .creneau3 {
      padding-left:17px;
    }
  }
  .commande .grey.blue {
          max-width:450px!important;
          & +.question + .grey.blue {
             max-width:420px!important;
          }
        }
        .resetPopin  {
            width:602px;
            right: 242px;
          }

          .resetPopin._place {
          width:588px;
        }
     .resetPopin._when {
         right: 242px;
         width: 610px;
     }
 }
   #header .container .delivery.fixednoanim {

           .block-minicart {
            margin: -1px 17px;
          }

           .commande .grey.blue {
          max-width:430px!important;
          & +.question + .grey.blue {
              max-width: ~"calc(100% - 723px)!important";
          }
        }


   }

    #header {
        .container {
            .delivery.fixednoanim {
                .resetPopin._place {
                    right: unset;
                    left: 0px;
                    width: 568px;
                }
                .resetPopin._when {
                    width: ~"calc(100% - 809px)";
                }
            }
        }
    }

}

// fix iphone zoom on inputs & scroll
@media only screen and (-webkit-min-device-pixel-ratio:2) {

  .noscroll { overflow: hidden; }
    @supports (-webkit-overflow-scrolling: touch) {
      #header .delivery .step1  .adresse,
      #header .delivery .step1 .adresse:focus,
      #header .delivery .step1 .adresse::placeholder,
      select:focus,
      textarea:focus,
      input[type='text']:focus,
      input[type='text']  {
        font-size: 16px!important;
        }
    }
  }
 @media only screen and (min-device-width : 300px) and (max-device-width : 320px)   {
      #header .delivery  input.adresse,
      #header .delivery  input.adresse::placeholder {  font-size: 11px!important; }
    }
     @media only screen and (min-device-width : 321px) and (max-device-width : 359px)   {
      #header .delivery  input.adresse,
      #header .delivery  input.adresse::placeholder {  font-size: 12px!important;}
    }
    @media only screen and (min-device-width : 360px) and (max-device-width : 370px)  {
      #header .delivery  input.adresse,
      #header .delivery  input.adresse::placeholder {  font-size: 13px!important;}
    }
     @media only screen and (min-device-width : 370px) and (max-device-width : 390px)  {
      #header .delivery  input.adresse,
      #header .delivery  input.adresse::placeholder {  font-size: 14px!important;}
    }
    @media only screen and (min-device-width : 391px) and (max-device-width : 410px)  {
      #header .delivery  input.adresse,
      #header .delivery  input.adresse::placeholder {  font-size: 15px!important;}
    }

//ipad

@media only screen and (min-device-width : @screen__ipad) and (max-device-width : 1024px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
  body, html {
    zoom:0.75;
  }

.iwd_opc_wrapper .iwd_opc_alternative_wrapper #checkout-payment-method-load .payment-method .payment-method-content iframe {
    width: 930px!important;
}
.produits .categorie .container .produit.added div.visu div:before {
  zoom:0.8!important;
}
.produits .categorie .container .produit.cms p {
    background-size: contain;
    background-repeat: no-repeat;
}
.iwd_main_wrapper .field .iwd_opc_select_container.selected .iwd_opc_select_option.selected:after {
  top:-25px!important;
}
.password-bubble {
    margin-left: 220px!important;
    width: 530px!important;
     li:before {
      width:15px!important;
      height:15px!important;
    }
}
  .block.crosssell {
      margin-top:300px!important;
  }

  footer .bloc,  footer .bloc a {
    font-size: 10px!important;
    line-height: 25px!important;
  }
  .login-container .actions-toolbar {
    width:75%!important;
  }
  .login-container .field label {
    font-size:14px;
    white-space: nowrap;
    width:32%;
  }
  .block.block-customer-login {
    height: 500px!important;
  }
  .account-nav .item a {
    font-size: 13px;
  }
  .account h3.box-title > span {
    font-size: 18px;
  }
  .account .block-content * {
    font-size: 13px!important;
    line-height: 36px!important;
  }
  .account .block-actions a {
    font-size: 13px!important;
  }
  .account .page-title {
    font-size: 24px!important;
  }
  .account .action.edit {
    font-size: 13px!important;
  }
  .fieldset > .legend {
    font-size: 1.6rem;
  }
  .table-order-items td {
    font-size: 13px!important;
    &.actions a {
      white-space: nowrap;
    }
  }
  .form-address-edit ,
  .form-edit-account
   {
     .field {
      font-size: 14px;
      }
      .actions-toolbar {
        .action {
          &.primary {
            font-size: 1.4rem;
          }
        }
      }
   }
   .order-status,
   .order-date,
   .action.print {
    font-size: 13px;
   }
   .order-status::before {
    content:'(';
   }
   .order-status::after {
    content:')';
   }
  .tooltip {
    width: 320px!important;
    &::before {
      margin-top:-14px!important;
    }
    div.part {
       font-size: 12px;
    }
    div.part[value="0"] {
      font-size: 10px;
    }
  }
  .ll-skin-latoja {
    zoom:1.5;
  }
  .ll-skin-latoja .ui-datepicker th span {
    width:8px;
    &[title="mardi"] {
      width: 12px;
    }
    &[title="mercredi"] {
      width: 12px;
    }
    &[title="samedi"] {
      width: 9px;
    }
    &[title="dimanche"] {
      width: 10px;
    }
  }
  .ll-skin-latoja .ui-datepicker td a {
    font-size: 14px;
  }
  #header .logo.fixednoanim {
    border-left-width: 45px!important;
  }

  footer .bloc,
  footer .bloc a {
    font-size: 10px;
    line-height: 25px;

  }
  .closest h4 {
    font-size: 15px;
  }
  .closest input[type="submit"] {
    padding:0;
  }
  .closest input[type="text"] {
    font-size: 12px!important;
  }
  .cms-le-concept #header .bigtitre {
    width: 800px;
    left: 550px;
    top:176px;
  }
  .centertext {
    line-height: 35px;
    font-size: 16px;
  }
  .cms-left.w40:not(.concept3),
   .cms-left.w40:not(.concept4) {
    height: 800px;
    width:41%;
    div {
      width: 101%;
    }
    & + .cms-right {
      width:58%;
    }
   }
   .formulaire div input {
     width: ~"calc(100% - 200px)";
     height: 46px;
   }
    .formulaire div  {
    height: 56px;
    line-height: 56px;
   }
  .popup-authentication .block-authentication {
    height: 490px;
    .block-title {
      font-size: 2rem;
    }
    .block-content {
      font-size: 12px;
    }
    .block[class] {
      height: 500px;
    }
  }
   #header .container {
    .bigtitre:before {
      font-size:28px;
    }
    .bigtitre:after {
        font-size:18px;
      }
    .delivery  {
      width: 1340px;
      .resetPopin {
        line-height: 24px;
        right: 241px;
        width: 620px;
        &._place {
          width: 453px;
        }
      }
        #resetPopinDate {
            right: 320px;
            width: 470px;
        }
      .adresse {
        width:600px;
        height: 42px;
        margin-top: 26px;
      }
      .moment {
        font-size: 12px;
      }
      .question {
       font-size: 13px;
      }
      &.fixednoanim {
        max-width:1280px;
        right: 241px;
        .resetPopin {

        width: 609px;
        &._place {
         right: 241px;
        }
      }

      }
      .step1 .ok {
        font-size: 9px;
        padding-top: 30px;
        height: 61px;
      }
      .step1.OK + .step2 .ok {
        font-size: 9px;
      }
      .step2 .choix-livraison {
        top:21px;
        label:after {
          margin-top:11px;
        }
      }
      .step2 {
        .creneau1 {
          margin-left: 445px;
        }
        .creneau2 {
          margin-left: 705px;
        }
        .creneau3 {
          margin-left: 917px;
          .tooltip {
            width: 400px!important;
          }
        }
        &.noToday {
          .creneau2 {
          margin-left: 445px;
        }
        .creneau3 {
          margin-left: 657px;
          }
        }
      }



    }

    .links {

      .aspan {
        width:238px;
        z-index:9;
        a {
          width: 228px;
          padding:0 15px 0 0;
        }
        a.close {
          position:absolute;
          width: 40px!important;
          right: -1px;
          &:after {
            font-size:27px;
          }
        }
      }
        a {
        font-size:9px!important;
       }
     }
   }
   .delivered .logos {
    transition:none;
   }
   .commande, .fidexnoanim .commande {
    width:~"calc(100% - 255px)";
   }

   .commande .grey.blue {
    font-size: 13px;
   }
   .fixednoanim .commande  {
    max-width: 1030px;
   }
   .fixednoanim .commande .grey.blue {
    line-height: 23px;
    max-width: 280px;
   }
   .commande .grey.blue + .question + .grey.blue {
    max-width: 400px;
    &:after {
      right:-22px;
    }
   }
   .fixednoanim .commande .grey.blue + .question + .grey.blue {
    max-width: 380px;
   }
   .commande .grey.blue:after {
    height:14.5px;
   }
   .produits .filtres {
    height: 63px;

      .filtre {
      padding:19px 17.7px;
      &:not(.selected):hover {
        color:@lf-blue!important;
        background:unset!important;
        &:after {
          background:transparent;
        }
      }
      &.last:before {
        margin-top:1px;
      }
      &.last:not(.selected):hover:after {
        background:transparent;
        color:@lf-blue;
      }
      &.last .tooltip {
        width:400px!important;
        left:-255px;
        h5 {
          margin-bottom: 10px;
        }
        .item {
          height: 40px;
          width: 372px;
          &:hover {
            background:transparent;
          }
        }
      }
     }
   }
   .fixednoanim.commande .grey.blue + .question + .grey.blue {
    max-width: 208px;
   }
   .productlayer, .productlayer .modal-inner-wrap {
    max-width: 1000px;
   }
   .productlayer .modal-inner-wrap  .action-close {
       left:950px;
    }
   .productlayer .modal-inner-wrap  .action-close:before {
        font-size: 31px!important;
        margin-left:-6.5px!important;
    }

     .productlayer .modal-inner-wrap {
      .produit {
        /*width: 962px;*/
      }
     }
    .smallcart {
      width: 242px;
    }
    .smallcart .count {
      width: 32px;
      height: 30px;
      border-radius: 16px;
    }
    .minicart-wrapper {
      .action.close {
        right: 6px;
      }
      .block-minicart {
        width:450px!important;
         .product-item-details {
          font-size: 13px;
          .details-qty {
            vertical-align: bottom;
            min-width: 42px;
            width: auto;
          }
        }
        .product-item-name {
          width: 320px;
        }
        .subtitle {
           width:400px!important;
           font-size: 13px!important;
        }
      }
        .action.showcart {
        margin-top: -8px;
      }
    }
    .order_minimum_amount {
      font-size: 11px;
    }
   .produits {
    h2 {
      font-size: 24px;
    }
    .categorie .container {
      max-width: 1800px;
      .produit {
      width:23.5%;
      height: 635px;
      margin-bottom: 20px;
      &.added div.visu div {
        font-size:24px!important;
        &:after {
          display:none;
        }
      }
        &:hover div.visu div {
          line-height:29px;
          &:not(.hovered) {
            &:before {
              font-size:40px!important;
              line-height:25px;
            }
            &:after {
              margin-left:6px;
            }
          }
        }
      .desc {
        font-size: 12px;
        line-height: 20px;
      }
      .pic img {
        width: 40px;
      }
      .tt:before {
        top:73px;
      }
      .tt:after {
        top:58px;
      }
      .content {
        height: 310px;
      }

      &.added div.visu div:before {
        font-size: 39px!important;
        height: 41px;
        margin-top: 5px;
        line-height: 27px;
        vertical-align: top;
      }
       div.visu div:after,
      &:hover div.visu div:after {
        display: none;
      }
      &.added  div.visu div:before,
      &.added:hover div.visu div:before {
         font-size: 39px!important;
        height: 41px;
        margin-top: 5px;
        line-height: 27px;
        vertical-align: top;
        display: block;
      } &:hover div.visu div:not(.hovered):before {
        line-height: 26px;
      }
        &.added:hover div.visu div:not(.hovered):before {
          line-height: 35px;
        }
      &.added:active div.visu div:before {
         font-size: 26px!important;
        height: 41px;
        margin-top: 5px;
        line-height: 27px;
        vertical-align: top;
        transform:none!important;
        display: block;
      }

      &.added:hover div.visu div:after {
        display: inline-block;
        width: 3px;
        height: 30px;
        background: white;
        content:'';
        transform: rotate(-50deg);
        position: absolute;
        margin-left: 4px;
        margin-top: 34px;
      }
      &:active div.visu div:after,
      &.added:active div.visu div:after {
        display: none;
      }
      &.added div.visu div {
        font-size: 18px!important;
      }
      .addtocart {
        font-size: 16px;
        .quantity {
          margin-top: -15px;
          input {
            height: 45px;
            width: 64px;
            vertical-align: middle;
          }
        }
        &.prixbarre {
          .quantity {
            margin-top: -42px;
          }
          div.ttc, div.ht {
            top: -20px!important;
          }
          div.barre {
            zoom:0.9;
            top:-20px!important;
            &:before {
              margin-top:8px;
            }
          }
        }
      }
        .smalldesc {
          height: 34px;
          line-height: 35px;
          font-size: 13px;
        }
        h4 {
          font-size: 15px;
          height: 60px;
          margin-bottom: 10px;
        }
        &.cms h3 {
          font-size: 18px;
        }
        .no-delivery h5 {
          font-size:7px;
        }
     }
     h4 {
      font-size:14px;
     }
   }
   }
   .productlayer.crosssell {
    max-width: 1404px;
   }
   .productlayer .modal-inner-wrap {
    &.crosssell {
      max-width: 1404px;
      cross .produit .addtocart img {
        margin:10px!important;
      }
      .cross .produit .addtocart div.ht,
      .cross .produit .addtocart div.ttc {
        font-size: 15px;
        margin-top: -20px!important;
      }
      .cross .produit .addtocart.prixbarre .quantity {
        margin-top: -46px!important;
      }
      .cross .produit .addtocart.prixbarre div.ht,
      .cross .produit .addtocart.prixbarre div.ttc {
        margin-top:8px!important;
      }
    }
    .gzoom, .gzoom .slides {
      width: 1000px;
      height: 750px;
    }
    .cross h3 {
      font-size: 16px;
    }
    .cross .sell .produit .no-delivery h5 {
      font-size:6px;
    }
    .cross .sell .produit .content h4 {
      font-size:15px;
      height: 57px;
    }
     cross .sell .produit  .addtocart {
       height: 55px!important;
       padding-top: 7px!important;
     }
   }
   .productlayer .modal-inner-wrap .produit {
    height: 100%;
    width:996px;
    .desc {
      line-height: 30px;
    }
    h4 {
      font-size:20px;
      height: 75px;
      max-width: 800px;
   }
   h5 {
    max-width: 430px!important;
   }
   .addtocart {
    height: 40px!important;
    padding-top: 7px!important;
    span {
      font-size: 8px!important;
    }
    div.ht, div.ttc {
      font-size: 24px;
      margin-top: -17px!important;
      &.barre {
        top: -14px!important;
      }
    }
   }
   .smalldesc {
    line-height: 25px;
    height: 30px;
   }
     .pic img{
      width:40px;
     }
   }
   .pac-item {
     padding:6px!important;
  }

   .action.update {
     font-size: 8px!important ;
     height: 68px!important;
     max-width: 415px!important;
   }

  .checkout-cart-index {
    .columns {
      max-width: 1760px!important;
    }
    .price {
      font-size:16px!important;
    }
    .action.continue {
      font-size:13px!important;
    }
    .cart.table-wrapper .cart.items .col {
      font-size:14px!important;
    }
    .cart.table-wrapper .cart.items .qty input {
      height: 45px!important;
      font-size:18px!important;
    }
    .cart.table-wrapper .cart.items .qty div {
     margin-top: -44px!important;
    }
    .cart.table-wrapper .item .col.item {
      min-height: 115px!important;
    }
    .cart.table-wrapper .cart.items .col.qty {
     padding-top: 42px!important;
    }
  }
   .tunnel {
        div {
            font-size:13px!important;
        }

    }
   .accept-cgv {
     max-width: 580px;
     font-size:11px;
   }
   .payment-method-content iframe {
     height: 800px!important;
   }
  .iwd_main_wrapper {
    max-width:1750px!important;
    .iwd_opc_review_total_cell {
      line-height: 30px!important;
    }


    .iwd_opc_field.iwd_opc_input,
    .field.iwd_opc_input,
    .iwd_opc_field .input-text,
     .field .input-text,
     {

        height: 67px !important;
    }



    #iwd_opc_review_totals {
      h3 {
        font-size:17px!important;
      }
    }
    button.iwd_opc_place_order_button {
      max-width:570px!important;
      background-position:17% 15px!important;
    }
    .iwd_opc_payment_column,
    .iwd_opc_column_name {
      font-size:15p!important;
      .field .input-text {
        height:57px!important;
        line-height:57px!important;
      }
      .field textarea.input-text {
        line-height:20px!important;
        height:140px!important;
      }
    }
  }
  .shipping-address-button-container {
    margin-top:50px!important;
  }
  .selectize-control.single .item {
  line-height:30px!important;
  }
  .cart-container .checkout-methods-items .action.primary {
    font-size:9px!important;
  }
  .cart.table-wrapper .product-item-name {
    font-size:16px!important;
  }
  .cart-summary .discount .content .btn {
    margin-top:-52px!important;
    font-size:13px!important;
  }
  #coupon_code {
    height:50px!important;
    line-height:50px!important;
  }
  .nbPiecesConvives {
    height:56px!important;
    width:56px!important;
    border-radius:50px!important;
    font-size:22px!important;
  }
  .guestCalculator {
    margin-top:220px!important;
    .guestCalculatorIn {
      font-size:14px!important;
      select {
      height:60px!important;
      }
      .nbPieces {
        font-size:20px!important;
      }
    }
  }
  .cart-container .action.primary[data-role=proceed-to-checkout] {
    background-position:80px 18px!important;
  }

}



body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}


.nodisplayportrait  {



}


.nodisplaypaysage  {

  body.handheld, html.handheld {
    zoom:0.75;
    .page-wrapper *,
    .modals-wrapper {
      visibility: hidden;
    }
    .noportrait {
      position:fixed;
      display: block;
      z-index: 9000;
      visibility: visible;
       pointer-events: unset;
     &:after {
        content:'Merci de consulter notre site en mode paysage';
         color:@lf-blue;
      font-size: 40px;
      text-align: center;
      font-family: 'DinProBold';
      position: relative;
      top:70%;
     }

    }
  }

}

@media only screen and (min-device-width: 370px) and (max-device-width : @screen__ipad)  {
  .checkout-cart-index .cart.main.actions {
      margin-top: 460px;
  }
}




//phone paysage : affichage blanc

@media only screen and (min-device-width : 567px) and (max-device-width : 1100px) {
  .nodisplayportrait;
  body {
    z-index:1;
  }
}

//iphone 10

@media only screen and (min-device-width : 812px) and (max-device-width : 812px)  and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 3) {
.nodisplayportrait;
body {
    z-index:2;
  }
}



//phone paysage :  affichage blanc

@media only screen and (max-device-width : 1270px) and (min-device-width : 700px) and (-webkit-min-device-pixel-ratio: 3)  and (orientation : landscape)  {
 .nodisplayportrait;
 body {
    z-index:3;
  }
}

@media only screen and (max-device-width : 1270px) and (min-device-width : 700px) and (-webkit-min-device-pixel-ratio: 4) and (orientation : landscape)  {
.nodisplayportrait;
body {
    z-index:4;
  }
}


// oneplus6

@media only screen and (min-device-width: 414px) and (max-device-width: 1270px) and (orientation: landscape) {
.nodisplayportrait;
body {
    z-index:5;
  }
}


// iphone 7 paysage

 @media only screen and (min-device-width: 414px) and (max-device-width: @screen__ipad) and (-webkit-min-device-pixel-ratio: 3) and (orientation: landscape) and (min-aspect-ratio: 16/9) {
 .nodisplayportrait;
 body {
    z-index:6;
  }
}


// iphone 7 portrait

 @media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (-webkit-min-device-pixel-ratio: 3) and (orientation: portrait) {

  .modal-popup.modal-slide.productlayer .produit h4 {
      font-size:26px;
  }

}

//ipad portrait :  affichage blanc

@media only screen and (max-device-width : 768px) and (min-device-width : 768px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
 .nodisplaypaysage;
 body {
    z-index:7;
  }
}

//ipad portrait :  affichage blanc

@media only screen and (max-device-width : 768px) and (min-device-width : 768px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {
 body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}
 body {
    z-index:77;
  }
body.handheld  .page-wrapper *,
   body.handheld  .modals-wrapper {
      visibility: visible!important;
    }
}



//  ipad 11 portrait

 @media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
 .nodisplaypaysage;
 body {
    z-index:8;
  }
}


//  ipad 12.9 portrait

 @media only screen and (min-device-width : 1023px) and (max-device-width : 1026px) and (orientation : portrait) and (-webkit-min-device-pixel-ratio: 2) {
.nodisplaypaysage;
 body {
    z-index:9;
  }
}


 //  ipad 11 portrait

 @media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {

body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}
body {
    z-index:10;
  }
body.handheld  .page-wrapper *,
   body.handheld  .modals-wrapper {
      visibility: visible!important;
    }
}


 //  ipad 11 portrait

 @media only screen and (min-device-width : 894px) and (max-device-width : 896px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2.5) {

body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}
body {
    z-index:10;
  }
body.handheld  .page-wrapper *,
   body.handheld  .modals-wrapper {
      visibility: visible!important;
    }
}


//  ipad 11 portrait

 @media only screen and (min-device-width : 833px) and (max-device-width : 835px) and (orientation : landscape) and (-webkit-min-device-pixel-ratio: 2) {

body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}
body {
    z-index:10;
  }
body.handheld  .page-wrapper *,
   body.handheld  .modals-wrapper {
      visibility: visible!important;
    }
}

//  ipad 12.9 portrait

 @media only screen and (min-device-width : 1023px) and (max-device-width : 1026px) and (orientation : landscape)  {

body.handheld .noportrait,
html.handheld .noportrait {
  display: none;
  z-index: -1;
  position: absolute;
   top:0;
    left:0;
    visibility: hidden;
    right: 0;
    left:0;
    pointer-events: none;
    height: 100%;
    opacity: 1;
   text-align: center;
   background: white url(../images/logob.svg) left 58% center no-repeat;
}
body.handheld  .page-wrapper *,
   body.handheld  .modals-wrapper {
      visibility: visible!important;
    }
body {
    z-index:11;
  }

}

.column:not(.sidebar-additional) {
  .form.contact {
    float: none;
    width: 100%!important;
  }
}

.cookie-status-message {
    display: none;
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .timeslotSelected {
        background: #fb7576;
        color: @color-white;
        align-content: center;
        text-align: left;
        display: flex;
        top: 196px;
        align-items: center;
        padding: 25px 0;
        right: 0;
        position: absolute;
        left: 43px;
        z-index: 50;

        &:before {
            content: ' ';
            height: 30px;
            background: #fb7576;
            width: 30px;
            top: -16px;
            z-index: 50;
            left: 50%;
            position: relative;
            transform: translate(-50%,-50%) rotate(45deg);
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__ipad) {
    .timeslotSelected {
        top: 64px;
        left: 0;
        right: 110px;

        &:before {
            left: 30px;
        }
    }
}

@media only screen and (min-device-width : 1450px) {
    .timeslotSelected {
        left: 50px;
    }
}

@media only screen and (min-device-width : 1930px) {
    .timeslotSelected {
        top: -27px;
        height: 41px;
        left: 105%;
        right: auto;
        width: 100%;

        &:before {
            left: 0;
            top: 34%;
        }
    }
}


@media screen and (max-width: 456px) {
    .minicart-link {
        display: none;
    }

    .overlaysmallcarticone {

        padding-top: 17px;
    }

    .minicart-wrapper {
        min-width: 100px;
    }

    .smallcart {
        width: 100px;
    }

    #header .div-logo {
        width: 280px;
        margin-left: 10px;
    }
}

@media screen and (min-width: 457px) {
    .overlaysmallcarticone {
        display: none;
    }
}

#adresse-input {
    border: none;
}

@media screen and (min-width: 1001px) and (max-width: 1799px) {
    header .container .delivery .resetPopin._place {
        width: calc(~'(100% - 157px)*0.49') !important;
    }
}

@media screen and (max-width: @screen__xs) {
    header {
        .logo {
            &.fixednoanim {
                max-width: 200px !important;
            }
        }
    }
}



