define([
    'jquery',
], function ($) {
    'use strict';

    return function () {
        $(document).ready(function () {
            var anchor = window.location.hash.substr(1);

            if (anchor) {
                var filterName = '.filtre[data-filter=".' + anchor + '"]';

                setTimeout(function () {
                    $(filterName).trigger('click');
                }, 2000);
            }
        });
    }
});