<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/* @var $block \Magento\Catalog\Block\Product\AbstractProduct */
?>

<?php
switch ($type = $block->getType()) {

    case 'related-rule':
        if ($exist = $block->hasItems()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getAllItems();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = false;
            $templateType = null;
            $description = false;
        }
        break;

    case 'related':
        /** @var \Magento\Catalog\Block\Product\ProductList\Related $block */
        if ($exist = $block->getItems()->getSize()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getItems();
            $limit = 0;
            $shuffle = 0;
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = false;
            $templateType = null;
            $description = false;
        }
        break;

    case 'upsell-rule':
        if ($exist = $block->hasItems()) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getAllItems();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();

            $showAddTo = false;
            $showCart = false;
            $templateType = null;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'upsell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Upsell $block */
        if ($exist = count($block->getItemCollection()->getItems())) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getItemCollection()->getItems();
            $limit = $block->getItemLimit('upsell');
            $shuffle = 0;

            $showAddTo = false;
            $showCart = false;
            $templateType = null;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'crosssell-rule':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = $block->hasItems()) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices:');
            $items = $block->getItemCollection();

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'crosssell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = count($block->getItems())) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices:');
            $items = $block->getItems();

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = true;
            $canItemsAddToCart = false;
        }
        break;

    case 'new':
        if ($exist = $block->getProductCollection()) {
            $type = 'new';
            $mode = 'grid';
            $type = $type . ' ' . $mode;

            $class = 'widget' . ' ' . $type;

            $image = 'new_products_content_widget_grid';
            $title = __('New Products');
            $items = $exist;

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = ($mode == 'list') ? true : false;
            $canItemsAddToCart = false;
        }
        break;

    default:
        $exist = null;
}
?>

<?php if ($exist): ?>


<?php if ($type == 'crosssell'): ?>


    <div class="block produits <?= /* @escapeNotVerified */
    $class ?>">


        <div class="categorie block-content content" aria-labelledby="block-<?= /* @escapeNotVerified */
        $class ?>-heading">
            <div class="container wrapper grid products-grid products-<?= /* @escapeNotVerified */
            $type ?>">
                <ol class="products list items product-items">
                    <?php $cpt = 1; ?>
                    <?php foreach ($items as $_item): ?>
                        <?php if ($_item->getFranchiseProduct()): ?>
                            <div class="produit produit<?= $cpt++; ?> <?= $block->getFilters($_item); ?>"
                                 data-id="<?= $_item->getId(); ?>">
                                <div class="visu">
                                    <?= $block->getImage($_item, 'category_page_list', ['class' => 'prod'])->toHtml() ?>
                                </div>
                                <?php if ($_item->getData('label')): ?>
                                    <label><?= $block->getImage($_item, 'category_page_list_label')->toHtml() ?></label>
                                <?php endif; ?>
                                <div class="content">

                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <?php if ($_item->getData('picto_filtre_' . $i)): ?>
                                            <?php
                                            $filterImage = $block->getImage($_item, 'category_page_list_filter_' . $i);
                                            ?>
                                            <div class="pic tt"
                                                 data-text="label du filtre <?= $_item->getData('picto_filtre_' . $i) ?>"><?= $filterImage->toHtml() ?></div>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                    <h4><?= $_item->getName(); ?></h4>
                                    <div class="smalldesc"><?= $_item->getShortDescription(); ?></div>
                                    <hr>
                                    <?php if ($showAddTo || $showCart): ?>
                                        <div class="product actions product-item-actions">
                                            <?php if ($showCart): ?>

                                                <div class="addtocart" data-id="<?= $_item->getId(); ?>"
                                                     style="display:block">
                                                    <?php if ($_item->getQtyInCart()): ?>
                                                        <div class="quantity">
                                                            <div class="moins">-</div>
                                                            <input class="qty" type="text"
                                                                   value="<?= $_item->getQtyInCart() ?>">
                                                            <div class="plus">+</div>
                                                        </div>
                                                    <?php else: ?>


                                                        <?php if ($_item->isSaleable()): ?>
                                                            <?php if ($_item->getTypeInstance()->hasRequiredOptions($_item)): ?>
                                                                <button
                                                                    data-mage-init='{"redirectUrl": {"url": "<?= /* @escapeNotVerified */
                                                                    $block->getAddToCartUrl($_item) ?>"}}'
                                                                    type="button"><img src="<?= /* @escapeNotVerified */
                                                                    $block->getViewFileUrl('images/basket.svg') ?>"
                                                                                       class="add"></button>
                                                            <?php else: ?>
                                                                <?php $postDataHelper = $this->helper('Magento\Framework\Data\Helper\PostHelper');
                                                                $postData = $postDataHelper->getPostData($block->getAddToCartUrl($_item), ['product' => $_item->getEntityId()])
                                                                ?>
                                                                <button
                                                                    data-post='<?= /* @escapeNotVerified */
                                                                    $postData ?>'
                                                                    type="button" title="<?= /* @escapeNotVerified */
                                                                __('Add to Cart') ?>"><img
                                                                        src="<?= /* @escapeNotVerified */
                                                                        $block->getViewFileUrl('images/basket.svg') ?>"
                                                                        class="add"></button>
                                                            <?php endif; ?>
                                                        <?php endif; ?>

                                                    <?php endif; ?>
                                                    <?php if ($_item->getFranchiseProduct()->getSpecialPriceHt() != 0 || $_item->getFranchiseProduct()->getSpecialPrice() != 0): ?>
                                                        <div
                                                            class="ht barre"><?= number_format($_item->getFranchiseProduct()->getSpecialPriceHt(), 2); ?>
                                                            <span>€ HT</span></div>
                                                        <div
                                                            class="ttc barre"><?= number_format($_item->getFranchiseProduct()->getSpecialPrice(), 2); ?>
                                                            <span>€ TTC</span></div>
                                                        <br>
                                                    <?php endif; ?>
                                                    <div
                                                        class="ht"><?= number_format($_item->getFranchiseProduct()->getPriceHt(), 2); ?>
                                                        <span>€ HT</span></div>
                                                    <div
                                                        class="ttc"><?= number_format($_item->getFranchiseProduct()->getPrice(), 2); ?>
                                                        <span>€ TTC</span></div>
                                                    <br>

                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <hr>
                                    <div class="desc"><?= $_item->getDescription(); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach ?>
                </ol>
            </div>
        </div>
    </div>


<?php else: ?>

<?php if ($type == 'related' || $type == 'upsell'): ?>
<?php if ($type == 'related'): ?>
<div class="block <?= /* @escapeNotVerified */
$class ?>" data-mage-init='{"relatedProducts":{"relatedCheckbox":".related.checkbox"}}'
     data-limit="<?= /* @escapeNotVerified */
     $limit ?>" data-shuffle="<?= /* @escapeNotVerified */
$shuffle ?>">
    <?php else: ?>
    <div class="block <?= /* @escapeNotVerified */
    $class ?>" data-mage-init='{"upsellProducts":{}}' data-limit="<?= /* @escapeNotVerified */
    $limit ?>" data-shuffle="<?= /* @escapeNotVerified */
    $shuffle ?>">
        <?php endif; ?>

        <div class="block <?= /* @escapeNotVerified */
        $class ?>">
            <?php endif; ?>
            <div class="block-title title">
                <strong id="block-<?= /* @escapeNotVerified */
                $class ?>-heading" role="heading" aria-level="2"><?= /* @escapeNotVerified */
                    $title ?></strong>
            </div>
            <div class="block-content content" aria-labelledby="block-<?= /* @escapeNotVerified */
            $class ?>-heading">
                <?php if ($type == 'related' && $canItemsAddToCart): ?>
                    <div class="block-actions">
                        <?= /* @escapeNotVerified */
                        __('Check items to add to the cart or') ?>
                        <button type="button" class="action select" role="select-all"><span><?= /* @escapeNotVerified */
                                __('select all') ?></span></button>
                    </div>
                <?php endif; ?>
                <div class="products wrapper grid products-grid products-<?= /* @escapeNotVerified */
                $type ?>">
                    <ol class="products list items product-items">
                        <?php $iterator = 1; ?>
                        <?php foreach ($items as $_item): ?>
                            <?php $available = ''; ?>
                            <?php if (!$_item->isComposite() && $_item->isSaleable() && $type == 'related'): ?>
                                <?php if (!$_item->getRequiredOptions()): ?>
                                    <?php $available = 'related-available'; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if ($type == 'related' || $type == 'upsell'): ?>
                                <?= /* @escapeNotVerified */
                                ($iterator++ == 1) ? '<li class="item product product-item" style="display: none;">' : '</li><li class="item product product-item" style="display: none;">' ?>
                            <?php else: ?>
                                <?= /* @escapeNotVerified */
                                ($iterator++ == 1) ? '<li class="item product product-item">' : '</li><li class="item product product-item">' ?>
                            <?php endif; ?>
                            <div class="product-item-info <?= /* @escapeNotVerified */
                            $available ?>">
                                <?= /* @escapeNotVerified */
                                '<!-- ' . $image . '-->' ?>
                                <a href="<?= /* @escapeNotVerified */
                                $block->getProductUrl($_item) ?>" class="product photo product-item-photo">
                                    <?= $block->getImage($_item, $image)->toHtml() ?>
                                </a>
                                <div class="product details product-item-details">
                                    <strong class="product name product-item-name"><a class="product-item-link"
                                                                                      title="<?= $block->escapeHtml($_item->getName()) ?>"
                                                                                      href="<?= /* @escapeNotVerified */
                                                                                      $block->getProductUrl($_item) ?>">
                                            <?= $block->escapeHtml($_item->getName()) ?></a>
                                    </strong>

                                    <?= /* @escapeNotVerified */
                                    $block->getProductPrice($_item) ?>

                                    <?php if ($templateType): ?>
                                        <?= $block->getReviewsSummaryHtml($_item, $templateType) ?>
                                    <?php endif; ?>

                                    <?php if ($canItemsAddToCart && !$_item->isComposite() && $_item->isSaleable() && $type == 'related'): ?>
                                        <?php if (!$_item->getRequiredOptions()): ?>
                                            <div class="field choice related">
                                                <input type="checkbox" class="checkbox related"
                                                       id="related-checkbox<?= /* @escapeNotVerified */
                                                       $_item->getId() ?>" name="related_products[]"
                                                       value="<?= /* @escapeNotVerified */
                                                       $_item->getId() ?>"/>
                                                <label class="label" for="related-checkbox<?= /* @escapeNotVerified */
                                                $_item->getId() ?>"><span><?= /* @escapeNotVerified */
                                                        __('Add to Cart') ?></span></label>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if ($showAddTo || $showCart): ?>
                                        <div class="product actions product-item-actions">
                                            <?php if ($showCart): ?>
                                                <div class="actions-primary">
                                                    <?php if ($_item->isSaleable()): ?>
                                                        <?php if ($_item->getTypeInstance()->hasRequiredOptions($_item)): ?>
                                                            <button class="action tocart primary"
                                                                    data-mage-init='{"redirectUrl": {"url": "<?= /* @escapeNotVerified */
                                                                    $block->getAddToCartUrl($_item) ?>"}}' type="button"
                                                                    title="<?= /* @escapeNotVerified */
                                                                    __('Add to Cart') ?>">
                                                                <span><?= /* @escapeNotVerified */
                                                                    __('Add to Cart') ?></span>
                                                            </button>
                                                        <?php else: ?>
                                                            <?php $postDataHelper = $this->helper('Magento\Framework\Data\Helper\PostHelper');
                                                            $postData = $postDataHelper->getPostData($block->getAddToCartUrl($_item), ['product' => $_item->getEntityId()])
                                                            ?>
                                                            <button class="action tocart primary"
                                                                    data-post='<?= /* @escapeNotVerified */
                                                                    $postData ?>'
                                                                    type="button" title="<?= /* @escapeNotVerified */
                                                            __('Add to Cart') ?>">
                                                                <span><?= /* @escapeNotVerified */
                                                                    __('Add to Cart') ?></span>
                                                            </button>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <?php if ($_item->getIsSalable()): ?>
                                                            <div class="stock available">
                                                                <span><?= /* @escapeNotVerified */
                                                                    __('In stock') ?></span></div>
                                                        <?php else: ?>
                                                            <div class="stock unavailable">
                                                                <span><?= /* @escapeNotVerified */
                                                                    __('Out of stock') ?></span></div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($showAddTo): ?>
                                                <div class="secondary-addto-links actions-secondary"
                                                     data-role="add-to-links">
                                                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                                        <?= $addToBlock->setProduct($_item)->getChildHtml() ?>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?= ($iterator == count($items) + 1) ? '</li>' : '' ?>
                        <?php endforeach ?>
                    </ol>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
