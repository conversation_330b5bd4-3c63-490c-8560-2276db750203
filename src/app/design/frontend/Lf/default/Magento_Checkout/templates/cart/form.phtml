<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**  @var $block \Magento\Checkout\Block\Cart\Grid */
?>
<?php $mergedCells = ($this->helper('Magento\Tax\Helper\Data')->displayCartBothPrices() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form action="<?= /* @escapeNotVerified */ $block->getUrl('checkout/cart/updatePost') ?>"
          method="post"
          id="form-validate"
          data-mage-init='{"validation":{}}'
          class="form form-cart">
    <?= $block->getBlockHtml('formkey') ?>
    <div class="cart table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar" data-attribute="cart-products-toolbar-top"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
 
        <?php if ($block->getContinueShoppingUrl()): ?>
            <a class="btn btn-8g action continue"
               href="<?= $block->escapeUrl($block->getContinueShoppingUrl()) ?>"
               title="Continuer mes achats"><span>Continuer mes achats</span>
            </a>
        <?php endif; ?>

        <div class="tunnel"> 
            <div>
                <label class="btn-radio">
                    <input type="radio" name="livraison" value="1" id="tunnel1" checked>
                    <svg width="26px" height="26px" viewBox="0 0 20 20">
                        <circle cx="10" cy="10" r="9"></circle>
                        <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                        <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
                    </svg>
                    </label>
                    <br>
                    Panier
            </div>
            <div>        
                <label class="btn-radio">
                    <input type="radio" name="livraison" value="2" id="tunnel2" disabled>
                    <svg width="26px" height="26px" viewBox="0 0 20 20">
                        <circle cx="10" cy="10" r="9"></circle>
                        <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                        <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
                    </svg>
                    </label>
                    <br> 
                   Livraison
            </div>
            <div>
                <label class="btn-radio">
                    <input type="radio" name="livraison" value="3" id="tunnel3" disabled>
                    <svg width="26px" height="26px" viewBox="0 0 20 20">
                        <circle cx="10" cy="10" r="9"></circle>
                        <path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="inner"></path>
                        <path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="outer"></path>
                    </svg>
                   </label> 
                   <br>
                   Confirmation
            </div> 
        </div>    

        <?= $block->getChildHtml('shopping.cart.table.before') ?>

        <table id="shopping-cart-table"
               class="cart items data table"
               data-mage-init='{"shoppingCart":{"emptyCartButton": "action.clear",
               "updateCartActionContainer": "#update_cart_action_container"}}'>
            <caption role="heading" aria-level="2" class="table-caption"><?= /* @escapeNotVerified */ __('Shopping Cart Items') ?></caption>
            <thead>
                <tr>
                    <th class="col item" scope="col"><span><?= /* @escapeNotVerified */ __('Item') ?></span></th>
                    <th class="col price" scope="col"><span><?= /* @escapeNotVerified */ __('Price') ?></span></th>
                    <th class="col qty" scope="col"><span><?= /* @escapeNotVerified */ __('Qty') ?></span></th>
                    <th class="col subtotal" scope="col"><span><?= /* @escapeNotVerified */ __('Subtotal') ?></span></th>
                </tr>
            </thead>
            <?php foreach ($block->getItems() as $_item): ?>
                <?= $block->getItemHtml($_item) ?>
            <?php endforeach ?>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar" data-attribute="cart-products-toolbar-bottom"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
    </div>
    <div class="cart main actions"> 
        <button type="submit"
                name="update_cart_action"
                data-cart-empty=""
                value="empty_cart"
                title="<?= $block->escapeHtml(__('Clear Shopping Cart')) ?>"
                class="btn btn-8g action clear" id="empty_cart_button">
            <span><?= /* @escapeNotVerified */ __('Clear Shopping Cart') ?></span>
        </button>
        <button type="submit"
                id="btn_update_cart"
                name="update_cart_action"
                data-cart-item-update=""
                value="update_qty"
                title="<?= $block->escapeHtml(__('Update Shopping Cart')) ?>"
                class="btn btn-8g action update">
            <span><?= /* @escapeNotVerified */ __('Update Shopping Cart') ?></span>
        </button>
        <input type="hidden" value="" id="update_cart_action_container" data-cart-item-update=""/>
    </div>
</form>
<?= $block->getChildHtml('shopping.cart.table.after') ?>
