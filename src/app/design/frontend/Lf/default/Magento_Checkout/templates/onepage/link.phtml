<?php

/** @var $block Lf\Checkout\Block\Link */

// @codingStandardsIgnoreFile
?>
<?php if ($block->isPossibleOnepageCheckout()):?>
    
    <button type="button"
            data-role="proceed-to-checkout"
            title="<?= /* @escapeNotVerified */ __('Proceed to Checkout') ?>"
            data-mage-init='{"Magento_Checkout/js/proceed-to-checkout":{"checkoutUrl":"<?= /* @escapeNotVerified */ $block->getCheckoutUrl() ?>"}}'
            class="action primary checkout<?= ($block->isDisabled()) ? ' disabled' : '' ?>"
            <?php if ($block->isDisabled()):?>disabled="disabled"<?php endif; ?>>
        <span><?= /* @escapeNotVerified */ __('Proceed to Checkout') ?></span>
    </button>
<?php else: ?>
    <?php if (!$block->isMinimumOrderAmountReached()):?>
            <div class="order_minimum_amount">
                <?= __('Vous n\'avez pas atteint le minimum de commande de %1€', $block->getMinimumOrderAmount()); ?>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function(){ 
                    document.querySelector(".cart.main.actions").style.display='block';
                }, false);
            </script>
    <?php endif?>
<?php endif?>
