// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@block-auth__dropdown__padding: @indent__m;
@block-auth__dropdown__background-color: @color-white;
@block-auth__or-label__size: 36px;
@block-auth__width: 0;
@block-auth__border: 1px solid @color-gray-light3;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .authentication-dropdown {
        box-sizing: border-box;

        .modal-inner-wrap {
            padding: @block-auth__dropdown__padding;
        }
    }
    .authentication-wrapper {
        float: right;
        margin-top: -1.5*@indent__xl;
        max-width: 50%;
        position: relative;
        z-index: 1;

        ._has-auth-shown & {
            z-index: @modal__z-index;
        }
    }

    .block-authentication {
        .block-title {
            .lib-font-size(@h3__font-size);
            border-bottom: 0;
            margin-bottom: @indent__m;

            strong {
                font-weight: @font-weight__light;
            }
        }

        .field {
            .label {
                font-weight: @font-weight__regular;
            }
        }

        .actions-toolbar {
            margin-bottom: @indent__xs;

            > .secondary {
                padding-top: @indent__m;
                text-align: left;
            }
        }

        .action.action-register,
        .action.action-login {
            &:extend(.abs-button-l all);
        }

        .block[class] {
            margin: 0;

            ul {
                list-style: none;
                padding-left: @indent__s;
            }

            .field {
                .control,
                .label {
                    float: none;
                    width: auto;
                }
            }

            & + .block {
                border-top: 1px solid @color-gray-light5;
                margin-top: @indent__xl;
                padding-top: @indent__xl;
                position: relative;

                &::before {
                    .lib-css(height, @block-auth__or-label__size);
                    .lib-css(line-height, @block-auth__or-label__size - 2px);
                    .lib-css(margin, -(@block-auth__or-label__size/2 + 1px) 0 0 -(@block-auth__or-label__size / 2));
                    .lib-css(min-width, @block-auth__or-label__size);
                    background: @color-white;
                    border: 1px solid @color-gray-light5;
                    border-radius: 50%;
                    box-sizing: border-box;
                    color: @color-gray-light5;
                    content: attr(data-label);
                    display: inline-block;
                    left: 50%;
                    letter-spacing: normal;
                    padding: 0 .2rem;
                    position: absolute;
                    text-align: center;
                    text-transform: uppercase;
                    top: 0;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .authentication-dropdown {
        .lib-css(background-color, @block-auth__dropdown__background-color);
        .lib-css(border, @block-auth__border);
        -webkit-transform: scale(1,0);
        -webkit-transform-origin: 0 0;
        -webkit-transition: -webkit-transform linear .1s, visibility 0s linear .1s;
        position: absolute;
        text-align: left;
        top: 100%;
        transform: scale(1,0);
        transform-origin: 0 0;
        transition: transform linear .1s, visibility 0s linear .1s;
        visibility: hidden;
        width: 100%;

        &._show {
            .lib-css(z-index, @dropdown-list__z-index);
            -webkit-transform: scale(1,1);
            -webkit-transition: -webkit-transform linear .1s, visibility 0s linear 0s;
            transform: scale(1,1);
            transition: transform linear .1s, visibility 0s linear 0s;
            visibility: visible;
        }
    }

    .authentication-wrapper {
        .lib-column-width(@checkout-sidebar__columns);
        text-align: right;
    }

    .block-authentication {
        .block-title {
            .lib-font-size(@h2__font-size);
            border-bottom: 0;
            margin-bottom: @indent__m;
        }

        .actions-toolbar {
            > .primary {
                display: inline;
                float: right;
                margin-right: 0;
                .action {
                    margin-right: 0;
                }
            }

            > .secondary {
                float: left;
                margin-right: 2rem;
                padding-top: 1rem;
            }
        }
    }

    .popup-authentication {
        &.modal-popup {
            &.modal-slide .modal-inner-wrap {
                min-width: @screen__m;
                width: 1200px;
                overflow:visible;
            }
            .modal-content {
                overflow:visible;
            }
            .action-close:before {
                color:white;
                background: @lf-blue;
                width: 50px;
                height: 50px;
                font-size: 50px;
                line-height: 50px;
                border-radius: 25px;
                margin-right: -45px;
                margin-top: -35px;

                 @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
                    margin-right: -15px;
                    margin-top: -5px;
                  }
            }
        }
        .block-authentication {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction(row);
            &.popindevis {

                button {
                    padding: 10px 28px!important;
                    font-size: 12px;
                    font-family: 'DinProBold';
                    letter-spacing: 3px;
                    color: @lf-blue !important;
                    border: 2px solid @lf-blue;
                }
                .block-new-customer {
                    display: none;
                }
                .devis {

                    margin-top: -62px;
                    margin-left: -31px;
                    width: 850px;
                    color:@lf-blue;
                    font-family: Eczar;
                    height: 800px;
                    h3 {
                        font-size: 24px;
                        text-align: left;
                        margin-bottom: 40px;
                        margin-top: 70px;
                    }
                    img {
                        float: left;
                        margin-right: 50px;
                        margin-top: -50px;
                    }
                    .li {
                        margin-bottom: 30px;
                        font-size: 16px;
                    }
                    .li:before {
                        content: attr(data-text);
                        font-size: 20px;
                        font-family: DinProBlack;
                        display: inline-block;
                        height: 70px;
                        width: 25px;
                        float: left;
                        margin-top: -7px;
                    }
                 }
                 .devis2 {
                    position: absolute;
                    width: 100%;
                    text-align: center;
                    margin-top: -350px;
                    .guestmobile {
                        display: none;
                    }
                    button {
                        margin-left: 5px;
                        margin-right: 5px;
                    }
                 }
                 .block {
                    border:0px!important;
                    background:@lf-blue;
                    padding:20px 40px;
                    margin-top: -42px;
                    margin-right: -30px;
                    height: 445px;
                    h3 {
                        color:white;
                         font-size: 24px;
                        text-align: left;
                        margin-top:30px;
                        margin-bottom:0px;
                    }
                    .actions-toolbar {
                        margin-top: 0;
                        .primary {
                            float: right;
                        }
                        button {
                            width:220px!important;
                        }
                    }
                    .secondary span {
                        color:white;
                    }
                 }
                 .block:before {
                    display: none;
                }

            }
        }

        .block[class],
        .form-login,
        .fieldset,
        .block-content {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction(column);
            .lib-vendor-prefix-flex-grow(1);
        }

        .block[class] {
            box-sizing: border-box;
            float: left;
            padding: @indent__s @indent__l 0 0;
            width: 50%;

            & + .block {
                border-left: 1px solid @color-gray-light5;
                border-top: 0;
                margin: 0;
                padding: @indent__s 0 0 @indent__xl;

                &::before {
                    left: 0;
                    top: 50%;
                }
            }
        }

        .actions-toolbar {
            margin-bottom: 0;
            margin-top: auto;
        }
        .popindevis .actions-toolbar {
            margin-top: unset;
        }
    }
}















@media only screen and (min-width: 300px) and (max-width: 1000px) {
    .popup-authentication {
         .modal-header {
            position: fixed;
            right: 0;
            z-index: 1;
            top:0;
        }
        &.modal-popup {
            &.modal-slide .modal-inner-wrap {

                overflow:visible;
            }
            .modal-content {
                background: #F4F4F4;
                overflow-y:auto;
                overflow-x:hidden;
                width: ~"calc(100% - 60px)";
            }
            .action-close:before {
                color:white;
                background: @lf-blue;
                width: 50px;
                height: 50px;
                font-size: 50px;
                line-height: 50px;
                border-radius: 25px;
                margin-right: -13px;
                margin-top: -5px;
                position: relative;
                z-index: 1;
            }
        }
        .block-authentication {
            .block-title {
                   padding-right: 30px;
                }
            &.popindevis {

                button {
                    padding: 10px 28px!important;
                    font-size: 12px;
                    font-family: 'DinProBold';
                    letter-spacing: 3px;
                    color: @lf-blue !important;
                    border: 2px solid @lf-blue;
                }
                .block-new-customer {
                    display: none;
                }

                .devis {

                    margin-top: -112px;
                    margin-left: -30px;
                    width: ~"calc(100% + 60px)";
                    color:@lf-blue;
                    font-family: Eczar;
                    height: 1600px;
                    h3 {
                        font-size: 24px;
                        text-align: center;
                        margin-bottom: 60px;
                        margin-top: -70px;
                        width: 100%;
                        color:white;
                        z-index: 1;
                        position: relative;
                    }
                    img {
                        float: none;
                        margin-right: 50px;
                        margin-top: 0px;
                        opacity: 0.5;
                    }
                    .li {
                        margin-bottom: 30px;
                        font-size: 18px;
                        margin-left: 20%;
                    }
                    .li:before {
                        content: attr(data-text);
                        font-size: 20px;
                        font-family: DinProBlack;
                        display: inline-block;
                        height: 70px;
                        width: 25px;
                        float: left;
                        margin-top: -5px;
                    }
                 }
                 .devis2 {
                    width: ~"calc(100% + 60px)";
                    text-align: center;
                    margin-top: -400px;
                    padding-bottom: 30px;
                    margin-left: -30px;
                    background: #F4F4F4;
                    .guest {
                         display: none;
                    }
                    button {
                        margin-left: 5px;
                        margin-right: 5px;
                        width: 260px;
                        margin-bottom: 10px;
                        height: 50px;
                    }
                 }
                 .block {
                    border:0px!important;
                    background:@lf-blue;
                    padding:20px 40px;
                    margin-top: -1330px;
                    margin-left: -30px;
                    height: 420px;
                    width: 100%;
                    h3 {
                        color:white;
                         font-size: 22px;
                        text-align: left;
                        margin-top:30px;
                        margin-bottom:0px;
                        white-space: nowrap;
                    }
                    .actions-toolbar {
                        margin-top: 0;

                        button {
                            width:220px!important;
                            height: 50px;
                        }
                    } .secondary {
                        padding-top:10px!important;
                    }
                    .secondary span {
                        color:white;
                    }
                 }
                 .block:before {
                    display: none;
                }

            }
        }
    }
 }
