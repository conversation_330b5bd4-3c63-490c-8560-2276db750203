<div class="block-content">
    <button type="button"
            id="btn-minicart-close"
            class="action close"
            data-action="close"
            data-bind="attr: { title: $t('Close') }">
        <span translate="'Close'"></span>
    </button>

    <if args="getCartParam('summary_count')">
        <strong class="subtitle" translate="'Recently added item(s)'"></strong>
        <div data-action="scroll" class="minicart-items-wrapper">
            <ol id="mini-cart" class="minicart-items" data-bind="foreach: { data: getCartItems(), as: 'item' }">
                <each args="$parent.getRegion($parent.getItemRenderer(item.product_type))"
                      render="{name: getTemplate(), data: item, afterRender: function() {$parents[1].initSidebar()}}"
                ></each>
            </ol>
        </div>
    </if>

    <ifnot args="getCartParam('summary_count')">
        <strong class="subtitle empty"
                translate="'You have no items in your shopping cart.'"
        ></strong>
        <if args="getCartParam('cart_empty_message')">
            <p class="minicart empty text" text="getCartParam('cart_empty_message')"></p>
            <div class="actions">
                <div class="secondary">
                    <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                        <span translate="'View and Edit Cart'"></span>
                    </a>
                </div>
            </div>
        </if>
    </ifnot>

    <div class="actions" if="getCartParam('summary_count')">
        <div class="secondary">
            <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                <div translate="'Commander'"  data-mage-init='{"Magento_Checkout/js/proceed-to-checkout":{"checkoutUrl": "/checkout/cart" }}'></div>
            </a>
        </div>
    </div>

    <div id="minicart-widgets" class="minicart-widgets">
        <each args="getRegion('promotion')" render=""></each>
    </div>
    <div class="order_minimum_amount" data-bind="visible: !isMinimumOrderAmountReached(), i18n:'Vous n\'avez pas atteint le minimum de commande de '+shippingbarData().shipping_zone_order_minimum_amount+'€'">
    </div>
</div>
<each args="getRegion('sign-in-popup')" render=""></each>
