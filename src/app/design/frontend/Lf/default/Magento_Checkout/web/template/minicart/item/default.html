<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<li class="item product product-item" data-role="product-item">
    <div class="product">
 
        <div class="product-item-photo">
            <!-- ko foreach: $parent.getRegion('itemImage') -->
            <!-- ko template: {name: getTemplate(), data: item.product_image} --><!-- /ko -->
            <!-- /ko -->
        </div>
       

        <div class="product-item-details">
            <strong class="product-item-name">
                <!-- ko if: product_has_url -->

 
                    <!-- ko if: canApplyMsrp --> 
                    <div class="details-map">
                        <span class="label" data-bind="i18n: 'Price'"></span>
                        <span class="value" data-bind="i18n: 'See price before order confirmation.'"></span>
                    </div>
                    <!-- /ko -->
                    <!-- ko ifnot: canApplyMsrp -->
                    <!-- ko foreach: $parent.getRegion('priceSidebar') -->
                    <!-- ko template: {name: getTemplate(), data: item.product_price, as: 'price'} --><!-- /ko -->
                    <!-- /ko -->
                    <!-- /ko -->
                    <a href="#" style="float:right" data-bind="attr: {'data-cart-item': item_id, title: $t('Remove item')}"
                       class="action delete"></a>

                    <div class="details-qty qty">
                        <div data-bind="attr: {
                               id: 'cart-item-'+item_id+'-qty',
                               'data-cart-item': item_id,
                               'data-item-qty': qty,
                               'data-cart-item-id': product_sku
                               }, text: qty"  ></div>
                        <label class="label" data-bind="i18n: 'x ', attr: {
                               for: 'cart-item-'+item_id+'-qty'}"></label> 
                    </div>
                 


                <a data-bind="html: product_name"></a>
                <!-- /ko -->
                <!-- ko ifnot: product_has_url -->
                <!-- ko html: product_name --><!-- /ko -->
                <!-- /ko -->
            </strong>

            <!-- ko if: options.length -->
            <div class="product options" data-mage-init='{"collapsible":{"openedState": "active", "saveState": false}}'>
                <span data-role="title" class="toggle"><!-- ko i18n: 'See Details' --><!-- /ko --></span>

                <div data-role="content" class="content">
                    <strong class="subtitle"><!-- ko i18n: 'Options Details' --><!-- /ko --></strong>
                    <dl class="product options list">
                        <!-- ko foreach: { data: options, as: 'option' } -->
                        <dt class="label"><!-- ko text: option.label --><!-- /ko --></dt>
                        <dd class="values">
                            <!-- ko if: Array.isArray(option.value) -->
                            <span data-bind="html: option.value.join('<br>')"></span>
                            <!-- /ko -->
                            <!-- ko ifnot: Array.isArray(option.value) -->
                            <span data-bind="html: option.value"></span>
                            <!-- /ko -->
                        </dd>
                        <!-- /ko -->
                    </dl>
                </div>
            </div>
            <!-- /ko --> 
        </div>
    </div>
</li>
