<?php

// @codingStandardsIgnoreFile
/** @var \Magento\Contact\Block\ContactForm $block */
?>
<form class="form contact"
      action="<?= $block->escapeUrl($block->getFormAction()) ?>"
      id="contact-form"
      method="post"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      data-mage-init='{"validation":{}}'>

    <fieldset class="fieldset">
        <div class="field note no-label"><?= $block->escapeHtml(__('Jot us a note and we’ll get back to you as quickly as possible.')) ?></div>
        <div class="field name required">
            <label class="label" for="name"><span>Nom</span></label>
            <div class="control">
                <input name="name" id="name" title="<?= $block->escapeHtmlAttr(__('Name')) ?>" value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')->getPostValue('name') ?: $this->helper('Magento\Contact\Helper\Data')->getUserName()) ?>" class="input-text" type="text" data-validate="{required:true}"/>
            </div>
        </div>
        <div class="field name required">
            <label class="label" for="name"><span>Prénom</span></label>
            <div class="control">
                <input name="firstname" id="firstname" title="<?= $block->escapeHtmlAttr(__('Firstname')) ?>" value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')->getPostValue('firstname')) ?>" class="input-text" type="text" data-validate="{required:true}"/>
            </div>
        </div>
        <div class="field email required">
            <label class="label" for="email"><span>E-mail</span></label>
            <div class="control">
                <input name="email" id="email" title="<?= $block->escapeHtmlAttr(__('Email')) ?>" value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')->getPostValue('email') ?: $this->helper('Magento\Contact\Helper\Data')->getUserEmail()) ?>" class="input-text" type="email" data-validate="{required:true, 'validate-email':true}"/>
            </div>
        </div>
        <div class="field city required">
            <label class="label" for="city"><span>Ville</span></label>
            <div class="control">
                <input name="city" id="city" title="<?= $block->escapeHtmlAttr(__('City')) ?>" value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')->getPostValue('city')) ?>" class="input-text" type="text" data-validate="{required:true}"/>
            </div>
        </div>
        <div class="field telephone required">
            <label class="label" for="telephone"><span>Téléphone</span></label>
            <div class="control">
                <input name="telephone" id="telephone" title="<?= $block->escapeHtmlAttr(__('Phone Number')) ?>" value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')->getPostValue('telephone')) ?>" class="input-text" type="text" data-validate="{required:true}"/>
            </div>
        </div>
        <div class="field locality required">
            <label class="label" for="telephone"><span>Localité</span></label>
            <div class="control">
                <select name="locality">
                    <option value="0" ><?= __('non pertinent') ?></option>
                    <?php foreach($block->getLocalities() as $locality): ?>
                        <option value="<?= $locality['value']; ?>" ><?= $locality['label']; ?> </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        <div class="field comment required">
            <label class="label" for="comment"><span>Message</span></label>
            <div class="control center">
                <textarea name="comment" id="comment" title="<?= $block->escapeHtmlAttr(__('What’s on your mind?')) ?>" class="input-text" cols="5" rows="3" data-validate="{required:true}"><?= $block->escapeHtml($this->helper('Magento\Contact\Helper\Data')->getPostValue('comment')) ?></textarea>
                <br><br>
               <div class="txt"> Tous les champs sont obligatoires.<br><br></div>
            <input type="hidden" name="hideit" id="hideit" value="" />
            <button type="submit" title="<?= $block->escapeHtmlAttr(__('Submit')) ?>" class="action submit primary btn btn-8h">
                <span><?= $block->escapeHtml(__('Submit')) ?></span>
            </button>
            </div>
        </div>
    </fieldset>


</form>
