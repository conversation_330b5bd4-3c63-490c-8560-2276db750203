<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Please confirm your %store_name account" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var this.getUrl($store, 'customer/account/confirm/', [_query:[id:$customer.id, key:$customer.confirmation, back_url:$back_url]])":"Account Confirmation URL",
"var this.getUrl($store, 'customer/account/')":"Customer Account URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name"
} @-->

{{template config_path="design/email/header_template"}}
<div class="lf_container">
    
    <p class="greeting">{{trans "%name," name=$customer.name}}</p>
    <p>{{trans "You must confirm your %customer_email email before you can sign in (link is only valid once):" customer_email=$customer.email}}</p>

    <table class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td>
                <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
                    <tr>
                        <td align="center">
                            <a href="{{var this.getUrl($store,'customer/account/confirm/',[_query:[id:$customer.id,key:$customer.confirmation,back_url:$back_url],_nosid:1])}}" target="_blank">{{trans "Confirm Your Account"}}</a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
{{template config_path="design/email/footer_template"}}
