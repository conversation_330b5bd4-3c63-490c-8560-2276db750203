<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Your %store_name email and password has been changed" store_name=$store.getFrontendName()}} @-->
<!--@vars {} @-->
{{template config_path="design/email/header_template"}}
<div class="lf_container">

	<p class="greeting">{{trans "Hello,"}}</p>
	<br>

	<p>
	    {{trans "We have received a request to change the following information associated with your account at %store_name: email, password." store_name=$store.getFrontendName()}}
        {{if franchise.email}}
            {{trans 'If you have not authorized this action, please contact us immediately at <a href="mailto:%store_email">%store_email</a>' store_email=$franchise.email |raw}} {{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$franchise.phone |raw}}.
        {{else}}
            {{trans 'If you have not authorized this action, please contact us immediately at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}} {{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$store_phone |raw}}.
        {{/if}}	</p>
	<br>

	<p>{{trans "Thanks,<br>%store_name" store_name=$store.getFrontendName() |raw}}</p>
</div>
{{template config_path="design/email/footer_template"}}
