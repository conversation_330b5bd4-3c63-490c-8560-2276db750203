<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Address\Edit $block */
?>
<?php $_company = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Company') ?>
<?php $_telephone = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Telephone') ?>
<?php $_fax = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Fax') ?>
<form class="form-address-edit"
      action="<?= $block->escapeUrl($block->getSaveUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
    <fieldset class="fieldset">
        <legend class="legend"><span><?= $block->escapeHtml(__('Contact Information')) ?></span></legend><br>
        <?= $block->getBlockHtml('formkey') ?>
        <input type="hidden" name="success_url" value="<?= $block->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $block->escapeUrl($block->getErrorUrl()) ?>">
        <?= $block->getNameBlockHtml() ?>

        <?php if ($_company->isEnabled()): ?>
            <?= $_company->setCompany($block->getAddress()->getCompany())->toHtml() ?>
        <?php endif ?>

        <?php if ($_telephone->isEnabled()): ?>
            <?= $_telephone->setTelephone($block->getAddress()->getTelephone())->toHtml() ?>
        <?php endif ?>

        <?php if ($_fax->isEnabled()): ?>
            <?= $_fax->setFax($block->getAddress()->getFax())->toHtml() ?>
        <?php endif ?>

        <div class="field email">
            <label class="label" for="country"><span><?= $block->escapeHtml(__('Email comptable')) ?></span></label>
            <div class="control">
                <input type="text" name="email" class="validate-email" value="<?= $block->getAddress()->getCustomAttribute('email')?->getValue() ?>">
            </div>
        </div>
    </fieldset>
    <fieldset class="fieldset">
        <legend class="legend"><span><?= $block->escapeHtml(__('Address')) ?></span></legend><br>
        <?php $_streetValidationClass = $this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('street'); ?>
        <div class="field street required">
            <label for="street_1" class="label">
                <span><?= $block->escapeHtml(__('Street Address')) ?></span>
            </label>
            <div class="control">
                <input type="text"
                       name="street[]"
                       value="<?= $block->escapeHtmlAttr($block->getStreetLine(1)) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>"
                       id="street_1"
                       class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>"/>
                <div class="nested">
                    <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                    <?php for ($_i = 1, $_n = $this->helper('Magento\Customer\Helper\Address')->getStreetLines(); $_i < $_n; $_i++): ?>
                        <div class="field additional">
                            <label class="label" for="street_<?= /* @noEscape */ $_i + 1 ?>">
                                <span><?= $block->escapeHtml(__('Street Address %1', $_i + 1)) ?></span>
                            </label>
                            <div class="control">
                                <input type="text" name="street[]"
                                       value="<?= $block->escapeHtmlAttr($block->getStreetLine($_i + 1)) ?>"
                                       title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i + 1)) ?>"
                                       id="street_<?= /* @noEscape */ $_i + 1 ?>"
                                       class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>">
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <?php if ($this->helper('Magento\Customer\Helper\Address')->isVatAttributeVisible()) : ?>
            <div class="field taxvat">
                <label class="label" for="vat_id">
                    <span><?= $block->escapeHtml(__('VAT Number')) ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="vat_id"
                           value="<?= $block->escapeHtmlAttr($block->getAddress()->getVatId()) ?>"
                           title="<?= $block->escapeHtmlAttr(__('VAT Number')) ?>"
                           class="input-text <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('vat_id')) ?>"
                           id="vat_id">
                </div>
            </div>
        <?php endif; ?>
        <div class="field city required">
            <label class="label" for="city"><span><?= $block->escapeHtml(__('City')) ?></span></label>
            <div class="control">
                <input type="text"
                       name="city"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getCity()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('City')) ?>"
                       class="input-text <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('city')) ?>"
                       id="city">
            </div>
        </div>
        <div class="field region required">
            <label class="label" for="region_id">
                <span><?= $block->escapeHtml(__('State/Province')) ?></span>
            </label>
            <div class="control">
                <select id="region_id" name="region_id"
                        title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>"
                        class="validate-select" <?= /* @noEscape */ !$block->getConfig('general/region/display_all') ? ' disabled="disabled"' : '' ?>>
                    <option value=""><?= $block->escapeHtml(__('Please select a region, state or province.')) ?></option>
                </select>
                <input type="text"
                       id="region"
                       name="region"
                       value="<?= $block->escapeHtmlAttr($block->getRegion()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>"
                       class="input-text <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('region')) ?>"<?= !$block->getConfig('general/region/display_all') ? ' disabled="disabled"' : '' ?>/>
            </div>
        </div>
        <div class="field zip required">
            <label class="label" for="zip">
                <span><?= $block->escapeHtml(__('Zip/Postal Code')) ?></span>
            </label>
            <div class="control">
                <input type="text"
                       name="postcode"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getPostcode()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Zip/Postal Code')) ?>"
                       id="zip"
                       class="input-text validate-zip-international <?= $block->escapeHtmlAttr($this->helper('Magento\Customer\Helper\Address')->getAttributeValidationClass('postcode')) ?>">
            </div>
        </div>
        <div class="field country required">
            <label class="label" for="country"><span><?= $block->escapeHtml(__('Country')) ?></span></label>
            <div class="control">
                <?= $block->getCountryHtmlSelect() ?>
            </div>
        </div>

        <?php if ($block->isDefaultBilling()): ?>
            <div class="message info">
                <span><?= $block->escapeHtml(__("It's a default billing address.")) ?></span>
            </div>
        <?php elseif ($block->canSetAsDefaultBilling()): ?>
            <div class="field choice set billing">
                <input type="checkbox" id="primary_billing" name="default_billing" value="1" class="checkbox">
                <label class="label" for="primary_billing">
                    <span><?= $block->escapeHtml(__('Use as my default billing address')) ?></span>
                </label>
            </div>
        <?php else: ?>
            <input type="hidden" name="default_billing" value="1" />
        <?php endif; ?>

        <?php if ($block->isDefaultShipping()): ?>
            <div class="message info">
                <span><?= $block->escapeHtml(__("It's a default shipping address.")) ?></span>
            </div>
        <?php elseif ($block->canSetAsDefaultShipping()): ?>
            <div class="field choice set shipping">
                <input type="checkbox" id="primary_shipping" name="default_shipping" value="1" class="checkbox">
                <label class="label" for="primary_shipping">
                    <span><?= $block->escapeHtml(__('Use as my default shipping address')) ?></span>
                </label>
            </div>
        <?php else: ?>
            <input type="hidden" name="default_shipping" value="1">
        <?php endif; ?> 

        <div class="actions-toolbar">
            <div class="primary">
                <button type="submit"
                        class="btn btn-8h action save primary"
                        data-action="save-address"
                        title="<?= $block->escapeHtmlAttr(__('Save Address')) ?>">
                    <span><?= $block->escapeHtml(__('Save Address')) ?></span>
                </button>
            </div>
            <div class="secondary">
                <a class="action back" href="<?= $block->escapeUrl($block->getBackUrl()) ?>">
                    <span><?= $block->escapeHtml(__('Go back')) ?></span>
                </a>
            </div>
        </div>
    </fieldset>
   
</form>
<script type="text/x-magento-init">
    {
        "#form-validate": {
            "addressValidation": {}
        },
        "#country": {
            "regionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */ $block->getConfig('general/region/display_all') ? 'true' : 'false' ?>,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getRegionJson() ?>,
                "defaultRegion": "<?= (int) $block->getRegionId() ?>",
                "countriesWithOptionalZip": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getCountriesWithOptionalZip(true) ?>
            }
        }
    }
</script>
