<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="authentication-wrapper" data-block="authentication" data-bind="visible: isActive()">
    <button
        type="button"
        class="action action-auth-toggle"
        data-trigger="authentication">
        <span data-bind="i18n: 'Sign In'"></span>
    </button>
    <div class="block-authentication"
         style="display: none"
         data-bind="mageInit: {
            'Magento_Ui/js/modal/modal':{
                'type': 'custom',
                'modalClass': 'authentication-dropdown',
                'trigger': '[data-trigger=authentication]',
                'wrapperClass': 'authentication-wrapper',
                'parentModalClass': '_has-modal-custom _has-auth-shown',
                'responsive': true,
                'responsiveClass': 'custom-slide',
                'overlayClass': 'dropdown-overlay modal-custom-overlay',
                'buttons': []
            }}">
        <!-- ko foreach: getRegion('before') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!-- /ko -->
        <div class="block block-customer-login"
             data-bind="attr: {'data-label': $t('or')}">
            <div class="block-title">
                <h3 id="block-customer-login-heading"
                        role="heading"
                        aria-level="2"
                        data-bind="i18n: 'Sign In'"></h3>
            </div>
            <!-- ko foreach: getRegion('messages') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
            <div class="block-content" aria-labelledby="block-customer-login-heading">
                <form data-role="login"
                      data-bind="submit:login"
                      method="post">
                    <div class="fieldset"
                              data-bind="attr: {'data-hasrequired': $t('* Required Fields')}">
                        <div class="field field-email required">
                            <label class="label" for="login-email"><span data-bind="i18n: 'Email Address'"></span></label>
                            <div class="control">
                                <input type="email"
                                       class="input-text"
                                       id="login-email"
                                       name="username"
                                       data-bind="attr: {autocomplete: autocomplete}"
                                       data-validate="{required:true, 'validate-email':true}" />
                            </div>
                        </div>
                        <div class="field field-password required">
                            <label for="login-password" class="label"><span data-bind="i18n: 'Password'"></span></label>
                            <div class="control">
                                <input type="password"
                                       class="input-text"
                                       id="login-password"
                                       name="password"
                                       data-bind="attr: {autocomplete: autocomplete}"
                                       data-validate="{required:true}"
                                       autocomplete="off"/>
                            </div>
                        </div> 
                        <!-- ko foreach: getRegion('additional-login-form-fields') -->
                        <!-- ko template: getTemplate() --><!-- /ko -->
                        <!-- /ko -->
                    </div>
                    <div class="actions-toolbar">
                        <input name="context" type="hidden" value="checkout" />
                        <div class="primary">
                            <button type="submit" class="action action-login secondary btn btn-8h"><span data-bind="i18n: 'Sign In'"></span></button>
                        </div>
                        <div class="secondary">
                            <a class="action action-remind" data-bind="attr: { href: forgotPasswordUrl }">
                                <span data-bind="i18n: 'Forgot Your Password?'"></span>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
