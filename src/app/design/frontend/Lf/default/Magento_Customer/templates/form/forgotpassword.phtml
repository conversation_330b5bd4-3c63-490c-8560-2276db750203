<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 *
 * @var $block \Magento\Customer\Block\Account\Forgotpassword
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Account\Forgotpassword $block */
?>
<form class="form password forget"
      action="<?= $block->escapeUrl($block->getUrl('*/*/forgotpasswordpost')) ?>"
      method="post"
      id="form-validate"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
        <div class="field note"><?= $block->escapeHtml(__('Please enter your email address below to receive a password reset link.')) ?></div>
        <div class="field email required">
            <label for="email_address" class="label"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email" name="email" alt="email" id="email_address" class="input-text" value="<?= $block->escapeHtmlAttr($block->getEmailValue()) ?>" data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>
        <?= $block->getChildHtml('form_additional_info') ?>
    </fieldset>
    <div class="actions-toolbar">
        <?= $block->getBlockHtml('formkey')?>
        <div class="primary">
            <button type="submit" class="btn btn-8h action submit primary"><span><?= $block->escapeHtml(__('Reset My Password')) ?></span></button>
        </div> 
    </div>
</form>
