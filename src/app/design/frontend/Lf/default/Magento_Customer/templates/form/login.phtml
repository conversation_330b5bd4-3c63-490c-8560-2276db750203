<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Login $block */
?>
<div class="block block-customer-login">
    <div class="block-title">
        <h3 id="block-customer-login-heading" role="heading" aria-level="2"><?= $block->escapeHtml(__('Registered Customers')) ?></h3>
    </div>
    <div class="block-content" aria-labelledby="block-customer-login-heading">
        <form class="form form-login"
              action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>"
              method="post"
              id="login-form"
              data-mage-init='{"validation":{}}'>
            <?= $block->getBlockHtml('formkey') ?>
            <fieldset class="fieldset login" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
                <div class="field note"><?= $block->escapeHtml(__('If you have an account, sign in with your email address.')) ?></div>
                <div class="field email required">
                    <label class="label" for="email"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
                    <div class="control">
                        <input name="login[username]" value="<?= $block->escapeHtmlAttr($block->getUsername()) ?>" <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?> id="email" type="email" class="input-text" title="<?= $block->escapeHtmlAttr(__('Email')) ?>" data-validate="{required:true, 'validate-email':true}">
                    </div>
                </div>
                <div class="field password required">
                    <label for="pass" class="label"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
                    <div class="control">
                        <input name="login[password]" type="password" <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?> class="input-text" id="pass" title="<?= $block->escapeHtmlAttr(__('Password')) ?>" data-validate="{required:true}">
                    </div>
                </div>
                <?= $block->getChildHtml('form_additional_info') ?>
                <div class="actions-toolbar">
                    <div class="primary"><button type="submit" class="action login primary btn btn-8h" name="send" id="send2"><span><?= $block->escapeHtml(__('Sign In')) ?></span></button></div>
                    <div class="secondary"><a class="action remind" href="<?= $block->escapeUrl($block->getForgotPasswordUrl()) ?>"><span><?= $block->escapeHtml(__('Forgot Your Password?')) ?></span></a></div>
                </div> 
            </fieldset>
        </form>
    </div>
</div>
