<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Newsletter $block */
?>
<?= $block->getChildHtml('form_before') ?>
    <form class="form form-newsletter-manage" action="<?= $block->escapeUrl($block->getAction()) ?>" method="post" id="form-validate">
        <fieldset class="fieldset">
            <?= $block->getBlockHtml('formkey') ?>
            <legend class="legend"><span><?= $block->escapeHtml(__('Subscription option')) ?></span></legend><br>
            <div class="field choice">
                <input type="checkbox" name="is_subscribed" id="subscription" value="1" title="<?= $block->escapeHtmlAttr(__('General Subscription')) ?>"<?php if ($block->getIsSubscribed()): ?> checked="checked"<?php endif; ?> class="checkbox">
                <label for="subscription" class="label"><span><?= $block->escapeHtml(__('General Subscription')) ?></span></label>
            </div>
            <?php /* Extensions placeholder */ ?>
            <?= $block->getChildHtml('customer.form.newsletter.extra') ?>
        </fieldset>
        <div class="actions-toolbar">
            <div class="primary"><button type="submit" title="<?= $block->escapeHtmlAttr(__('Save')) ?>" class="btn btn-8h action save primary"><span><?= $block->escapeHtml(__('Save')) ?></span></button></div>
          
        </div>
    </form>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.newsletter.extra2') ?>
