<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Account\Resetpassword $block */
?>
<form action="<?= $block->escapeUrl($block->getUrl('*/*/resetpasswordpost', ['_query' => ['id' => $block->getRpCustomerId(), 'token' => $block->getResetPasswordLinkToken()]])) ?>"
      method="post"
      <?php if ($block->isAutocompleteDisabled()) :?> autocomplete="off"<?php endif; ?>
      id="form-validate"
      class="form password reset"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
        <div class="field password required" data-mage-init='{"passwordStrengthIndicator": {}}'>
            <label class="label" for="password"><span><?= $block->escapeHtml(__('New Password')) ?></span></label>
            <div class="eye"></div>     
            <div class="control">
                <input type="password" class="input-text" name="password" id="password"
                       data-password-min-length="<?= $block->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $block->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                
                 <div class="password-bubble"> 
                      <p>Votre mot de passe doit contenir</p>
                        <ul>
                          <li id="has-char-count"><span class="validation-text">Au moins 8 caractères (<span id="currentcount">0</span>/8)</span></li>
                          <li id="has-upper-and-lowercase-char"><span class="validation-text">Des majuscules et minuscules</span></li>
                          <li id="has-number"><span class="validation-text">Au moins 1 chiffre</span></li>
                      </ul>
                  </div>  
                  
            </div>
        </div>
        <div class="field confirmation required">
            <label class="label" for="password-confirmation"><span><?= $block->escapeHtml(__('Confirm New Password')) ?></span></label>
            <div class="control">
                <input type="password" class="input-text" name="password_confirmation" id="password-confirmation" data-validate="{required:true,equalTo:'#password'}" autocomplete="off">
            </div>
        </div>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary"><span><?= $block->escapeHtml(__('Set a New Password')) ?></span></button>
        </div>
    </div>
</form>
<script type="text/x-magento-init">
    {
        "*": {
            "Magento_Ui/js/core/app": {
                "components": {
                    "showPassword": {
                        "component": "Magento_Customer/js/show-password",
                        "passwordSelector": "#password,#password-confirmation"
                    }
                }
            }
        }
    }
</script>
