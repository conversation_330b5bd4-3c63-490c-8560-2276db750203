// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@account-nav-background: @sidebar__background-color;
@account-nav-color: false;

@account-nav-current-border: 3px solid transparent;
@account-nav-current-border-color: @color-orange-red1;
@account-nav-current-color: false;
@account-nav-current-font-weight: @font-weight__semibold;

@account-nav-delimiter__border-color: @color-gray82;

@account-nav-item-hover: @color-gray91;

@_password-default: @color-gray-light01;
@_password-weak: #ffafae;
@_password-medium: #ffd6b3;
@_password-strong: #c5eeac;
@_password-very-strong: #81b562;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .toolbar-amount, .limiter {
        display: none;
    }
    .items.order-links {
       border:0;
       li:first-child {
        display:none;
       }
    }
    .order-actions-toolbar {
        margin:0;
        margin-right:20px;
    }

    .block-order-details-view {
        .block-title, .box {
            margin-left: 0!important;
        }
        .block-title {
            strong {font-size: 20px!important;margin-bottom: 15px; display: block!important;}
        }
    }
    .order-title {
        strong {
            font-size: 20px!important;
            margin-bottom: 15px;
            display: block!important;
            font-weight:bold!important}
    }
    .order-status {
        &:before {
        content:'(';
        }
         &:after {
        content:')';
        }
    }
    #password-error {
        font-size:16px;
    }
    .login-container {
        max-width: 1300px;
        margin: auto;
        .block {
            background:#F4F4F4;
            padding:20px;
            width: 45%!important;

            &-new-customer {
                .actions-toolbar {
                    margin-top: 25px;
                }
            }

            .block-title {
                &:extend(.abs-login-block-title all);
                .lib-font-size(18);
            }
        }
        .secondary {
            margin-top: -30px;
        }
        .fieldset {
            .lib-form-hasrequired(bottom);
            &:after {
                margin-top: 35px;
            }
        }
        .actions-toolbar {
             width:55%!important;
        }
    }

    .block-addresses-list {
        .items.addresses {
            > .item {
                margin-bottom: @indent__base;
                width:51.8%!important;
                &:nth-child(even) {
                    width:45%!important;
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    .field {
        font-family: Eczar, arial;
        font-size: 18px;
    }
    .form-address-edit {
        #region_id {
            display: none;
        }

        .actions-toolbar .action.primary {
            &:extend(.abs-button-l all);
        }
    }

    .form-edit-account {
        .fieldset {
            &.password {
                display: none;

                .control {
                    margin-top: 0;
                }
            }

            .field input {
                margin-top: 0;
            }

            .eye + .control {
                margin-top:-20px;
            }
        }
    }

    .box-billing-address,
    .box-shipping-address,
    .box-information,
    .box-newsletter {
        .box-content {
            line-height: 26px;
        }
    }

    //  Full name fieldset
    .fieldset {
        .fullname {
            &.field {
                > .label {
                    &:extend(.abs-visually-hidden all);

                    + .control {
                        width: 100%;
                    }
                }
            }

            .field {
                &:extend(.abs-add-clearfix all);
            }
        }
    }

    .lf-systempay-aliases-index {
        .box {
            background: #F5F5F5;
            padding: 20px;
        }
    }


    //
    //  My account
    //  -  --------------------------------------------

    .account {

        .columns {
            max-width: 1800px;
            margin:auto;
        }
        .actions .action.order {
            display: none;
        }
        .message {
            &.info {
                width: 90%;
                margin: auto;
                background: @lf-beige;
            }
        }

       .page-title {
           margin-left: 12%;
       }

        .sidebar-main {
            margin-top: -80px;
        }

        .actions-toolbar .secondary .back {
            display: none;
        }

        .message.success {
          width: 52%;
          margin-left: 30%;
        }

        .column.main {
            h2 {
                margin-top: 0;
            }

            .toolbar {
                text-align: center;
                .limiter-options {
                    width: auto;
                }
            }

            .limiter {
                >.label {
                    &:extend(.abs-visually-hidden all);
                }
            }

            .block:not(.widget) {
                &:extend(.abs-account-blocks all);

                .block-title {
                      margin-left: 10%;
                }
                &.block-addresses-list {
                    margin-left: 10%;
                }

                 &.block block-dashboard-info {
                    .block-title {
                        display:none;
                    }
                 }
                 &.block-dashboard-addresses {
                    .block-title strong {
                        text-transform: uppercase;
                        margin-bottom: 15px;
                        display: inline-block;
                    }
                 }
                .block-content {
                    &:extend(.abs-add-clearfix-desktop all);

                    .box {
                        &:extend(.abs-blocks-2columns all);
                        width: 39%;
                        margin-left: 10%;
                        display: inline-block;
                        vertical-align: top;
                    }
                }
             }


             .fidelite {
                 margin-left: 10%;
                 margin-bottom: 20px;
                 margin-top: 15px;
                 text-transform: uppercase;
                 font-weight: bold;
             }

        }

        .sidebar-additional {
            margin-top: 40px;
        }

        .table-wrapper {
            &:last-child {
                margin-bottom: 0;
            }
            .col.actions {
                width: 120px;
            }
            .action {
                margin-right: 15px;

                &:last-child {
                    margin-right: 0;
                    display:none;
                }
            }
        }

        .table-return-items {
            .qty {
                .input-text {
                    &:extend(.abs-input-qty all);
                }
            }
        }
    }

    //  Checkout address (create shipping address)
    .field.street {
        .field.additional {
            .label {
                &:extend(.abs-visually-hidden all);
            }
        }
    }

    //
    //  Account navigation
    //  ---------------------------------------------

    .account-nav {
        .title {
            &:extend(.abs-visually-hidden all);
        }

        .content {
            .lib-css(background, @account-nav-background);
            padding: 15px 0;
        }

        .item {
            margin: 3px 0 0;
            text-transform: uppercase;

            &:first-child {
                margin-top: 0;
            }

            a,
            > strong {
                .lib-css(color, @lf-blue);
                border-left: 3px solid transparent;
                display: block;
                padding: @indent__xs 18px @indent__xs 15px;
            }
            > strong {
                background:@lf-blue;
                color:white;
            }
            a {
                text-decoration: none;

                &:hover {
                    .lib-css(background, @account-nav-item-hover);
                }
            }

            &.current {
                 background:@lf-blue;

                a,
                strong {
                    .lib-css(color, white);
                    .lib-css(font-weight, @account-nav-current-font-weight);
                }


            }

            .delimiter {
                border-top: 1px solid @account-nav-delimiter__border-color;
                display: block;
                margin: @indent__s 1.8rem;
            }
        }
    }

    //
    //  Blocks & Widgets
    //  ---------------------------------------------

    .block {
        &:extend(.abs-margin-for-blocks-and-widgets all);
        .column.main & {
            &:last-child {
                margin-bottom: 0;
            }
        }

        .title {
            margin-bottom: @indent__s;

            strong {
                .lib-heading(h4);
                .column.main & {
                    font-size: @h3__font-size;
                }
            }
        }

        p:last-child {
            margin: 0;
        }

        .box-actions {
            margin-top: @indent__xs;
            a {
                margin-right: 20px;
            }
        }
    }

    //
    //  Password Strength Meter
    //  ---------------------------------------------

    .field.password {
        .control {
            .lib-vendor-prefix-display();
            .lib-vendor-prefix-flex-direction();

            .mage-error {
                .lib-vendor-prefix-order(2);
            }

            .input-text {
                .lib-vendor-prefix-order(0);
                z-index: 2;
            }
        }
    }

    .control.captcha-image {
        .lib-css(margin-top, @indent__s);

        .captcha-img {
            vertical-align: middle;
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .account {
        .column.main,
        .sidebar-additional {
            margin: 0;
        }

    }
     .account-social-login .social-btn {
            width: 300px!important;
        }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .login-container {
        .fieldset {
            &:after {
                text-align: center;
            }
        }
    }

    .account {
        .page.messages {
            margin-bottom: @indent__xl;
        }

        .toolbar {
            &:extend(.abs-pager-toolbar-mobile all);
        }
    }

    .control.captcha-image {
        .captcha-img {
            .lib-css(margin-bottom, @indent__s);
            display: block;
        }
    }

    .customer-account-index {
        .page-title-wrapper {
            position: relative;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .actions-toolbar > .primary .action:last-child, .actions-toolbar > .secondary .action:last-child {
        margin-top: 6px !important;
    }
    .login-container {
        &:extend(.abs-add-clearfix-desktop all);

        .block {
            &:extend(.abs-blocks-2columns all);
            &.login {
                .actions-toolbar {
                    > .primary {
                        margin-bottom: 0;
                        margin-right: @indent__l;

                    }

                    > .secondary {
                        float: left;
                    }
                }
            }
        }

        .fieldset {
            &:after {
                &:extend(.abs-margin-for-forms-desktop all);
            }

            > .field {
                > .control {
                    width: 55%;
                }
            }
        }
    }

    //  Full name fieldset
    .fieldset {
        .fullname {
            .field {
                .label {
                    .lib-css(margin, @form-field-type-label-inline__margin);
                    .lib-css(padding, @form-field-type-label-inline__padding);
                    .lib-css(text-align, @form-field-type-label-inline__align);
                    .lib-css(width, @form-field-type-label-inline__width);
                    box-sizing: border-box;
                    float: left;
                }

                .control {
                    .lib-css(width, @form-field-type-control-inline__width);
                    float: left;
                }
            }
        }
    }

    .form.password.reset,
    .form.send.confirmation,
    .form.password.forget {
        min-width: 600px;
        width: 50%;
        margin:auto;
        .note {
            text-align:center;
        }
    }

    //
    //  My account
    //  ---------------------------------------------

    .account.page-layout-2columns-left {
        .sidebar-main,
        .sidebar-additional {
            width: 22.3%;
        }
        .sidebar-additional {
            padding-left: 1%;
            .block-wishlist {
                display: none;
            }
        }
        .column.main {
            width: 77.7%;
        }
    }


    .form.create.account  {
        text-align:center;
        fieldset {
            width:40%;
            margin: 0 2%;
            display:inline-block;
            vertical-align: top;
            label.label {
                font-weight:normal;
            }
        }
    }

    .block-addresses-list {
        a.delete {
            margin-left:30px;
        }
       & + .actions-toolbar {
            margin: auto!important;
            max-width: 350px;
        }
    }

    .form-address-edit,
    .form-edit-account {

        .field {
            font-size:16px;
            &.billing,
            &.shipping {
                .checkbox {
                    float:right;
                }

                &:before {
                    width: 0;
                }
            }

            &.country,
            &.region {
                display: none !important;
            }
        }

        .fieldset > .field.required > .label:after,
        .fieldset > .fields > .field.required > .label:after,
        .fieldset > .field._required > .label:after,
        .fieldset > .fields > .field._required > .label:after {
            position: absolute;
        }

        fieldset {
            width: 40%;
            margin: 0 2%;
            display: inline-block;
            vertical-align: top;

            label.label {
                font-weight:normal!important;
            }
        }
        .actions-toolbar {
            margin-left: 12%!important;
            max-width: 300px;
            float: right;
        }
    }

    .account .form-edit-account {

        .actions-toolbar {

            margin-top: 45px !important;
            float: none;
        }
    }
     .form-address-edit {
         .actions-toolbar {
            margin: 0!important;
            max-width: 300px;
        }
     }
    input[type="checkbox"] {
        width: 20px;
        height: 20px;
        vertical-align: middle;
    }

    .form-newsletter-manage {
        fieldset .legend {
            margin-left: 10%;
        }
        .fieldset > .field.choice {
            margin-top:15px;
            &:before {
            width:10%;
            }
        }
        .actions-toolbar {
            margin-left: 10%!important;
            max-width: 200px;
        }
    }

    legend.legend {
        color: @lf-blue;
        font-size: 24px;
        font-family: 'DinProBlack';
        text-transform: uppercase;

        .toolbar {
            &:extend(.abs-pager-toolbar all);
        }
    }

    .block-addresses-list {
        .items.addresses {
            &:extend(.abs-add-clearfix-desktop all);
            font-size: 0;

            > .item {
                display: inline-block;
                font-size: @font-size__base;
                margin-bottom: @indent__base;
                vertical-align: top;
                width: 48.8%;

                &:nth-last-child(1),
                &:nth-last-child(2) {
                    margin-bottom: 0;
                }

                &:nth-child(even) {
                    margin-left: 2.4%;
                }
            }
        }
    }

    //
    //  Welcome block
    //  ---------------------------------------------

    .dashboard-welcome-toggler {
        &:extend(.abs-visually-hidden-desktop all);
    }

    .control.captcha-image {
        .captcha-img {
            margin: 0 @indent__s @indent__s 0;
        }
    }
}


@media only screen and (min-width: 300px) and (max-width: 1000px) {
    .login-container .block {
        width: auto !important;
    }

    .account {
        .page-title {
            margin: 25px !important;
        }

        .column.main {
            .block:not(.widget) {
                .block-title {
                     margin-left:15px;
                     strong {
                        font-size:20px;
                        display:block!important;
                     }
                     > .action {
                        margin-left: 0;
                     }
                }
                .block-content .box {
                    margin-left:15px;
                    width:~"calc(100% - 20px)";
                }
                &.block-addresses-list {
                     margin-left:15px;
                      a.delete {
                        margin-left:30px;
                    }
                }
            }
            .actions-toolbar {
                padding:15px;
                margin-bottom: 40px;
            }
        }
        fieldset  {
           padding:15px;
        }
        .sidebar-additional {
            display: none;
        }
    }

    .table-wrapper.orders-history {
        width: 90%;
        margin-left: 5%;

        .col.actions {
            width: auto !important;
        }

        table tr {
            border-bottom: 1px solid @lf-blue;
        }
    }
}
