/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'ko',
    'Magento_Ui/js/form/form',
    'Magento_Customer/js/action/login',
    'Magento_Customer/js/customer-data',
    'Magento_Customer/js/model/authentication-popup',
    'mage/translate',
    'mage/url',
    'Magento_Ui/js/modal/alert',
    'mage/validation'
], function ($, ko, Component, loginAction, customerData, authenticationPopup, $t, url, alert) {
    'use strict';

    return Component.extend({
        registerUrl: window.authenticationPopup.customerRegisterUrl,
        forgotPasswordUrl: window.authenticationPopup.customerForgotPasswordUrl,
        autocomplete: window.authenticationPopup.autocomplete,
        modalWindow: null,
        isLoading: ko.observable(false),

        defaults: {
            template: 'Magento_Customer/authentication-popup'
        },

        /**
         * Init
         */
        initialize: function () {
            var self = this;

            this._super();
            url.setBaseUrl(window.authenticationPopup.baseUrl);
            loginAction.registerLoginCallback(function () {
                self.isLoading(false);
            });


            $(".eye").on('click', function() {

                $(this).toggleClass("close");
                var input = $("input[name='password']");
                if (input.attr("type") == "password") {
                    input.attr("type", "text");
                } else {
                    input.attr("type", "password");
                }
            });

            var $bubble = $("#password").next(".password-bubble")

            $("#password").on('focus', function() {
                $bubble.show();
            });
            $("#password-confirmation").on('focus', function() {

                if($bubble.find(".okay").length !== 3) {
                    $("#password").trigger('focus');
                } else {
                    $bubble.hide();
                }
            });

            $("#password").on("input", function() {
                $("#currentcount").html($(this).val().length);


                if(/[a-z]/.test($(this).val())) {
                    var min = 1;
                } else {
                    var min = 0;
                }
                if(/[A-Z]/.test($(this).val())) {
                    var maj = 1;
                } else {
                    var maj = 0;
                }

                if(min + maj == 2) {
                    $("#has-upper-and-lowercase-char").addClass("okay");
                } else {
                    $("#has-upper-and-lowercase-char").removeClass("okay");
                }

                if(/[0-9]/.test($(this).val())) {
                    $("#has-number").addClass("okay");
                } else {
                    $("#has-number").removeClass("okay");
                }

                if($(this).val().length > 7) {
                    $("#has-char-count").addClass("okay");
                } else {
                    $("#has-char-count").removeClass("okay");
                }
            });


        },

        /** Init popup login window */
        setModalElement: function (element) {
            if (authenticationPopup.modalWindow == null) {
                authenticationPopup.createPopUp(element);
            }
        },

        /** Is login form enabled for current customer */
        isActive: function () {
            var customer = customerData.get('customer');

            return customer() == false; //eslint-disable-line eqeqeq
        },

        /** Show login popup window */
        showModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            } else {
                alert({
                    content: $t('Guest checkout is disabled.')
                });
            }
        },

        /**
         * Provide login action
         *
         * @return {Boolean}
         */
        login: function (formUiElement, event) {
            var loginData = {},
                formElement = $(event.currentTarget),
                formDataArray = formElement.serializeArray();

            event.stopPropagation();
            formDataArray.forEach(function (entry) {
                loginData[entry.name] = entry.value;
            });

            if (formElement.validation() &&
                formElement.validation('isValid')
            ) {
                this.isLoading(true);
                loginAction(loginData,'/checkout/cart');
            }

            return false;
        }
    });
});
