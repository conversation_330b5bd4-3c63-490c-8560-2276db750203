<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->

<div class="block-authentication"
     data-bind="afterRender: setModalElement, blockLoader: isLoading"
     style="display: none">
    <div class="block block-new-customer"
         data-bind="attr: {'data-label': $t('or')}">
        <h4 class="block-title">
            <strong id="block-new-customer-heading"
                    role="heading"
                    aria-level="2"
                    data-bind="i18n: 'Checkout as a new customer'"></strong>
        </h4>
        <div class="block-content" aria-labelledby="block-new-customer-heading"> 
            <div class="actions-toolbar">
                <div class="primary">
                    <button class="action action-register primary btn btn-8h" onclick="window.location=jQuery(this).attr('href');" data-bind="attr: {href: registerUrl}">
                        <span data-bind="i18n: 'Create an Account'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="block block-customer-login"
         data-bind="attr: {'data-label': $t('or')}">
        <h4 class="block-title">
            <strong id="block-customer-login-heading"
                    role="heading"
                    aria-level="2"
                    data-bind="i18n: 'Checkout using your account'"></strong>
        </h4>
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <!-- ko foreach: getRegion('before') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!-- /ko -->
        <div class="block-content" aria-labelledby="block-customer-login-heading">
            <form class="form form-login"
                  method="post"
                  data-bind="event: {submit: login }"
                  id="login-form">
                <div class="fieldset login" data-bind="attr: {'data-hasrequired': $t('* Required Fields')}">
                    <div class="field email required"> 
                        <div class="control">
                            <input name="username"
                                   id="customer-email"
                                   type="email"
                                   placeholder="Adresse email *"
                                   class="input-text"
                                   data-bind="attr: {autocomplete: autocomplete}"
                                   data-validate="{required:true, 'validate-email':true}">
                        </div>
                    </div>
                    <div class="field password required"> 
                        <div class="control">
                            <input name="password"
                                   type="password"
                                   class="input-text"
                                   id="pass"
                                   placeholder="Mot de passe *"
                                   data-bind="attr: {autocomplete: autocomplete}"
                                   data-validate="{required:true}">
                        </div>
                    </div>
                    <!-- ko foreach: getRegion('additional-login-form-fields') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!-- /ko -->
                    <div class="actions-toolbar">
                        <input name="context" type="hidden" value="checkout" />
                        <div class="primary">
                            <button type="submit" class="action action-login secondary btn btn-8h" name="send" id="send2">
                                <span data-bind="i18n: 'Sign In'"></span>
                            </button>
                        </div>
                        <div class="secondary">
                            <a class="action" data-bind="attr: {href: forgotPasswordUrl}">
                                <span data-bind="i18n: 'Forgot Your Password?'"></span>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
