<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Credit memo for your %store_name order" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var comment|escape|nl2br":"Credit Memo Comment",
"var creditmemo.increment_id":"Credit Memo Id",
"layout handle=\"sales_email_order_creditmemo_items\" creditmemo=$creditmemo order=$order":"Credit Memo Items Grid",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var order_data.customer_name":"Customer Name",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var store.frontend_name":"Store Frontend Name",
"var store_email":"Store Email",
"var store_phone":"Store Phone",
"var store_hours":"Store Hours",
"var creditmemo":"Credit Memo",
"var order":"Order",
"var order_id": "Order DB Id",
"var creditmemo_id": "Credit Memo DB Id",
"var order_data.is_not_virtual":"Order Type"
} @-->
{{template config_path="design/email/header_template"}}
<div class="lf_container">

    <table>
        <tr class="email-intro">
            <td>
                <p><b>Voici votre avoir<br><br></b></p>
                <p class="greeting">{{trans "%name," name=$order_data.customer_name}}</p>
            </td>
        </tr>
        <tr class="email-summary">
            <td>
                 <div class="half left">
                    <p>
                        {{trans "Vous trouverez ci-contre votre avoir de commande N°%order_id" creditmemo_id=$creditmemo.increment_id order_id=$order.increment_id}}
                        <br><br>
                        {{if franchise.email}}
                            {{trans 'N’hésitez pas à nous joindre par <a href="mailto:%store_email">mail</a>' store_email=$franchise.email |raw}} {{trans ' ou par  <a href="tel:%store_phone">téléphone</a>  si vous avez la moindre question.' store_phone=$franchise.phone |raw}}
                        {{else}}
                            {{trans 'N’hésitez pas à nous joindre par <a href="mailto:%store_email">mail</a>' store_email=$store_email |raw}} {{trans ' ou par  <a href="tel:%store_phone">téléphone</a>  si vous avez la moindre question.' store_phone=$store_phone |raw}}
                        {{/if}}

                        <br><br>
                    </p>
                 </div>
            </td>
        </tr>
    </table>
</div>
{{template config_path="design/email/footer_template"}}
