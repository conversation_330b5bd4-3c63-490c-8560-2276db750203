<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Invoice for your %store_name order" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order_data.customer_name":"Guest Customer Name",
"var comment|escape|nl2br":"Invoice Comment",
"var invoice.increment_id":"Invoice Id",
"layout handle=\"sales_email_order_invoice_items\" invoice=$invoice order=$order":"Invoice Items Grid",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var invoice": "Invoice",
"var order": "Order",
"var order_id": "Order DB Id",
"var invoice_id": "Invoice DB Id",
"var order_data.is_not_virtual": "Order Type"
} @-->
{{template config_path="design/email/header_template"}}
<div class="lf_container">

    <table>
        <tr class="email-intro">
            <td>
                <p class="greeting">{{trans "%name," name=$order_data.customer_name}}</p>
                <p>
                    {{trans "Thank you for your order from %store_name." store_name=$store.frontend_name}}
                    {{if franchise.email}}
                    {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$franchise.email |raw}}{{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$franchise.phone |raw}}.
                    {{else}}
                    {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}} {{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$store_phone |raw}}.
                    {{/if}}
                    {{depend store_hours}}
                    {{trans 'Our hours are <span class="no-link">%store_hours</span>.' store_hours=$store_hours |raw}}
                    {{/depend}}
                </p>
            </td>
        </tr>
        <tr class="email-summary">
            <td>
                <h1>{{trans "Your Invoice #%invoice_id for Order #%order_id" invoice_id=$invoice.increment_id order_id=$order.increment_id}}</h1>
            </td>
        </tr>
        <tr class="email-information">
            <td>
                {{depend comment}}
                <table class="message-info">
                    <tr>
                        <td>
                            {{var comment|escape|nl2br}}
                        </td>
                    </tr>
                </table>
                {{/depend}}
                <table class="order-details">
                    <tr>
                        <td class="address-details">
                            <h3>{{trans "Billing Info"}}</h3>
                            <p>{{var formattedBillingAddress|raw}}</p>
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="address-details">
                            <h3>{{trans "Shipping Info"}}</h3>
                            <p>{{var formattedShippingAddress|raw}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                    <tr>
                        <td class="method-info">
                            <h3>{{trans "Payment Method"}}</h3>
                            {{var payment_html|raw}}
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="method-info">
                            <h3>{{trans "Shipping Method"}}</h3>
                            <p>{{var order.getShippingDescription()}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                </table>
                {{layout handle="sales_email_order_invoice_items" invoice_id=$invoice_id order_id=$order_id}}
            </td>
        </tr>
    </table>
</div>
{{template config_path="design/email/footer_template"}}
