<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php $_order = $block->getOrder() ?>
<?php if ($_order): ?>
    <?php $_items = $_order->getAllItems(); ?>
    <table class="email-items" style="width: 100%;">
        <thead>
            <tr>
                <th class="item-info" style="text-align:left">
                    <?= /* @escapeNotVerified */  __('Items') ?>
                </th>
                <th class="item-qty" style="text-align:center">
                    <?= /* @escapeNotVerified */  __('Qty') ?>
                </th>
                <th class="item-price" style="text-align:right">
                    <?= /* @escapeNotVerified */  __('Price') ?>
                </th>
            </tr>
        </thead>
        <?php foreach ($_items as $_item): ?>
            <?php if (!$_item->getParentItem()) : ?>
                <tbody>
                    <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot class="order-totals">
            <?= $block->getChildHtml('order_totals') ?>
        </tfoot>
    </table>
    <?php if ($this->helper('Magento\GiftMessage\Helper\Message')->isMessagesAllowed('order', $_order, $_order->getStore()) && $_order->getGiftMessageId()): ?>
        <?php $_giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessage($_order->getGiftMessageId()); ?>
        <?php if ($_giftMessage): ?>
            <br />
            <table class="message-gift">
                <tr>
                    <td>
                        <h3><?= /* @escapeNotVerified */  __('Gift Message for this Order') ?></h3>
                        <strong><?= /* @escapeNotVerified */  __('From:') ?></strong> <?= $block->escapeHtml($_giftMessage->getSender()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('To:') ?></strong> <?= $block->escapeHtml($_giftMessage->getRecipient()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('Message:') ?></strong>
                        <br /><?= $block->escapeHtml($_giftMessage->getMessage()) ?>
                    </td>
                </tr>
            </table>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>
