<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Your %store_name order confirmation" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order_data.email_customer_note|escape|nl2br":"Email Order Note",
"var order.increment_id":"Order Id",
"layout handle=\"sales_email_order_items\" order=$order area=\"frontend\"":"Order Items Grid",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var shipping_msg":"Shipping message",
"var created_at_formatted":"Order Created At (datetime)",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var order_data.is_not_virtual":"Order Type",
"var order":"Order",
"var order_id": "Order DB Id",
"var order_data.customer_name":"Customer Name",
"var shipping_date":"ShippingDate",
"var timeslot":"Timeslot"
} @-->

{{template config_path="design/email/header_template"}}
<div class="lf_container"> 
    <table style="width: 100%;max-width: 700px;">
        <tr class="email-intro">
            <td>
                <p><b>Votre commande est confirmée, merci !<br><br></b></p>    
                <p class="greeting"> 
                {{trans "Bonjour %customer_name," customer_name=$order_data.customer_name}}</p>
                <p>
                    Merci pour votre commande sur LA FAMILLE !
                </p>
            </td>
        </tr>
        <tr class="email-summary">
            <td>
                <h1>{{trans 'Your Order <span class="no-link">#%increment_id</span>' increment_id=$order.increment_id |raw}}</h1>
                <p>{{trans 'Placed on <span class="no-link">%created_at</span>' created_at=$created_at_formatted |raw}}</p>
            </td>
        </tr>
        <tr class="email-information">
            <td>
                {{depend order_data.email_customer_note}}
                <table class="message-info">
                    <tr>
                        <td>
                            {{var order_data.email_customer_note|escape|nl2br}}
                        </td>
                    </tr>
                </table> 
                {{/depend}}
                <table class="order-details" style="width: 100%;">
                   {{depend order_data.is_not_virtual}}
                    <tr> 
                        <td class="method-info" colspan="2">
                            <h3>{{trans "Shipping Method"}}</h3>
                            <p>{{var order.shipping_description}}</p>
                            <p>Date de livraison : {{var shipping_date}}</p>
                            <p>Créneau livraison : {{var timeslot}}</p>
                            {{if shipping_msg}}
                            <p>{{var shipping_msg}}</p>
                            {{/if}}
                        </td> 
                    </tr>    
                    {{/depend}}    
                    <tr>
                        <td class="address-details">
                            <h3><nobr>Adresse de facturation</nobr></h3>
                            <p>{{var formattedBillingAddress|raw}}</p>
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="address-details">
                            <h3><nobr>Adresse de livraison</nobr></h3>
                            <p>{{var formattedShippingAddress|raw}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                     <tr>
                        <td colspan="2"><br/>
                            {{layout handle="sales_email_order_items" order_id=$order_id area="frontend"}}
                        </td>    
                    </tr> 
                    <tr>
                        <td class="method-info" colspan="2">
                            <h3>{{trans "Payment Method"}}</h3>
                            {{var payment_html|raw}}
                        </td> 
                    </tr>
                </table>
                
            </td>
        </tr>
    </table>
</div>
{{template config_path="design/email/footer_template"}}
