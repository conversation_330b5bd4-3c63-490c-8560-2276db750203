<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Your %store_name order has shipped" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var order_data.customer_name":"Customer Name",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var comment|escape|nl2br":"Shipment Comment",
"var shipment.increment_id":"Shipment Id",
"layout handle=\"sales_email_order_shipment_items\" shipment=$shipment order=$order":"Shipment Items Grid",
"block class='Magento\\\\Framework\\\\View\\\\Element\\\\Template' area='frontend' template='Magento_Sales::email\/shipment\/track.phtml' shipment=$shipment order=$order":"Shipment Track Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var order_data.is_not_virtual": "Order Type",
"var shipment": "Shipment",
"var order": "Order",
"var order_id": "Order DB Id",
"var shipment_id": "Shipment DB Id"
} @-->
{{template config_path="design/email/header_template"}}
<div class="lf_container">

    <table>
        <tr class="email-intro">
            <td>
                <p class="greeting">{{trans "%name," name=$order_data.customer_name}}</p>
                <p>
                    {{trans "Thank you for your order from %store_name." store_name=$store.frontend_name}}
                    {{trans 'You can check the status of your order by <a href="%account_url">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
                    {{if franchise.email}}
                        {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$franchise.email |raw}} {{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$franchise.phone |raw}}.
                    {{else}}
                        {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}} {{trans 'or call us at <a href="tel:%store_phone">%store_phone</a>' store_phone=$store_phone |raw}}.
                    {{/if}}
                    {{depend store_hours}}
                        {{trans 'Our hours are <span class="no-link">%store_hours</span>.' store_hours=$store_hours |raw}}
                    {{/depend}}
                </p>
            </td>
        </tr>
        <tr class="email-summary">
            <td>
                <p>{{trans "Your shipping confirmation is below. Thank you again for your business."}}</p>

                <h1>{{trans "Your Shipment #%shipment_id for Order #%order_id" shipment_id=$shipment.increment_id order_id=$order.increment_id}}</h1>
            </td>
        </tr>
        <tr class="email-information">
            <td>
                {{depend comment}}
                <table class="message-info">
                    <tr>
                        <td>
                            {{var comment|escape|nl2br}}
                        </td>
                    </tr>
                </table>
                {{/depend}}
                {{layout handle="sales_email_order_shipment_track" shipment_id=$shipment_id order_id=$order_id}}
                <table class="order-details">
                    <tr>
                        <td class="address-details">
                            <h3>{{trans "Billing Info"}}</h3>
                            <p>{{var formattedBillingAddress|raw}}</p>
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="address-details">
                            <h3>{{trans "Shipping Info"}}</h3>
                            <p>{{var formattedShippingAddress|raw}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                    <tr>
                        <td class="method-info">
                            <h3>{{trans "Payment Method"}}</h3>
                            {{var payment_html|raw}}
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="method-info">
                            <h3>{{trans "Shipping Method"}}</h3>
                            <p>{{var order.shipping_description}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                </table>
                {{layout handle="sales_email_order_shipment_items" shipment_id=$shipment_id order_id=$order_id}}
            </td>
        </tr>
    </table>
</div>
{{template config_path="design/email/footer_template"}}
