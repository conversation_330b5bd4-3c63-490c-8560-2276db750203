<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php $_order = $block->getOrder() ?>
<div class="actions-toolbar">
    <a href="<?= /* @escapeNotVerified */ $block->getPrintAllCreditmemosUrl($_order) ?>"
       onclick="this.target='_blank'"
       class="action print">
    </a>
</div>
<?php foreach ($_order->getCreditmemosCollection() as $_creditmemo): ?>
<div class="order-title">
    <strong><?= /* @escapeNotVerified */ __('Refund #') ?><?= /* @escapeNotVerified */ $_creditmemo->getIncrementId() ?> </strong>
    <a href="<?= /* @escapeNotVerified */ $block->getPrintCreditmemoUrl($_creditmemo) ?>"
       onclick="this.target='_blank'"
       class="action print">
        <span><?= /* @escapeNotVerified */ __('Print Refund') ?></span>
    </a>
</div>

<div class="table-wrapper order-items-creditmemo">
    <table class="data table table-order-items creditmemo" id="my-refund-table-<?= /* @escapeNotVerified */ $_creditmemo->getId() ?>">
        <caption class="table-caption"><?= /* @escapeNotVerified */ __('Items Refunded') ?></caption>
        <thead>
            <tr>
                <th class="col name"><?= /* @escapeNotVerified */ __('Product Name') ?></th>
                <th class="col sku"><?= /* @escapeNotVerified */ __('SKU') ?></th>
                <th class="col price"><?= /* @escapeNotVerified */ __('Price') ?></th>
                <th class="col qty"><?= /* @escapeNotVerified */ __('Qty') ?></th>
                <th class="col subtotal"><?= /* @escapeNotVerified */ __('Subtotal') ?></th>
                <th class="col discount"><?= /* @escapeNotVerified */ __('Discount Amount') ?></th>
                <th class="col total"><?= /* @escapeNotVerified */ __('Row Total') ?></th>
            </tr>
        </thead>
        <?php $_items = $_creditmemo->getAllItems(); ?>
        <?php foreach ($_items as $_item): ?>
            <?php if (!$_item->getOrderItem()->getParentItem()): ?>
                <tbody>
                    <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot>
            <?= $block->getTotalsHtml($_creditmemo) ?>
        </tfoot>
    </table>
</div>
<?= $block->getCommentsHtml($_creditmemo) ?>
<?php endforeach; ?>
