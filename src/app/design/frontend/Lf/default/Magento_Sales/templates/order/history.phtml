<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

use Magento\Sales\Model\Order; ?>
<?php $_orders = $block->getOrders(); ?>
<?php $_dateHelper = $block->getDateHelper(); ?>
<?= $block->getChildHtml('info') ?>
<?php if ($_orders && count($_orders)): ?>
    <div class="table-wrapper orders-history">
        <table class="datatable data table table-order-items history" id="my-orders-table">
            <caption class="table-caption"><?= /* @escapeNotVerified */ __('Orders') ?></caption>
            <thead>
                <tr>
                    <th scope="col" class="col id"><?= /* @escapeNotVerified */ __('Order #') ?></th>
                    <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Order Date') ?></th>
                    <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Shipping Date') ?></th>
                    <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Timeslot') ?></th>
                    <?= /* @noEscape */ $block->getChildHtml('extra.column.header') ?>
                    <th scope="col" class="col shipping"><?= /* @escapeNotVerified */ __('Ship To') ?></th>
                    <th scope="col" class="col total"><?= /* @escapeNotVerified */ __('Order Total') ?></th>
                    <th scope="col" class="col status"><?= /* @escapeNotVerified */ __('Status') ?></th>
                    <th scope="col" class="col status"><?= /* @escapeNotVerified */ __('Invoice') ?></th>
                    <th scope="col" class="col actions"><?= /* @escapeNotVerified */ __('Action') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($_orders as $_order): ?>
                <?php /** @var Order $_order */ ?>
                <?php
                $timeslotId = $_order->getTimeslotId();
                $timeslot="";

                if(is_numeric($timeslotId)){

                    $timeslot = $_dateHelper->formatTimeslot($_order->getTimeslotStartHour()) . " - " . $_dateHelper->formatTimeslot($_order->getTimeslotEndHour());
                }
                else
                {
                    if($timeslotId=='express')
                    {
                        $timeslot='Livraison Express';
                    }
                    else
                    {
                        $timeslot='Retrait boutique';
                    }
                }
                ?>
                    <tr>
                        <td data-th="<?= $block->escapeHtml(__('Order #')) ?>" class="col id important"><?= /* @escapeNotVerified */ $_order->getRealOrderId() ?></td>
                        <td data-th="<?= $block->escapeHtml(__('Order Date')) ?>" class="col date"><?= /* @escapeNotVerified */ $block->formatDate($_order->getCreatedAt()) ?></td>
                        <td data-th="<?= $block->escapeHtml(__('Shipping Date')) ?>" class="col date"><?= /* @escapeNotVerified */ $block->formatDate($_order->getShippingDate()) ?></td>
                        <td data-th="<?= $block->escapeHtml(__('Timeslot')) ?>" class="col date"><?= /* @escapeNotVerified */ $timeslot ?></td>
                        <?php $extra = $block->getChildBlock('extra.container'); ?>
                        <?php if ($extra): ?>
                            <?php $extra->setOrder($_order); ?>
                            <?= /* @noEscape */ $extra->getChildHtml() ?>
                        <?php endif; ?>
                        <td data-th="<?= $block->escapeHtml(__('Ship To')) ?>" class="col shipping"><?= $_order->getShippingAddress() ? $block->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                        <td data-th="<?= $block->escapeHtml(__('Order Total')) ?>" class="col total important"><?= /* @escapeNotVerified */ $_order->getOrderCurrency()->formatPrecision($_order->getGrandTotal(), 2, [], false, false); ?></td>
                        <td data-th="<?= $block->escapeHtml(__('Status')) ?>" class="col status"><?= /* @escapeNotVerified */ $_order->getStatusLabel() ?></td>
                        <?php if ($_order->hasInvoices()): ?>
                            <td data-th="<?= $block->escapeHtml(__('Invoice')) ?>">
                                <a href="<?= $block->getPrintInvoiceUrl($_order->getInvoiceCollection()->getFirstItem()) ?>" class="order">
                                    <span><?= __('Download') ?></span>
                                </a>
                            </td>
                        <?php else: ?>
                            <td data-th="<?= $block->escapeHtml(__('Invoice')) ?>" class="col status">&nbsp;</td>
                        <?php endif; ?>
                        <td data-th="<?= $block->escapeHtml(__('Actions')) ?>" class="col actions">
                            <a href="<?= /* @escapeNotVerified */ $block->getViewUrl($_order) ?>" class="action view">
                                <span><?= /* @escapeNotVerified */ __('View Order') ?></span>
                            </a>
                            <?php if ($this->helper('Magento\Sales\Helper\Reorder')->canReorder($_order->getEntityId())) : ?>
                                <a href="#" data-post='<?php /* @escapeNotVerified */ echo
                                $this->helper(\Magento\Framework\Data\Helper\PostHelper::class)
                                    ->getPostData($block->getReorderUrl($_order))
                                ?>' class="action order">
                                    <span><?= /* @escapeNotVerified */ __('Reorder') ?></span>
                                </a>
                            <?php endif ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php if ($block->getPagerHtml()): ?>
        <div class="order-products-toolbar toolbar bottom"><?= $block->getPagerHtml() ?></div>
    <?php endif ?>
<?php else: ?>
    <div class="message info empty"><span><?= /* @escapeNotVerified */ __('You have placed no orders.') ?></span></div>
<?php endif ?>
