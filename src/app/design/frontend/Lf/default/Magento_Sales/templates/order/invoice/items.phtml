<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php $_order = $block->getOrder() ?>
<?php foreach ($_order->getInvoiceCollection() as $_invoice): ?>
<div class="order-title">
    <strong><?= /* @escapeNotVerified */ __('Invoice #') ?><?= /* @escapeNotVerified */ $_invoice->getIncrementId() ?></strong>
    <a href="<?= /* @escapeNotVerified */ $block->getPrintInvoiceUrl($_invoice) ?>"
       onclick="this.target='_blank'"
       class="action print">
        <span><?= /* @escapeNotVerified */ __('Print Invoice') ?></span>
    </a>
</div>
<div class="table-wrapper table-order-items invoice">
    <table class="data table table-order-items invoice" id="my-invoice-table-<?= /* @escapeNotVerified */ $_invoice->getId() ?>">
        <caption class="table-caption"><?= /* @escapeNotVerified */ __('Items Invoiced') ?></caption>
        <thead>
            <tr>
                <th class="col name"><?= /* @escapeNotVerified */ __('Product Name') ?></th>
                <th class="col sku"><?= /* @escapeNotVerified */ __('SKU') ?></th>
                <th class="col price"><?= /* @escapeNotVerified */ __('Price') ?></th>
                <th class="col qty"><?= /* @escapeNotVerified */ __('Qty Invoiced') ?></th>
                <th class="col subtotal"><?= /* @escapeNotVerified */ __('Subtotal') ?></th>
            </tr>
        </thead>
        <?php $_items = $_invoice->getAllItems(); ?>
        <?php foreach ($_items as $_item): ?>
            <?php if (!$_item->getOrderItem()->getParentItem()) : ?>
                <tbody>
                    <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot>
            <?= $block->getInvoiceTotalsHtml($_invoice) ?>
        </tfoot>
    </table>
</div>
<?= $block->getInvoiceCommentsHtml($_invoice) ?>
<?php endforeach; ?>
