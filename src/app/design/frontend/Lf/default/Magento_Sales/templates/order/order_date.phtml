<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>

<div class="order-date">
    <?= /* @escapeNotVerified */ __('<span class="label">Order Date:</span> %1', '<date>' . $block->formatDate($block->getOrder()->getCreatedAt(), \IntlDateFormatter::LONG) . '</date>') ?>
</div>

<?php if($block->getShippingData()): ?>
<div class="order-shipping-date">
    <?= /* @escapeNotVerified */ __('<span class="label">Shipping Order Date:</span> %1', '<date>' . $block->formatDate($block->getShippingData()->getShippingDate(), \IntlDateFormatter::LONG) . '</date>') ?> - <?= $block->getTimeslot(); ?>
</div>
<?php endif; ?>