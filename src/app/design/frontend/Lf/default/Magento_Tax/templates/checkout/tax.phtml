<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * @var $block \Magento\Tax\Block\Checkout\Tax
 * @see \Magento\Tax\Block\Checkout\Tax
 */
?>
<?php
    $_value = $block->getTotal()->getValue();
    $_style = $block->getTotal()->getStyle();
?>
<?php global $taxIter; $taxIter++; ?>

<?php
    $attributes = 'class="totals-tax"';
    if ($this->helper('Magento\Tax\Helper\Data')->displayFullSummary() && $_value != 0) {
        $attributes = 'class="totals-tax-summary" data-mage-init=\'{"toggleAdvanced": {"selectorsToggleClass": "shown", "baseToggleClass": "expanded", "toggleContainers": ".totals-tax-details"}}\'';
    }
?>

<tr <?= /* @escapeNotVerified */ $attributes ?>>
    <th style="<?= /* @escapeNotVerified */ $_style ?>" class="mark" colspan="<?= /* @escapeNotVerified */ $block->getColspan() ?>" scope="row">
        TVA
    </th>
    <td style="<?= /* @escapeNotVerified */ $_style ?>" class="amount" data-th="<?= $block->escapeHtml($block->getTotal()->getTitle()) ?>">
        <?= /* @escapeNotVerified */ $this->helper('Magento\Checkout\Helper\Data')->formatPrice($_value) ?>
    </td>
</tr>

<?php if ($this->helper('Magento\Tax\Helper\Data')->displayFullSummary() && $_value != 0): ?>
    <?php $isTop = 1; ?>
    <?php foreach ($block->getTotal()->getFullInfo() as $info): ?>
        <?php if (isset($info['hidden']) && $info['hidden']) {
            continue;
        } ?>
        <?php $percent = $info['percent']; ?>
        <?php $amount = $info['amount']; ?>
        <?php $rates = $info['rates']; ?>
        <?php $isFirst = 1; ?>

        <?php foreach ($rates as $rate): ?>
            <tr class="totals-tax-details details-<?= /* @escapeNotVerified */ $taxIter ?>">
                <th class="mark" style="<?= /* @escapeNotVerified */ $_style ?>" colspan="<?= /* @escapeNotVerified */ $block->getColspan() ?>" scope="row">
                    <?= $block->escapeHtml($rate['title']) ?>
                    <?php if (!is_null($rate['percent'])): ?>
                        (<?= (float)$rate['percent'] ?>%)
                    <?php endif; ?>
                </th>
                <?php if ($isFirst): ?>
                    <td style="<?= /* @escapeNotVerified */ $_style ?>" class="amount" rowspan="<?= count($rates) ?>"
                       data-th="<?= $block->escapeHtml($rate['title']) ?><?php if (!is_null($rate['percent'])): ?>(<?= (float)$rate['percent'] ?>%)<?php endif; ?>">
                        <?= /* @escapeNotVerified */ $this->helper('Magento\Checkout\Helper\Data')->formatPrice($amount) ?>
                    </td>
                <?php endif; ?>
            </tr>
            <?php $isFirst = 0; ?>
            <?php $isTop = 0; ?>
        <?php endforeach; ?>
    <?php endforeach; ?>
<?php endif;?>
