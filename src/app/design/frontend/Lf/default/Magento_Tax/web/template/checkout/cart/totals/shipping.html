<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: isCalculated() && quoteIsVirtual == 0 -->
<!-- ko if: isBothPricesDisplayed() -->
<tr class="totals shipping excl">
    <th class="mark" scope="row">
        <span class="label" data-bind="text: title + ' ' + excludingTaxMessage"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <div class="value" data-bind="text: getShippingMethodTitle()"></div>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getExcludingValue(), attr: {'data-th': excludingTaxMessage}"></span> <sup>HT</sup>
    </td>
</tr>
<tr class="totals shipping incl">
    <th class="mark" scope="row">
        <span class="label" data-bind="text: title + ' ' + includingTaxMessage"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <div class="value" data-bind="text: getShippingMethodTitle()"></div>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getExcludingValue(), attr: {'data-th': title + ' ' + excludingTaxMessage}"></span>
        <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: isIncludingDisplayed() -->
<tr class="totals shipping incl">
    <th class="mark" scope="row">
        <span class="label" data-bind="i18n: title"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <div class="value" data-bind="text: getShippingMethodTitle()"></div>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getExcludingValue(), attr: {'data-th': title}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: isExcludingDisplayed() -->
<tr class="totals shipping excl">
    <th class="mark" scope="row">
        <span class="label" data-bind="i18n: title"></span>
        <!-- ko if: haveToShowCoupon() -->
        <span class="label description" data-bind="text: getCouponDescription()"></span>
        <!-- /ko -->
        <div class="value" data-bind="text: getShippingMethodTitle()"></div>
    </th>
    <td class="amount">
            <span class="price"
                  data-bind="text: getValue(), attr: {'data-th': title}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- /ko -->
