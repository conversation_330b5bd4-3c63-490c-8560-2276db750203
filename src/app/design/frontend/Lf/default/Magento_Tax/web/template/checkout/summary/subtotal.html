<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: isBothPricesDisplayed() -->
<tr class="totals sub excl">
    <th class="mark" scope="row">
        <span data-bind="i18n: title"></span>
        <span data-bind="i18n: excludingTaxMessage"></span>
    </th>
    <td class="amount">
        <span class="price" data-bind="text: getValue(), attr: {'data-th': excludingTaxMessage}"></span> <sup>HT</sup>
    </td>
</tr>
<tr class="totals sub incl">
    <th class="mark" scope="row">
        <span data-bind="i18n: title"></span>
        <span data-bind="i18n: includingTaxMessage"></span>
    </th>
    <td class="amount">
        <span class="price" data-bind="text: getValue(), attr: {'data-th': includingTaxMessage}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: !isBothPricesDisplayed() && isIncludingTaxDisplayed() -->
<tr class="totals sub">
    <th data-bind="i18n: title" class="mark" scope="row"></th>
    <td class="amount">
        <span class="price" data-bind="text: getValue(), attr: {'data-th': title}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: !isBothPricesDisplayed() && !isIncludingTaxDisplayed() -->
<tr class="totals sub">
    <th data-bind="i18n: title" class="mark" scope="row"></th>
    <td class="amount">
        <span class="price" data-bind="text: getValue(), attr: {'data-th': title}"></span> <sup>HT</sup>
    </td>
</tr>
<!-- /ko -->
