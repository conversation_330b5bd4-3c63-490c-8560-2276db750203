<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * Pager template
 *
 * @var \Magento\Theme\Block\Html\Pager $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \Magento\Framework\Locale\LocaleFormatter $localeFormatter
 */

?>
<?php if ($block->getCollection()->getSize()): ?>

    <?php if ($block->getUseContainer()): ?>
    <div class="pager">
    <?php endif ?>

        <?php if ($block->getShowAmounts()): ?>
        <p class="toolbar-amount">
            <span class="toolbar-number">
            <?php if ($block->getLastPageNum()>1): ?>
                <?= $block->escapeHtml(
                    __(
                        'Items %1 to %2 of %3 total',
                        $localeFormatter->formatNumber($block->getFirstNum()),
                        $localeFormatter->formatNumber($block->getLastNum()),
                        $localeFormatter->formatNumber($block->getTotalNum())
                    )
                ) ?>
            <?php elseif ($block->getTotalNum() == 1): ?>
                <?= $block->escapeHtml(__('%1 Item', $localeFormatter->formatNumber($block->getTotalNum()))) ?>
            <?php else: ?>
                <?= $block->escapeHtml(__('%1 Item(s)', $localeFormatter->formatNumber($block->getTotalNum()))) ?>
            <?php endif; ?>
            </span>
        </p>
        <?php endif ?>

        <?php if ($block->getLastPageNum()>1): ?>
        <div class="pages">
            <strong class="label pages-label" id="paging-label"><?= $block->escapeHtml(__('Page')) ?></strong>
            <ul class="items pages-items" aria-labelledby="paging-label">
                <li class="item pages-item-previous <?= $block->isFirstPage() ? 'hidden' : '' ?>">
                    <?php $text = $block->getAnchorTextForPrevious() ? $block->getAnchorTextForPrevious() : '';?>
                    <a class="<?= $block->escapeHtmlAttr($text ? 'link ' : 'action ') ?> previous"
                       href="<?= $block->escapeUrl($block->getPreviousPageUrl()) ?>"
                       title="<?= $block->escapeHtmlAttr($text ? $text : __('Previous')) ?>">
                        <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                        <span><?= $block->escapeHtml($text ? $text : __('Page précédente')) ?></span>
                    </a>
                </li>

            <?php if ($block->canShowFirst()): ?>
                <li class="item">
                    <a class="page first" href="<?= $block->escapeUrl($block->getFirstPageUrl()) ?>">
                        <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                        <span><?= $block->escapeHtml($localeFormatter->formatNumber(1)) ?></span>
                    </a>
                </li>
            <?php endif;?>

            <?php if ($block->canShowPreviousJump()): ?>
                <li class="item">
                    <a class="page previous jump"
                       title=""
                       aria-label="<?= $escaper->escapeHtmlAttr(__(
                           'Skip to page %1',
                           $localeFormatter->formatNumber($block->getPreviousJumpPage())
                       )) ?>"
                       href="<?= $block->escapeUrl($block->getPreviousJumpUrl()) ?>">
                        <span>...</span>
                    </a>
                </li>
            <?php endif;?>

            <?php foreach ($block->getFramePages() as $_page): ?>
                <?php if ($block->isPageCurrent($_page)): ?>
                    <li class="item current">
                        <strong class="page">
                            <span class="label"><?= $block->escapeHtml(__('You\'re currently reading page')) ?></span>
                            <span><?= $block->escapeHtml($localeFormatter->formatNumber($_page)) ?></span>
                        </strong>
                    </li>
                <?php else: ?>
                    <li class="item">
                        <a href="<?= $block->escapeUrl($block->getPageUrl($_page)) ?>" class="page">
                            <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                            <span><?= $block->escapeHtml($localeFormatter->formatNumber($_page)) ?></span>
                        </a>
                    </li>
                <?php endif;?>
            <?php endforeach;?>

            <?php if ($block->canShowNextJump()): ?>
                <li class="item">
                    <a class="page next jump"
                       title=""
                       aria-label="<?= $escaper->escapeHtmlAttr(__(
                           'Skip to page %1',
                           $localeFormatter->formatNumber($block->getNextJumpPage())
                       )) ?>"
                       href="<?= $block->escapeUrl($block->getNextJumpUrl()) ?>">
                        <span>...</span>
                    </a>
                </li>
            <?php endif;?>

            <?php if ($block->canShowLast()): ?>
              <li class="item">
                  <a class="page last" href="<?= $block->escapeUrl($block->getLastPageUrl()) ?>">
                      <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                      <span><?= $block->escapeHtml($localeFormatter->formatNumber($block->getLastPageNum())) ?></span>
                  </a>
              </li>
            <?php endif;?>

                <li class="item pages-item-next <?= $block->isLastPage() ? 'hidden' : '' ?>">
                    <?php $text = $block->getAnchorTextForNext() ? $block->getAnchorTextForNext() : '';?>
                    <a class="<?= /* @noEscape */ $text ? 'link ' : 'action ' ?> next"
                       href="<?= $block->escapeUrl($block->getNextPageUrl()) ?>"
                       title="<?= $block->escapeHtmlAttr($text ? $text : __('Next')) ?>">
                        <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                        <span><?= $block->escapeHtml($text ? $text : __('Page suivante')) ?></span>
                    </a>
                </li>
            </ul>
        </div>
        <?php endif; ?>

    <?php if ($block->isShowPerPage()): ?>
        <div class="limiter">
            <strong class="limiter-label"><?= $block->escapeHtml(__('Show')) ?></strong>
            <select id="limiter" data-mage-init='{"redirectUrl": {"event":"change"}}' class="limiter-options">
                <?php foreach ($block->getAvailableLimit() as $_key => $_limit): ?>
                    <option value="<?= $block->escapeUrl($block->getLimitUrl($_key)) ?>"
                        <?php if ($block->isLimitCurrent($_key)): ?>
                        selected="selected"<?php endif ?>>
                        <?= $block->escapeHtml($localeFormatter->formatNumber((int) $_limit)) ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <span class="limiter-text"><?= $block->escapeHtml(__('per page')) ?></span>
        </div>
    <?php endif ?>

    <?php if ($block->getUseContainer()): ?>
    </div>
    <?php endif ?>

<?php endif ?>
