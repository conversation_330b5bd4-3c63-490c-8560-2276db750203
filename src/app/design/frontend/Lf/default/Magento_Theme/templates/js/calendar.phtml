<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>
<?php
/**
 * Calendar localization script. Should be put into page header.
 *
 * @see \Magento\Framework\View\Element\Html\Calendar
 */
?>

<script>
    require([
        "jquery",
        "moment"
    ], function($, moment){

//<![CDATA[
        $.extend(true, $, {
            calendarConfig: {
                dayNames: <?php /* @escapeNotVerified */ echo $days['wide']?>,
                dayNamesMin: <?php /* @escapeNotVerified */ echo $days['abbreviated']?>,
                monthNames: <?php /* @escapeNotVerified */ echo $months['wide']?>,
                monthNamesShort: <?php /* @escapeNotVerified */ echo $months['abbreviated']?>,
                infoTitle: "<?php /* @escapeNotVerified */ echo __('About the calendar');?>",
                closeText: "<?php /* @escapeNotVerified */ echo __('Close');?>",
                currentText: "<?php /* @escapeNotVerified */ echo __('Go Today'); ?>",
                prevText: "<?php /* @escapeNotVerified */ echo __('Previous');?>",
                nextText: "<?php /* @escapeNotVerified */ echo __('Next');?>",
                weekHeader: "<?php /* @escapeNotVerified */ echo __('WK'); ?>",
                timeText: "<?php /* @escapeNotVerified */ echo __('Time');?>",
                hourText: "<?php /* @escapeNotVerified */ echo __('Hour');?>",
                minuteText: "<?php /* @escapeNotVerified */ echo __('Minute');?>"
            }
        });

        moment.locale('fr', {
            months : <?php /* @escapeNotVerified */ echo $months['wide']?>,
            monthsShort : <?php /* @escapeNotVerified */ echo $months['abbreviated']?>,
            monthsParseExact : true,
            weekdays : <?php /* @escapeNotVerified */ echo $days['wide']?>,
            weekdaysShort : <?php /* @escapeNotVerified */ echo $days['abbreviated']?>,
            weekdaysMin : <?php /* @escapeNotVerified */ echo $days['abbreviated']?>,
            weekdaysParseExact : true,
            dayOfMonthOrdinalParse : /\d{1,2}(er|e)/,
            ordinal : function (number) {
                return number + (number === 1 ? 'er' : 'e');
            },
            meridiemParse : /PD|MD/,
            isPM : function (input) {
                return input.charAt(0) === 'M';
            },
            // In case the meridiem units are not separated around 12, then implement
            // this function (look at locale/id.js for an example).
            // meridiemHour : function (hour, meridiem) {
            //     return /* 0-23 hour, given meridiem token and hour 1-12 */ ;
            // },
            meridiem : function (hours, minutes, isLower) {
                return hours < 12 ? 'PD' : 'MD';
            },
            week : {
                dow : 1, // Monday is the first day of the week.
                doy : 4  // The week that contains Jan 4th is the first week of the year.
            }
        });
//]]>

    });
</script>
