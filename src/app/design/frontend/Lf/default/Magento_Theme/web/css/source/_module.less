// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Theme variables
//  _____________________________________________

//  Messages
@message-global-note__background: @color-yellow-light2;
@message-global-note__border-color: @color-yellow-light3;
@message-global-note__color: @text__color;

@message-global-note-link__color: @link__color;
@message-global-note-link__color-hover: @link__hover__color;
@message-global-note-link__color-active: @link__active__color;

@message-global-caution__background: @color-red9;
@message-global-caution__border-color: none;
@message-global-caution__color: @color-white;

@message-global-caution-link__color: @link__color;
@message-global-caution-link__color-hover: @link__hover__color;
@message-global-caution-link__color-active: @link__active__color;

//  Header
@header__background-color: false;
@header-icons-color: @color-gray56;
@header-icons-color-hover: @color-gray20;

@addto-color: @text__color__muted;
@addto-hover-color: @primary__color;

@minicart-icons-color: @header-icons-color;
@minicart-icons-color-hover: @header-icons-color-hover;

@button__shadow: inset 0 2px 1px rgba(0,0,0,.12);

@h1__margin-bottom__desktop: @indent__xl;

//  Footer
@footer__background-color: false;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

  body {
    .lib-css(background-color, @page__background-color);
  }

  //
  //  Header
  //  ---------------------------------------------

  .page-header {
    .lib-css(background-color, @header__background-color);
    border-bottom: 1px solid @border-color__base;
    margin-bottom: @indent__base;
  }

  .header {
    &.content {
      &:extend(.abs-add-clearfix all);
      padding-top: @indent__s;
      position: relative;
    }
  }

  .page-main {
    > .page-title-wrapper {
      .page-title + .action {
        margin-top: @indent__l;
      }
    }
  }

  .action.skip {
    &:not(:focus) {
      &:extend(.abs-visually-hidden all);
    }

    &:focus {
      .lib-css(background, @color-gray94);
      .lib-css(padding, @indent__s);
      box-sizing: border-box;
      left: 0;
      position: absolute;
      text-align: center;
      top: 0;
      width: 100%;
      z-index: 15;
    }
  }

  .action-skip-wrapper {
    height: 0;
    position: relative;
  }

  //
  //  Global notice
  //  ---------------------------------------------
  .message.global {
    p {
      margin: 0;
    }

    &.noscript,
    &.cookie {
      .lib-message(@_message-type: global-note);
      margin: 0;
    }

    &.cookie {
      bottom: 0;
      left: 0;
      position: fixed;
      right: 0;
      z-index: 5;
      background:#F7F7F7;
      border-top:1px solid #CCC;
      strong {
        display: block;
      }
      .actions {
        margin-top: @indent__s;
      }
    }

    &.demo {
      .lib-message(@_message-type: global-caution);
      margin-bottom: 0;
      text-align: center;
    }
  }

  //
  //  Footer
  //  ---------------------------------------------

  .page-footer {
    .lib-css(background-color, @footer__background-color);
    margin-top: auto;
  }

  .footer {
    &.content {
      border-top: 1px solid @border-color__base;
      margin-top: 25px;
      padding-bottom: 25px;
      padding-top: 25px;

      ul {
        &:extend(.abs-reset-list all);
      }

      .links {
        > li {
          margin: 0 0 8px;
        }
      }

      .switcher-store {
        margin: 0 0 30px;
      }
    }

    .copyright,
    .bugs {
      display: block;
      margin: 20px 0 0;
    }
  }

  .page-header,
  .page-footer {
    .switcher {
      margin-right: 10px;

      .options {
        .lib-dropdown(
          @_dropdown-actions-padding: 0,
          @_dropdown-list-item-padding: 0,
          @_dropdown-toggle-icon-content: @icon-down,
          @_dropdown-toggle-active-icon-content: @icon-up,
          @_icon-font-text-hide: true,
          @_icon-font-size: 22px,
          @_icon-font-line-height: 22px,
          @_dropdown-list-min-width: 160px
        );

        ul.dropdown {
          a {
            display: block;
            padding: 8px;

            &:hover {
              text-decoration: none;
            }
          }
        }
      }

      li {
        font-size: @font-size__s;
        margin: 0;
      }

      .label {
        &:extend(.abs-visually-hidden all);
      }

      strong {
        font-weight: @font-weight__regular;
      }
    }
  }

  //
  //  Widgets
  //  ---------------------------------------------

  .sidebar {
    .widget.block:not(:last-child),
    .widget:not(:last-child) {
      &:extend(.abs-margin-for-blocks-and-widgets all);
    }
  }

  .widget {
    clear: both;

    .block-title {
      &:extend(.abs-block-title all);
    }
  }

  .page-header,
  .page-footer {
    .widget.block {
      .lib-css(margin, @indent__base 0);
    }
  }

  .no-display {
    &:extend(.abs-no-display all);
  }

  //
  //  Calendar
  //  ---------------------------------------------

  .ui-datepicker {
    td {
      padding: 0;
    }
  }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

  html,
  body {
    height: 100%; // Stretch screen area for sticky footer
  }

  .page-wrapper {
    .lib-vendor-prefix-display(flex);
    .lib-vendor-prefix-flex-direction(column);
    min-height: 100%; // Stretch content area for sticky footer

    > .breadcrumbs,
    > .top-container,
    > .widget {
      box-sizing: border-box;
      width: 100%;
    } 
     
  }

  .navigation ul {
    padding: 0 8px;
  }

  .header {
    &.panel {
      > .header.links {
        float: right;
        font-size: 0;
        .lib-list-inline();
        margin-left: auto;

        > li {
          font-size: @font-size__base;
          margin: 0 0 0 15px;

          &.welcome,
          a {
            line-height: 1.4;
          }

          &.welcome {
            a {
              .lib-css(padding-left, @indent__xs);
            }
          }
        }
      }
    }

    &.content {
      &:extend(.abs-add-clearfix-desktop all);
      padding: @indent__l @indent__base 0;
    }
  }

  .page-header {
    border: 0;
    margin-bottom: 0;

    .panel.wrapper {
      border-bottom: 1px solid @secondary__color;
    }

    .header.panel {
      &:extend(.abs-add-clearfix-desktop all);
      padding-bottom: @indent__s;
      padding-top: @indent__s;
    }

    .switcher {
      display: inline-block;
    }
  }

  .page-main {
    > .page-title-wrapper {
        .page-title {
          display: inline-block;
          padding:20px; 
        }

        .page-title + .action {
          float: right;
          margin-top: @indent__base;
        }
    }
  }

  footer {
    margin-top:30px;
    background:@lf-blue;
    padding:40px;
    min-height: 180px;
    img  {
      width:80%;
      float:right;
    }
    .bloc {
      width:12%;
      padding-left: 3%;
      padding-right: 3%;
      float:left;
      font-size:13px;
      font-family:'DinProBold';
      letter-spacing: 2px;
      color:white;
      text-transform: uppercase;
      line-height:20px;
      border-right: 1px solid white;
      height: 160px;
      white-space: nowrap;
      &:first-child {
        width:20%;
        margin-left: 2%;
        border:0;
      }
      &:nth-of-type(2) {
        margin-left: -5%; 
      }
        &:nth-of-type(4) {
        width:16%;
      }
      &.last {
        border:0;
      }
      a {
        font-size:13px;
        font-family:'DinProBold';
        color:white;
        text-transform: uppercase;
        display: inline-block;
        &:hover {
          text-decoration:underline;
        }
      }
    }
  }

}


.col.sku {
  color:white;
  text-indent: -10000px;
}