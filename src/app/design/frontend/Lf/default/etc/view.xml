<?xml version="1.0"?>
<view xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Config/etc/view.xsd">
    <media>
        <images module="Magento_Catalog">
            <image id="category_page_list" type="small_image">
                <width>360</width>
                <height>270</height>
                <transparency>false</transparency>
            </image>
            <image id="product_base_image" type="image">
                <width>700</width>
                <height>525</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_label" type="label">
                <width>80</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_filter_1" type="image">
                <width>40</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_filter_2" type="image">
                <width>40</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_filter_3" type="image">
                <width>40</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_filter_4" type="image">
                <width>40</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
            <image id="category_page_list_filter_5" type="image">
                <width>40</width>
                <height>40</height>
                <transparency>true</transparency>
            </image>
        </images>
    </media>
</view>
