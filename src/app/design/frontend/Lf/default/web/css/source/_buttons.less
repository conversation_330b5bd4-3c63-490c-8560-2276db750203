@color_1: inherit;
@color_2: #fff;
@color_3: #0e83cd;
@color_4: #f29e0d;
@color_5: #f58500;
@color_6: #fcad26;
@color_7: #17954c;
@color_8: #703b87;
@color_9: #226fbe;
@color_10: #0a833d;
@color_11: #ea515e;
@color_12: #ffe44d;
@color_13: transparent;
@color_14: #999;
@color_15: white;
@font_family_1: inherit;
@font_family_2: 'icomoon';
@border_color_1: #17954c;

@font-face {
  font-family: 'icomoon';
  src: url('../fonts/icomoon.eot');
  src: url('../fonts/icomoon.eot?#iefix') format('embedded-opentype'), url('../fonts/icomoon.woff') format('woff'), url('../fonts/icomoon.ttf') format('truetype'), url('../fonts/icomoon.svg#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

@-webkit-keyframes fadeOutText {
  0% {
    color: transparent;
  }
  80% {
    color: transparent;
  }
  100% {
    color: #fff;
  }
}

@-moz-keyframes fadeOutText {
  0% {
    color: transparent;
  }
  80% {
    color: transparent;
  }
  100% {
    color: #fff;
  }
}

@keyframes fadeOutText {
  0% {
    color: transparent;
  }
  80% {
    color: transparent;
  }
  100% {
    color: #fff;
  }
}

@-webkit-keyframes moveToRight {
  80% {
    -webkit-transform: translateX(250%);
  }
  81% {
    opacity: 1;
    -webkit-transform: translateX(250%);
  }
  82% {
    opacity: 0;
    -webkit-transform: translateX(250%);
  }
  83% {
    opacity: 0;
    -webkit-transform: translateX(-50%);
  }
  84% {
    opacity: 1;
    -webkit-transform: translateX(-50%);
  }
  100% {
    -webkit-transform: translateX(0%);
  }
}

@-moz-keyframes moveToRight {
  80% {
    -moz-transform: translateX(250%);
  }
  81% {
    opacity: 1;
    -moz-transform: translateX(250%);
  }
  82% {
    opacity: 0;
    -moz-transform: translateX(250%);
  }
  83% {
    opacity: 0;
    -moz-transform: translateX(-50%);
  }
  84% {
    opacity: 1;
    -moz-transform: translateX(-50%);
  }
  100% {
    -moz-transform: translateX(0%);
  }
}

@keyframes moveToRight {
  80% {
    transform: translateX(250%);
  }
  81% {
    opacity: 1;
    transform: translateX(250%);
  }
  82% {
    opacity: 0;
    transform: translateX(250%);
  }
  83% {
    opacity: 0;
    transform: translateX(-50%);
  }
  84% {
    opacity: 1;
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* Button 7b */
@-webkit-keyframes scaleUp {
  80% {
    opacity: 0;
    -webkit-transform: scale(2);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(2);
  }
}

@-moz-keyframes scaleUp {
  80% {
    opacity: 0;
    -moz-transform: scale(2);
  }
  100% {
    opacity: 0;
    -moz-transform: scale(2);
  }
}

@keyframes scaleUp {
  80% {
    opacity: 0;
    transform: scale(2);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

/* Icon only style */
/* Button 7c */
@-webkit-keyframes fillToRight {
  to {
    width: 100%;
  }
}

@-moz-keyframes fillToRight {
  to {
    width: 100%;
  }
}

@keyframes fillToRight {
  to {
    width: 100%;
  }
}

/* Button 7d */
@-webkit-keyframes emptyBottom {
  to {
    height: 100%;
  }
}

@-moz-keyframes emptyBottom {
  to {
    height: 100%;
  }
}

@keyframes emptyBottom {
  to {
    height: 100%;
  }
}

/* Button 7e */
@-webkit-keyframes scaleFade {
  50% {
    opacity: 1;
    -webkit-transform: scale(1);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(2.5);
  }
}

@-moz-keyframes scaleFade {
  50% {
    opacity: 1;
    -moz-transform: scale(1);
  }
  100% {
    opacity: 0;
    -moz-transform: scale(2.5);
  }
}

@keyframes scaleFade {
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(2.5);
  }
}

/* Button 7f */
@-webkit-keyframes dropDown {
  to {
    opacity: 1;
    -webkit-transform: scale(1);
  }
}

@-moz-keyframes dropDown {
  to {
    opacity: 1;
    -moz-transform: scale(1);
  }
}

@keyframes dropDown {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Button 7g */
@-webkit-keyframes dropDownFade {
  50% {
    opacity: 1;
    -webkit-transform: scale(1);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(1.5);
  }
}

@-moz-keyframes dropDownFade {
  50% {
    opacity: 1;
    -moz-transform: scale(1);
  }
  100% {
    opacity: 0;
    -moz-transform: scale(1.5);
  }
}

@keyframes dropDownFade {
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

/* Button 7h */
/* Success and error */
@-webkit-keyframes moveUp {
  0% {
    -webkit-transform: translateY(50%);
    opacity: 0;
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
  }
}

@-moz-keyframes moveUp {
  0% {
    -moz-transform: translateY(50%);
    opacity: 0;
  }
  100% {
    opacity: 1;
    -moz-transform: translateY(0);
  }
}

@keyframes moveUp {
  0% {
    transform: translateY(50%);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* From Dan Eden's animate.css: http://daneden.me/animate/ */
@-webkit-keyframes shake {
  0%, 100% {
    -webkit-transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translateX(10px);
  }
}

@-moz-keyframes shake {
  0%, 100% {
    -moz-transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    -moz-transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    -moz-transform: translateX(10px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@-webkit-keyframes scaleFromUp {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
  }
}

@-moz-keyframes scaleFromUp {
  0% {
    -moz-transform: scale(0);
    opacity: 0;
  }
  100% {
    opacity: 1;
    -moz-transform: scale(1);
  }
}

@keyframes scaleFromUp {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Special trash effect */
/* Button 7i */
@-webkit-keyframes openTrash {
  50% {
    -webkit-transform: rotate(-35deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
  }
}

@-moz-keyframes openTrash {
  50% {
    -moz-transform: rotate(-35deg);
  }
  100% {
    -moz-transform: rotate(0deg);
  }
}

@keyframes openTrash {
  50% {
    transform: rotate(-35deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.btn {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background: none;
  cursor: pointer;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  outline: none;
  position: relative;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

.btn:after {
  content: '';
  position: absolute;
  z-index: -1;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

/* Pseudo elements for icons */
.btn:before,
.icon-heart:after,
.icon-star:after,
.icon-plus:after,
.icon-file:before {
  font-family: 'icomoon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  position: relative;
  -webkit-font-smoothing: antialiased;
}
 
 

/* Button 8f */
.btn-8f {
  -webkit-transform: rotateX(15deg);
  -moz-transform: rotateX(15deg);
  -ms-transform: rotateX(15deg);
  transform: rotateX(15deg);
}

.btn-8f:after {
  width: 100%;
  height: 40%;
  left: 0;
  top: 100%;
  background: #49a7df;
  -webkit-transform-origin: 0% 0%;
  -webkit-transform: rotateX(-90deg);
  -moz-transform-origin: 0% 0%;
  -moz-transform: rotateX(-90deg);
  -ms-transform-origin: 0% 0%;
  -ms-transform: rotateX(-90deg);
  transform-origin: 0% 0%;
  transform: rotateX(-90deg);
}

.btn-8f:active {
  -webkit-transform: rotateX(0deg);
  -moz-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

/* Button 8g */
.btn-8g {
  background: #fff;
  color: white!important;
  font-size: 13px;
}

.btn-8g:active {
  background: #fff;
}

.btn-8h, .btn-8h:active  {
  background: @lf-blue;
  color: white;
  font-size: 15px;
  text-transform:uppercase;
  font-family:'DinProBlack';
  padding:15px;
}

.btn-8g:active {
  background: #fff;
}

.btn-8g:after {
  text-transform: uppercase;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  line-height: 32px;
}

 
 
 
.btn-8g.btn-success3d {
color:transparent!important;
  img {
  display:none;
  }
  &:after {
  color:white!important;
    content:attr(data-content);
  }
}

.btn-8g.btn-error3d  {
  background: #aaa;
  -webkit-transform-origin: 50% 0%;
  -webkit-transform: rotateX(90deg) translateY(-100%);
  -moz-transform-origin: 50% 0%;
  -moz-transform: rotateX(90deg) translateY(-100%);
  -ms-transform-origin: 50% 0%;
  -ms-transform: rotateX(90deg) translateY(-100%);
  transform-origin: 50% 0%;
  transform: rotateX(90deg) translateY(-100%);
}


.back-to-top {
  width: 40px;
  height: 40px;
  position: fixed;
  right: 10px;
  bottom: 10px;
  z-index: 20;
  color: #fff;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  display: block;
  background: white;
  opacity:1;
  border:1px solid @lf-blue;
  border-radius: 20px; 
}


.back-to-top:before {
  content: '';
  border-top: 1px solid @lf-blue;
  border-left: 1px solid @lf-blue;
  transform: rotate(45deg);
  width: 10px;
  height: 10px;
  display: block;
  position: absolute;
  z-index: 21;
  margin: 17px 15px;
}