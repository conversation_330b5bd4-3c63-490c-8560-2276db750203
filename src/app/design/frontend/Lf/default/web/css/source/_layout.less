// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@layout-column-main__sidebar-offset: 2%;
@layout-column__additional-sidebar-offset: @layout-column-main__sidebar-offset;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .columns {
        #lib-layout-columns();

        .column.main {
            &:extend(.abs-add-box-sizing all);
            .lib-css(padding-bottom, @indent__xl);
            .lib-vendor-prefix-flex-basis(100%);
            .lib-vendor-prefix-flex-grow(1);
            .lib-vendor-prefix-order(1);
            width: 100%;
        }

        .sidebar-main {
            &:extend(.abs-add-box-sizing all);
            .lib-vendor-prefix-flex-grow(1);
            .lib-vendor-prefix-flex-basis(100%);
            .lib-vendor-prefix-order(1);
        }

        .sidebar-additional {
            &:extend(.abs-add-box-sizing all);
            .lib-vendor-prefix-flex-grow(1);
            .lib-vendor-prefix-flex-basis(100%);
            .lib-vendor-prefix-order(2);
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
  
    .page-main {
        .account &,
        .cms-privacy-policy & {
            padding-top: 41px;
            position: relative;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .navigation,
    .breadcrumbs,
    .page-header .header.panel,
    .header.content,
    .footer.content,
    .page-wrapper > .widget,
    .page-wrapper > .page-bottom,
    .block.category.event,
    .top-container,
    .page-main {
        box-sizing: border-box;
        margin-left: auto;
        margin-right: auto; 
        width: auto;
    }

    .page-main {
        width: 100%;

        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-flex-shrink(0);
        .lib-vendor-prefix-flex-basis(auto);

        .ie9 & {
            width: auto;
        }
    }

    .columns {
        display: block;
    }

    .column.main {
        #lib-layout-columns > .main();
        &:extend(.abs-add-box-sizing-desktop all);
        min-height: 300px;
    }

    .sidebar-main {
        #lib-layout-columns > .left();
        padding-right: @layout-column-main__sidebar-offset;
        &:extend(.abs-add-box-sizing-desktop all);
    }

    .page-layout-2columns-right .sidebar-main {
        padding-left: @layout-column-main__sidebar-offset;
        padding-right: 0;
    }

    .sidebar-additional {
        #lib-layout-columns > .right();
        clear: right;
        padding-left: @layout-column__additional-sidebar-offset;
        &:extend(.abs-add-box-sizing-desktop all);
    }

    .page-layout-2columns-left {
        .sidebar-additional {
            clear: left;
            float: left;
            padding-left: 1%;
            padding-right: @layout-column__additional-sidebar-offset;
        }
    }

    .panel.header {
        padding: 10px 20px;
    }
}
