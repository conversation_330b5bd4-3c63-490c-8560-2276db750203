//
//  Common
//  _____________________________________________

& when (@media-common = true) {
  .pages {
    .lib-pager();

    .items {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      padding: 0 5px;
    }

    .item {
      &.hidden {
        visibility: hidden;
      }

      &.pages-item-previous {
        margin-right: auto;
      }

      &:first-child:not(.pages-item-previous) {
        margin-left: auto;
      }

      &:last-child:not(.pages-item-next) {
        margin-right: auto;
      }

      &.pages-item-next {
        margin-left: auto;
      }
    }

    .action {
      &:before {
        margin: 0 !important;
        margin-left: -20px !important;
      }

      &.previous {
        margin-right: 12px;
      }

      &.next {
        margin-left: 12px;

        display: flex;

        &:before {
          order: 2;
        }

        span {
          margin-right: 30px;
        }
      }

      span {
        font-family: @font-family-name__header;
        text-decoration: underline;
      }
    }
  }

  .pages a.page,
  .pages strong.page span {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: @lf-font-DinPro;
    font-size: 16px !important;
    height: 48px;
    width: 48px;

    &.jump {
      border: 0;
    }
  }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
  .pages {
    .item {
      &.hidden {
        display: none;
      }
    }
  }
}
