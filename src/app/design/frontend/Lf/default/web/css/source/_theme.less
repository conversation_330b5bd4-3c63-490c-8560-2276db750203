@import '_buttons.less';
@import '_headings.less';

@layout__width-xs-indent: 0;
@layout-indent__width: false;
@layout__max-width: false;

//forms
@form-element-input__background : false;
@form-element-input__padding : 0px;

// Colors
@lf-blue: #003456;
@lf-beige: #f3eae0;
@lf-gold: #bda25a;
@lf-grey: #999;


@screen__xl: 1600px;


// Fonts
@lf-font-DinProBlack: 'DinProBlack';
@lf-font-DinPro: 'DinPro';

.modals-overlay {
    z-index: @modal__z-index - 10 !important;
}

.blue {
  color: @lf-blue !important;
}

.eczar {
  font-family: Eczar, arial;
}

.fs-18 {
  font-size:18px;
}

.grey {
  color: #999;
  #_hour span {
    display: none;
  }
}

.smalltxt {
  font-size: 11px;
}
a, .alink,
a:visited, .alink:visited {
   color: @lf-blue;
}

.page-main {
  padding-left: 0px;
}

.page-title-wrapper {
  text-align:center;
}

.actions-toolbar {
  width:auto!important;
}

.tt {
  position: relative;
  display: block;
  font-family: 'DinPro';

  &:before,
  &:after {
    display: block;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    transform: translate3d(0, -10px, 0);
    transition: all .15s ease-in-out;
    z-index: 2;
  }

  &:after {
    border-right: 6px solid transparent;
    border-bottom: 6px solid rgba(0, 0, 0, .75);
    border-left: 6px solid transparent;
    content: '';
    height: 0;
    top: 47px;
    width: 0;
    right: 15px;
  }

  &:before {
    background: @lf-blue;
    border-radius: 2px;
    color: #fff;
    content: attr(data-text);
    font-size: 14px;
    padding: 6px 10px;
    top: 53px;
    white-space: nowrap;
    right: 0px;
  }

  &:hover:after,
  &:hover:before {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

}

.tooltip {
  border: 1px solid #C8CCD4;
  padding: 7px 10px 10px 10px;
  width: 230px;
  margin-top: -15px;
  position: absolute;
  background: white;
  z-index: 1;

  &:before {
    display: block;
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: white;
    transform: rotate(45deg);
    margin-left: 5px;
    margin-top: -13px;
    border-top: 1px solid #C8CCD4;
    border-left: 1px solid #C8CCD4;
  }

  ul {
    padding: 5px;
    padding-left: 5px;
    padding-left: 15px;
    margin: 0;
  }

  .item {
    color: @lf-blue;
    text-align: left !important;
    list-style: none;
    padding: 3px 3px 5px;
    width: auto;
    border: 0;
    height: 15px;
    line-height: 21px;
    margin-bottom: 1px;
    font-size: 12px;
    width: 202px;
    &[data-content='veggie'] {
      color:#2a8e50;
        &.selectedfilter:after  {
        outline: 5px solid #2a8e50;
      }
    }
    &:hover {
      background:@lf-beige;
    }
    &.selectedfilter:after  {
      outline: 5px solid @lf-blue;
      outline-offset: -9px;

       @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        height: 10px!important;
        width: 10px!important;
      }

    }
    &:after {
      content:'';
      pointer-events: none;
      position: absolute;
      right: 20px;
      border: 1px solid #aaa;
      height: 14px;
      width: 14px;
    }
  }
}

header {
  .container {
    .logo {
      width: 300px;
      transition: all 0.5s ease;
      margin-top: 30px;

      &.fixednoanim {
        position: fixed;
        top: -10px;
        left: 5px;
        z-index: 9;
        transition: all 0.5s ease;
        max-width: ~"calc(50% - 500px)";
        width: 170px;
      }
    }
  }
}


.cntr {
  margin: auto;
}

.btn-radio {
  cursor: pointer;
  display: inline-block;
  float: left;
  -webkit-user-select: none;
  user-select: none;
  margin-left: 50px;
  padding: 5px 5px;
  border-radius: 5px;

  svg {
    fill: none;
    vertical-align: middle;
    margin-right:10px;
    circle {
      stroke-width: 1;
      stroke: #C8CCD4;
    }
    path {
      stroke: @lf-blue;
      &.inner {
        stroke-width: 6;
        stroke-dasharray: 19;
        stroke-dashoffset: 19;
      }
      &.outer {
        stroke-width: 1;
        stroke-dasharray: 57;
        stroke-dashoffset: 57;
      }
    }
  }
  input {
    display: none;
    &:checked + svg {
      path {
        transition: all .4s ease;
        &.inner {
          stroke-dashoffset: 38;
          transition-delay: .1s;
        }
        &.outer {
          stroke-dashoffset: 0;
        }
      }
    }
  }
}
input::placeholder {
  color:#999;
}
input::focus  {
  outline: none;
}
