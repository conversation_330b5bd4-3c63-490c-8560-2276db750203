/* devanagari */
@font-face {
  font-family: 'Eczar';
  font-style: normal;
  font-weight: 400;
  src: local('Eczar Regular'), local('Eczar-Regular'), url(https://fonts.gstatic.com/s/eczar/v6/BXRlvF3Pi-DLmz0kDO5C8A.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF6, U+1CF8-1CF9, U+200C-200D, U+20A8, U+20B9, U+25CC, U+A830-A839, U+A8E0-A8FB;
}
/* latin-ext */
@font-face {
  font-family: 'Eczar';
  font-style: normal;
  font-weight: 400;
  src: local('Eczar Regular'), local('Eczar-Regular'), url(https://fonts.gstatic.com/s/eczar/v6/BXRlvF3Pi-DLmz0rDO5C8A.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Eczar';
  font-style: normal;
  font-weight: 400;
  src: local('Eczar Regular'), local('Eczar-Regular'), url(https://fonts.gstatic.com/s/eczar/v6/BXRlvF3Pi-DLmz0lDO4.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: "DinPro";
  src: url(../fonts/DINPro-Regular.otf) format("otf"), url(../fonts/DINPro-Regular.woff) format("woff"), url(../fonts/DINPro-Regular.ttf) format("ttf");
}
@font-face {
  font-family: "DinProBlack";
  src: url(../fonts/DINPro-Black.otf) format("otf"), url(../fonts/DINPro-Black.woff) format("woff"), url(../fonts/DINPro-Black.ttf) format("ttf");
}
@font-face {
  font-family: "DinProBold";
  src: url(../fonts/DINPro-Bold.otf) format("otf"), url(../fonts/DINPro-Bold.woff) format("woff"), url(../fonts/DINPro-Bold.ttf) format("ttf");
}

hr{
  margin-bottom : 8px;
  margin-top : 8px;
}

a {
  color:@lf-blue;
}