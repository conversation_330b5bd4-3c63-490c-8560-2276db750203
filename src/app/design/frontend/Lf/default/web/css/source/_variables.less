// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Blank theme variables
//  _____________________________________________

//
//  Typography
//  ---------------------------------------------

//  Fonts
@font-family-name__base: 'Open Sans';
@font-family__base: @font-family-name__base, @font-family__sans-serif;

//
//  Sections variables
//  _____________________________________________

//
//  Tabs
//  ---------------------------------------------
@tab-content__border-top-status: true;


//
//  Sidebar
//  ---------------------------------------------

@sidebar__background-color: @color-white-smoke; // Used in cart sidebar, Checkout sidebar, Tier Prices, My account navigation, Rating block background

@screen__ipad: 1000px;
@screen__xlf: 1800px;
@screen__xxl:  2560px;

@pager-current__border: 1px solid @lf-blue;
@pager-current__background: @lf-blue;
@pager-current__color: @color-white;
@pager__border: @pager-current__border;
@pager__line-height: 24px;
@pager__color: @lf-blue;
@pager__visited__color: @pager__color;
@pager__active__color: @pager__color;
@pager__hover__color: @pager__color;
@pager-item__margin: 0 8px 10px 0;
@pager-item__padding: 0;
@pager__font-size: 16px;
@pager__font-weight: @font-weight__bold;
@pager-action__border: false;
@pager-action__color: @pager__color;
@pager-icon__text-hide: false;
