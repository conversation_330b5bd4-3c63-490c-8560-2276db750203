// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Resetes
//  _____________________________________________

//
//  Magento reset
//  ---------------------------------------------

.lib-magento-reset() {
    body {
        margin: 0;
        padding: 0;
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    main,
    footer,
    header,
    nav,
    section,
    summary {
        display: block;
    }

    audio,
    canvas,
    video {
        display: inline-block;
    }

    audio:not([controls]) {
        display: none;
        height: 0;
    }

    nav ul,
    nav ol {
        list-style: none none;
    }

    img {
        border: 0;
    }

    img,
    object,
    video,
    embed { 
        max-width: 100%;
    }

    svg:not(:root) {
        overflow: hidden;
    }

    figure {
        margin: 0;
    }

    .lib-typography-all();

    table {
        .lib-table();
    }

    button {
        .lib-button();
        &::-moz-focus-inner {
            border: 0;
            padding: 0;
        }
    }

    .lib-form-element-all();
    form {
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }

    address {
        font-style: normal;
    }

    * {
        &:focus {
            box-shadow: none!important;
            outline: 0;
        }
    }
 
}

//
//  Normalize
//  ---------------------------------------------

//  ToDo UI: delete with old admin styles
.lib-normalize() {
    /*! normalize.css v3.0.0 | MIT License | git.io/normalize */
    html {
        font-family: sans-serif;
        -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
                font-size-adjust: 100%;
    }
    body {
        margin: 0;
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    main,
    footer,
    header,
    main,
    nav,
    section,
    summary {
        display: block;
    }

    audio,
    canvas,
    progress,
    video {
        display: inline-block;
        vertical-align: baseline;
    }

    audio:not([controls]) {
        display: none;
        height: 0;
    }

    template {
        display: none;
    }

    a {
        background: transparent;
    }

    a:active,
    a:hover {
        outline: 0;
    }

    abbr[title] {
        border-bottom: 1px dotted;
    }

    b,
    strong {
        font-weight: bold;
    }

    dfn {
        font-style: italic;
    }

    h1 {
        font-size: 2em;
        margin: .67em 0;
    }

    mark {
        background: #ff0;
        color: @color-black;
    }

    small {
        font-size: 80%;
    }

    sub,
    sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
    }

    sup {
        top: -.5em;
    }

    sub {
        bottom: -.25em;
    }

    img {
        border: 0;
    }

    svg:not(:root) {
        overflow: hidden;
    }

    figure {
        margin: 1em 40px;
    }

    hr {
        box-sizing: content-box;
        height: 0;
    }

    pre {
        overflow: auto;
    }

    code,
    kbd,
    pre,
    samp {
        font-family: monospace, monospace;
        font-size: 1em;
    }

    button,
    input,
    optgroup,
    select,
    textarea {
        color: inherit;
        font: inherit;
        margin: 0;
    }

    button {
        overflow: visible;
    }

    button,
    select {
        text-transform: none;
    }

    button,
    html input[type="button"],
    input[type="reset"],
    input[type="submit"] {
        -webkit-appearance: button;
        cursor: pointer;
    }

    button[disabled],
    html input[disabled] {
        cursor: default;
    }

    button::-moz-focus-inner,
    input::-moz-focus-inner {
        border: 0;
        padding: 0;
    }

    input {
        line-height: normal;
    }

    input[type="checkbox"],
    input[type="radio"] {
        box-sizing: border-box;
        padding: 0;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        height: auto;
    }

    input[type="search"] {
        box-sizing: content-box;
    }

    input[type="search"]::-webkit-search-cancel-button,
    input[type="search"]::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    fieldset {
        border: 1px solid #c0c0c0;
        margin: 0 2px;
        padding: .35em .625em .75em;
    }

    legend {
        border: 0;
        padding: 0;
    }

    textarea {
        overflow: auto;
    }

    optgroup {
        font-weight: bold;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0;
    }

    td,
    th {
        padding: 0;
    }
}

//
//  Reset
//  ---------------------------------------------

.lib-reset() {
    /* http://meyerweb.com/eric/tools/css/reset/
       v2.0 | 20110126
       License: none (public domain)
    */
    html, body, div, span, applet, object, iframe,
    h1, h2, h3, h4, h5, h6, p, blockquote, pre,
    a, abbr, acronym, address, big, cite, code,
    del, dfn, em, img, ins, kbd, q, s, samp,
    small, strike, strong, sub, sup, tt, var,
    b, u, i, center,
    dl, dt, dd, ol, ul, li,
    fieldset, form, label, legend,
    table, caption, tbody, tfoot, thead, tr, th, td,
    article, aside, canvas, details, embed,
    figure, figcaption, main, footer, header,
    menu, nav, output, ruby, section, summary,
    time, mark, audio, video {
        border: 0;
        font-size: 100%;
        font: inherit;
        margin: 0;
        padding: 0;
        vertical-align: baseline;
    }

    article, aside, details, figcaption, figure, main,
    footer, header, menu, nav, section {
        display: block;
    }

    body {
        line-height: 1;
    }

    ol, ul {
        list-style: none;
    }

    blockquote, q {
        quotes: none;
    }

    blockquote:before, blockquote:after,
    q:before, q:after {
        content: '';
        content: none;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0;
    }
}

//
//  Universal Selector ‘*’ Reset
//  ---------------------------------------------

.lib-universal() {
    * {
        margin: 0;
        padding: 0;
    }
}

//
//  Html5doctor Reset Stylesheet
//  ---------------------------------------------

.lib-html5doctor-reset() {
    /*
        html5doctor.com Reset Stylesheet - http://html5doctor.com/html-5-reset-stylesheet
        v1.6.1
        Last Updated: 2010-09-17
        Author: Richard Clark - http://richclarkdesign.com
        Twitter: @rich_clark
    */
    html, body, div, span, object, iframe,
    h1, h2, h3, h4, h5, h6, p, blockquote, pre,
    abbr, address, cite, code,
    del, dfn, em, img, ins, kbd, q, samp,
    small, strong, sub, sup, var,
    b, i,
    dl, dt, dd, ol, ul, li,
    fieldset, form, label, legend,
    table, caption, tbody, tfoot, thead, tr, th, td,
    article, aside, canvas, details, figcaption, figure, main,
    footer, header, menu, nav, section, summary,
    time, mark, audio, video {
        background: transparent;
        border: 0;
        font-size: 100%;
        margin: 0;
        outline: 0;
        padding: 0;
        vertical-align: baseline;
    }

    body {
        line-height: 1;
    }

    article, aside, details, figcaption, figure, main,
    footer, header, menu, nav, section {
        display: block;
    }

    nav ul {
        list-style: none;
    }

    blockquote, q {
        quotes: none;
    }

    blockquote:before, blockquote:after,
    q:before, q:after {
        content: '';
        content: none;
    }

    a {
        background: transparent;
        font-size: 100%;
        margin: 0;
        padding: 0;
        vertical-align: baseline;
    }

    ins {
        background-color: #ff9;
        color: @color-black;
        text-decoration: none;
    }

    mark {
        background-color: #ff9;
        color: @color-black;
        font-style: italic;
        font-weight: bold;
    }

    del {
        text-decoration: line-through;
    }

    abbr[title], dfn[title] {
        border-bottom: 1px dotted;
        cursor: help;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0;
    }

    hr {
        border: 0;
        border-top: 1px solid #ccc;
        display: block;
        height: 1px;
        margin: 1em 0;
        padding: 0;
    }

    input, select {
        vertical-align: middle;
    }
}

//
//  Global border-box
//  ---------------------------------------------

.lib-set-default-border-box() {
    html {
        box-sizing: border-box;
    }

    *,
    *:before,
    *:after {
        box-sizing: inherit;
    }
}
