var restos = [

  ['LEZENNES - SYNERGIE PARK', '1 Rue Louis Néel', 'Synergie Park', '59260 Le<PERSON>nes','lundi','vendredi','8','15','03 20 05 05 12', '<EMAIL>'],
  ['VILLENEUVE D\'ASCQ - DECATHLON CAMPUS', '4, boulevard de Mons', 'Décathlon Campus', '59665 Villeneuve d\'Ascq','lundi','samedi','8','19','03 20 05 05 12', '<EMAIL>'],
  ['VILLENEUVE D\'ASCQ - MOULINS', '214 rue de la Convention', '', '59650 Villeneuve d\'Ascq','lundi','vendredi','8','15','03 20 05 05 12', '<EMAIL>'],
  ['VILLENEUVE D\'ASCQ - HAUTE BORNE', '5 rue Hergé', 'Pa<PERSON> de <PERSON> Borne', '59650 Villeneuve d\'Ascq','lundi','vendredi','8','15','03 20 05 05 12', '<EMAIL>'],
  ['LILLE - EURATECHNOLOGIES', '166 bis, avenue de Bretagne', '', '59000 Lille','lundi','vendredi','8','15','03 20 05 05 12', '<EMAIL>'],
  ['LA MADELEINE - ROMARIN', '2 avenue Louise', '', '59110 La Madeleine','lundi','vendredi','11','15','03 20 05 05 12', '<EMAIL>'],
  ['WASQUEHAL - CENTRE COMMERCIAL CARREFOUR', 'Avenue du Grand Cottignies', '', '59290 Wasquehal','lundi','vendredi','8','20','03 20 05 05 12', '<EMAIL>'],
  ['Arteparc - Lesquin', '1 rue des Peupliers', '', '59810 Lesquin','lundi','vendredi','8','15','03 20 05 05 12', '<EMAIL>'],
  ['VILLEPINTE - NATIONS', '22 avenue des Nations', '', '93420 Villepinte','lundi','vendredi','8','15','01 48 63 06 06', '<EMAIL>'],
  ['VILLEPINTE - GARE', '93 avenue des Nations', '', '93420 Villepinte','lundi','vendredi','8','15','01 48 63 06 06', '<EMAIL>'],
  ['VILLEPINTE - EPIS', '2 rue des Epis', '', '93420 Villepinte','lundi','vendredi','8','15','01 48 63 06 06', '<EMAIL>'],

  ['PUTEAUX', '67 quai De Dion Bouton', '', '92800 Puteaux','lundi','vendredi','8','15','01 41 02 04 04', '<EMAIL>'],
  ['PARVIS DE LA DEFENSE', '13 place de La Défense', '', '92400 Courbevoie','lundi','vendredi','8','15','01 41 02 04 04', '<EMAIL>'],
  ['LA DEFENSE - VALMY', '43 place de l\'Hémicycle', '', '92000 Nanterre','lundi','vendredi','8','15','01 41 02 04 04', '<EMAIL>'],
  ['PARIS - AVENUE KLEBER', '41 avenue Kleber', '', '75116 Paris','lundi','vendredi','8','15','01 41 02 04 04', '<EMAIL>']

 // ['TREMBLAY EN FRANCE', '12 rue des Chardonnerets', '', '93290 Tremblay en France','Du lundi au vendredi','8','15','01 48 63 06 06', '<EMAIL>']

];





function deferjq() {
    if (window.jQuery) {
        jQuery.each(restos, function(index) {
           var br1=''; if(this[1] != '') { br= '<br>'; }
           var br2=''; if(this[2] != '') { br= '<br>'; }

		   jQuery("<div class='item' id='lfitem"+ index +"'><h3>LA FAMILLE " + this[0] + "</h3> Du " + this[4] + " au " + this[5] + " de " + this[6] + ":00 à " + this[7] + ":00<br>" + br1 + this[1] + "<br>" + this[2] + br2 + this[3] + "<br>Tel " + this[8] + "<br>Email : <a href='mailto:"+ this[9] +"'>"+  this[9] +"</a></div>").appendTo(".restos"); //
		});
		defermap();
    } else {
        setTimeout(function() { deferjq() }, 500);
    }
}
function defermap() {
    if (typeof google !== 'undefined') {

		if(window.location.hash != '' && window.location.hash != '#zones' ) {
	        var zip = window.location.hash.replace('#', '');
	        var zoom = 11;
	     } else {
	     	var zip = '80500';
	     	var zoom = 8;
	     }

	    var geocoder = new google.maps.Geocoder();
	    geocoder.geocode( { 'address': zip}, function(results, status) {
	      if (status == google.maps.GeocoderStatus.OK) {

        	var mapr = new google.maps.Map(document.getElementById('mapr'), {
		        zoom: zoom
		    });

		    var request = {
		        placeId: results[0].place_id
		    };

		    var service = new google.maps.places.PlacesService(mapr);

		    service.getDetails(request, function (place, status) {
		        if (status == google.maps.places.PlacesServiceStatus.OK) {
					 mapr.setCenter(place.geometry.location);
		        }
		    });
				var markers = [];
				var infowindow = new google.maps.InfoWindow();

        if (window.location.hash=="#zones") {
          const kmlFile = "/media/lafamille.xml";
          new geoXML3.parser({afterParse: function (docs) {
              docs[0].placemarks.forEach(function(element) {

                  var _this = element;
                  var polygon = new google.maps.Polygon({
                      path: _this.Polygon[0].outerBoundaryIs[0].coordinates,
                      geodesic: true,
                      strokeOpacity: 1.0,
                      strokeWeight: 0,
                      clickable: false,
                      fillColor: '#999',
                      fillOpacity: 0.35
                  });
                  polygon.setMap(mapr);
              });
          }}).parse(kmlFile);
        }

		    jQuery.each(restos, function(index) {
		    	var address = this[1] + " " + this[3];
					var _this = this;
					var _index = index;
					var timer = index*400;
                    setTimeout(function() {
                        geocoder.geocode({'address': address}, function (results, status) {
                            if (status == google.maps.GeocoderStatus.OK) {
                                var marker = new google.maps.Marker({
                                    map: mapr,
                                    title: 'LA FAMILLE ' + _this[0],
                                    icon: '/static/frontend/Lf/default/fr_FR/images/cms/pointer.png',
                                    position: results[0].geometry.location
                                });
                                marker.addListener('click', function () {
                                    infowindow.close();
                                    infowindow.setContent(jQuery('#lfitem' + _index).html());
                                    infowindow.open(mapr, marker);
                                    for (var j = 0; j < markers.length; j++) {
                                        markers[j].setIcon('/static/frontend/Lf/default/fr_FR/images/cms/pointer.png');
                                    }
                                    marker.setIcon('/static/frontend/Lf/default/fr_FR/images/cms/pointer2.png');
                                });
                                markers.push(marker);
                            } else {
                                console.log("err");
                            }
                        });
                    },timer);
	        });

	      }
	  });


    } else {
        setTimeout(function() { defermap() }, 500);
    }
}

deferjq();
