
--- a/Plugin/Model/RuleRepositoryPlugin.php
+++ b/Plugin/Model/RuleRepositoryPlugin.php
@@ -110,6 +110,7 @@ class RuleRepositoryPlugin
      */
     public function afterSave(RuleRepositoryInterface $subject, RuleInterface $rule)
     {
+        $rule = $subject->getById($rule->getRuleId());
         $extensionAttributes = $rule->getExtensionAttributes();
         $giftRule = $extensionAttributes->getGiftRule();
         $this->giftRuleRepository->save($giftRule);
