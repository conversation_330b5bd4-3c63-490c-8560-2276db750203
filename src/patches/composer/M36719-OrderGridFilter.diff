--- a/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php	2023-03-27 14:24:36.239018275 +0200
+++ b/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php	2023-03-27 14:42:17.911436311 +0200
@@ -51,12 +51,6 @@
                     $condition[$key] = $this->timeZone->convertConfigTimeToUtc($value);
                 }
             }
-
-            $fieldName = $subject->getConnection()->quoteIdentifier($field);
-            $condition = $subject->getConnection()->prepareSqlCondition($fieldName, $condition);
-            $subject->getSelect()->where($condition, null, Select::TYPE_CONDITION);
-
-            return $subject;
         }
 
         return $proceed($field, $condition);

--- a/Model/ResourceModel/Order/Grid/Collection.php	   2022-09-12 16:47:34.000000000 +0200
+++ b/Model/ResourceModel/Order/Grid/Collection.php	   2023-03-27 16:10:37.632687667 +0200
@@ -63,20 +63,4 @@

         return $this;
     }
-
-    /**
-     * @inheritDoc
-     */
-    public function addFieldToFilter($field, $condition = null)
-    {
-        if ($field === 'created_at') {
-            if (is_array($condition)) {
-                foreach ($condition as $key => $value) {
-                    $condition[$key] = $this->timeZone->convertConfigTimeToUtc($value);
-                }
-            }
-        }
-
-        return parent::addFieldToFilter($field, $condition);
-    }
 }
