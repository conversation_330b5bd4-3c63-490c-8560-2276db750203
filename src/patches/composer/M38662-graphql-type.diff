--- a/Model/Customer/GetCustomSelectedOptionAttributes.php
+++ b/Model/Customer/GetCustomSelectedOptionAttributes.php
@@ -55,7 +55,7 @@ public function execute(string $entityType, array $customAttribute): ?array
                 continue;
             }
             $result[] = [
-                'uid' => $this->uid->encode($option->getValue()),
+                'uid' => $this->uid->encode((string) $option->getValue()),
                 'value' => $option->getValue(),
                 'label' => $option->getLabel()
             ];
