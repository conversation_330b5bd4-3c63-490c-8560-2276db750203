--- a/lib/web/jquery/jquery-ui.js	2023-05-12 14:42:18.841954385 +0200
+++ b/lib/web/jquery/jquery-ui.js	2023-05-12 14:45:40.401091352 +0200
@@ -10671,9 +10671,9 @@

 		offset.left -= ( this._get( inst, "isRTL" ) ? ( dpWidth - inputWidth ) : 0 );
 		offset.left -= ( isFixed && offset.left === inst.input.offset().left ) ? $( document ).scrollLeft() : 0;
-		offset.top -= ( isFixed && offset.top === ( inst.input.offset().top + inputHeight ) ) ? $( document ).scrollTop() : 0;
+        offset.top -= ( isFixed && Math.floor(offset.top) === Math.floor( inst.input.offset().top + inputHeight ) ) ? $( document ).scrollTop() : 0;

-		// Now check if datepicker is showing outside window viewport - move to a better place if so.
+        // Now check if datepicker is showing outside window viewport - move to a better place if so.
 		offset.left -= Math.min( offset.left, ( offset.left + dpWidth > viewWidth && viewWidth > dpWidth ) ?
 			Math.abs( offset.left + dpWidth - viewWidth ) : 0 );
 		offset.top -= Math.min( offset.top, ( offset.top + dpHeight > viewHeight && viewHeight > dpHeight ) ?