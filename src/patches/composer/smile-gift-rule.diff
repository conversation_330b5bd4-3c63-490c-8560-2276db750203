--- a/Model/RulesApplier.php	2021-01-15 17:37:59.000000000 +0100
+++ b/Model/RulesApplier.php	2024-10-11 11:22:28.630206006 +0200
@@ -66,7 +66,7 @@
      * {@inheritdoc}
      * @SuppressWarnings(PHPMD.CyclomaticComplexity)
      */
-    public function applyRules($item, $rules, $skipValidation, $couponCode)
+    public function applyRules($item, $rules, $skipValidation, $couponCode = [])
     {
         $address = $item->getAddress();
         $appliedRuleIds = [];
