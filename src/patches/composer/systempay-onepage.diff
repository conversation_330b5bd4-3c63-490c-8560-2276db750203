--- a/ModelStandardConfigProvider.php	2023-01-27 15:43:27.313457826 +0100
+++ b/Model/StandardConfigProvider.php	2023-01-27 15:43:33.530116549 +0100
@@ -78,7 +78,7 @@
     private function getRestFormToken()
     {
         // Do not create payment token until arriving to checkout page.
-        if ($this->urlBuilder->getCurrentUrl() != $this->urlBuilder->getUrl('checkout', ['_secure' => true])) {
+        if ($this->urlBuilder->getCurrentUrl() != $this->urlBuilder->getUrl('onepage', ['_secure' => true])) {
             return false;
         }
 
