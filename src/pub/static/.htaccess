<IfModule mod_php7.c>
    php_flag engine 0
</IfModule>

<IfModule mod_php.c>
    php_flag engine 0
</IfModule>

# To avoid situation when web server automatically adds extension to path
Options -MultiViews

<IfModule mod_rewrite.c>
    RewriteEngine On

    ## you can put here your pub/static folder path relative to web root
    #RewriteBase /magento/pub/static/

    # Remove signature of the static files that is used to overcome the browser cache
    RewriteRule ^version.+?/(.+)$ $1 [L]

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-l

    RewriteRule .* ../static.php?resource=$0 [L]
    # Detects if moxieplayer request with uri params and redirects to uri without params
    <Files moxieplayer.swf>
     	RewriteCond %{QUERY_STRING} !^$
     	RewriteRule ^(.*)$ %{REQUEST_URI}? [R=301,L]
     </Files>
</IfModule>

############################################
## setting MIME types

# JavaScript
AddType application/javascript js jsonp
AddType application/json json

# HTML
AddType text/html html

# CSS
AddType text/css css

# Images and icons
AddType image/x-icon ico
AddType image/gif gif
AddType image/png png
AddType image/jpeg jpg
AddType image/jpeg jpeg
AddType image/webp webp
AddType image/avif avif
AddType image/avif-sequence avifs

# SVG
AddType image/svg+xml svg svgz

# Fonts
AddType application/vnd.ms-fontobject eot
AddType application/x-font-ttf ttf
AddType application/x-font-otf otf
AddType application/x-font-woff woff
AddType application/font-woff2 woff2

# Manifest
AddType application/manifest+json webmanifest

# Archives and exports
AddType application/zip gzip
AddType application/x-gzip gz gzip
AddType application/x-bzip2 bz2
AddType text/csv csv
AddType application/xml xml

<IfModule mod_headers.c>

    <FilesMatch .*\.(ico|jpg|jpeg|png|gif|svg|webp|avif|avifs|js|css|eot|ttf|otf|woff|woff2|html|json|webmanifest)$>
        Header append Cache-Control public
    </FilesMatch>

    <FilesMatch .*\.(zip|gz|gzip|bz2|csv|xml)$>
        Header append Cache-Control no-store
    </FilesMatch>

</IfModule>

<IfModule mod_expires.c>

############################################
## Add default Expires header
## http://developer.yahoo.com/performance/rules.html#expires

    ExpiresActive On

    # Data
    <FilesMatch \.(zip|gz|gzip|bz2|csv|xml)$>
        ExpiresDefault "access plus 0 seconds"
    </FilesMatch>
    ExpiresByType text/xml "access plus 0 seconds"
    ExpiresByType text/csv "access plus 0 seconds"
    ExpiresByType application/json "access plus 0 seconds"
    ExpiresByType application/zip "access plus 0 seconds"
    ExpiresByType application/x-gzip "access plus 0 seconds"
    ExpiresByType application/x-bzip2 "access plus 0 seconds"

    # Manifest
    <FilesMatch \.(webmanifest)$>
        ExpiresDefault "access plus 0 seconds"
    </FilesMatch>
    ExpiresByType application/manifest+json "access plus 0 seconds"

    # CSS, JavaScript, html
    <FilesMatch \.(css|js|html|json)$>
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType text/html "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/json "access plus 1 year"

    # Favicon, images, flash
    <FilesMatch \.(ico|gif|png|jpg|jpeg|svg|svgz|webp|avif|avifs)$>
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/avif "access plus 1 year"
    ExpiresByType image/avif-sequence "access plus 1 year"

    # Fonts
    <FilesMatch \.(eot|ttf|otf|svg|woff|woff2)$>
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"
    ExpiresByType application/x-font-otf "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType font/truetype "access plus 1 year"

</IfModule>
