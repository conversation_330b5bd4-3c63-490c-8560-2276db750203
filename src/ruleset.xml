<?xml version="1.0"?>
<ruleset name="Adin">
    <description>Adin coding standards</description>
    <rule ref="Magento2">
        <exclude name="Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing"/>
        <exclude name="Magento2.Annotation.MethodAnnotationStructure.MethodArguments"/>
        <exclude name="Magento2.Annotation.MethodArguments.NoCommentBlock"/>
        <exclude name="Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation"/>
        <exclude name="Magento2.Annotation.MethodArguments.ParamMissing"/>
    </rule>
</ruleset>
